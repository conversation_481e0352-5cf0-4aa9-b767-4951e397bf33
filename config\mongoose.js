const mongoose = require("mongoose");
const { MONGODB_URL } = require("../environment");

// Database Connection
async function MongoDBConnection(app) {
  console.log(`| MongoDB URL  : ${MONGODB_URL}`);
  await mongoose.connect(MONGODB_URL, {}).then(() => {
    console.log("| MongoDB Connected");
    console.log("|--------------------------------------------");
  });

  return app;
}

const connectMongoDB = (app) => {
  mongoose.Promise = global.Promise;
  Promise.resolve(app)
    .then(MongoDBConnection)
    .catch((err) => console.log({ err }));
};

module.exports = { connectMongoDB };
