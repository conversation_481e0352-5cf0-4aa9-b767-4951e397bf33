const { prisma } = require("../../config/prisma");
const constants = require("../../constants");
const Logger = require("../../utils/logger");
const lodash = require("lodash");
const { WalletService } = require("./wallet.service");
const { ReservationReceipt } = require("../models/ReservationReceipt");

class ReservationReceiptService {
  constructor() {}

  /**
   *
   * @param {object} param0
   * @param {import("@prisma/client").tbl_reservations & {tbl_reservation_types:import("@prisma/client").tbl_reservation_types} & {tbl_vehicles:import("@prisma/client").tbl_vehicles & {tbl_vehicle_types:import("@prisma/client").tbl_vehicle_types}}  & {tbl_users:import("@prisma/client").tbl_users & {tbl_location_offers_map: import("@prisma/client").tbl_location_offers_map}}} param0.reservation
   */
  static generateReservationReceipt = async ({ reservation }) => {
    try {
      Logger.log("info", {
        message: "ReservationReceiptService:generateReservationReceipt:params",
        params: {
          userID: reservation.user_id,
          reservationID: reservation.reservation_id,
        },
      });

      const reservationType = reservation.tbl_reservation_types;

      const newReservationReceipt = new ReservationReceipt({
        reservation_time_charge:
          reservationType.amount / reservationType.reservation_time,
        initial_reservation_time_fare: reservationType.amount,
        final_reservation_time_fare: reservationType.amount,
        total_reservation_time: reservationType.reservation_time,
        tax_rate: 0,
        reservation_start_at: reservation.reservation_start_at,
        reservation_end_at: reservation.reservation_end_at,
        coupon_id: reservation.user_selected_coupon_id,
        plan_id: reservation.user_selected_plan_id,
        subscription_id: reservation.user_selected_subscription_id,
      });

      newReservationReceipt.finalCalculate();

      // extract the data from final reservation receipt
      const { finalCalculate, ...finalReservationReceiptData } =
        newReservationReceipt;

      Logger.log("info", {
        message:
          "ReservationReceiptService:generateReservationReceipt:finalReservationReceiptData",
        params: {
          userID: reservation.user_id,
          reservationID: reservation.reservation_id,
          finalReservationReceiptData,
        },
      });
      // execute all prisma transactions and updates

      await prisma.tbl_reservations.update({
        where: {
          reservation_id: parseInt(reservation.reservation_id),
        },
        data: {
          tbl_reservation_receipts: {
            create: finalReservationReceiptData,
          },
        },
      });
      // deduct final amount from wallet

      await WalletService.payReservationPaymentFromWallet({
        reservationID: reservation.reservation_id,
        userID: reservation.user_id,
      });
      return newReservationReceipt;
    } catch (error) {
      Logger.log("error", {
        message: "ReservationReceiptService:generateReservationReceipt:catch-1",
        params: {
          error,
        },
      });
      return false;
    }
  };
}

module.exports = { ReservationReceiptService };
