var admin = require("firebase-admin");
const Logger = require("../../utils/logger");
const { prisma } = require("../../config/prisma");
const constants = require("../../constants");
const moment = require("moment");

class DeliveryService {
  constructor() {}

  /**
   *
   * @param {object} param0
   * @param {import("@prisma/client").tbl_users} param0.user
   */
  static getUserDeliveryBookings = async ({ user, skip, take }) => {
    try {
      Logger.log("info", {
        message: "DeliveryService:getUserDeliveryBookings:params",
        params: {
          userID: user.user_id,
          skip,
          take,
        },
      });
      const deliveryBookings = await prisma.tbl_delivery_bookings.findMany({
        where: {
          user_id: parseInt(user.user_id),
        },
        include: {
          tbl_delivery_booking_logs: true,
          tbl_vehicles: {
            include: {
              tbl_vehicle_types: true,
            },
          },
          tbl_vehicle_types: true,
        },
        orderBy: {
          delivery_booking_initiation_at: "desc",
        },
        skip: skip,
        take: take,
      });

      Logger.log("success", {
        message: "DeliveryService:getUserDeliveryBookings:success",
        params: {
          userID: user.user_id,
          deliveryBookingsLength: deliveryBookings?.length,
        },
      });
      return deliveryBookings;
    } catch (error) {
      Logger.log("error", {
        message: "DeliveryService:getUserDeliveryBookings:catch-1",
        params: { error },
      });
      throw error;
    }
  };

  /**
   *
   * @param {object} param0
   * @param {import("@prisma/client").tbl_users} param0.user
   * @param {Number} param0.deliveryBookingID
   */
  static getUserDeliveryBookingByID = async ({ user, deliveryBookingID }) => {
    try {
      Logger.log("info", {
        message: "DeliveryService:getUserDeliveryBookingByID:params",
        params: {
          userID: user.user_id,
          deliveryBookingID,
        },
      });
      const deliveryBooking = await prisma.tbl_delivery_bookings.findFirst({
        where: {
          user_id: parseInt(user.user_id),
          delivery_booking_id: parseInt(deliveryBookingID),
        },
        include: {
          tbl_delivery_booking_logs: true,
          tbl_vehicles: {
            include: {
              tbl_vehicle_types: true,
            },
          },
          tbl_vehicle_types: true,
        },
      });

      Logger.log("success", {
        message: "DeliveryService:getUserDeliveryBookingByID:success",
        params: {
          userID: user.user_id,
          deliveryBookingID: deliveryBooking.delivery_booking_id,
        },
      });
      return deliveryBooking;
    } catch (error) {
      Logger.log("error", {
        message: "DeliveryService:getUserDeliveryBookingByID:catch-1",
        params: { error },
      });
      throw error;
    }
  };

  /**
   *
   * @param {object} param0
   * @param {import("@prisma/client").tbl_users} param0.user
   * @param {Number} param0.vehicleTypeID
   * @param {Number} param0.lat
   * @param {Number} param0.lng
   * @param {Number} param0.duration
   */
  static requestDeliveryBooking = async ({
    user,
    vehicleTypeID,
    lat,
    lng,
    duration,
  }) => {
    try {
      Logger.log("info", {
        message: "DeliveryService:requestDeliveryBooking:params",
        params: {
          userID: user.user_id,
          vehicleTypeID: vehicleTypeID,
        },
      });
      const requestDeliveryBookingTransaction = await prisma.$transaction(
        async (tx) => {
          const newDeliveryBooking = await tx.tbl_delivery_bookings.create({
            data: {
              delivery_booking_status:
                constants.DELIVERY_BOOKING_STATUS.REQUESTED,
              vehicle_type_id: parseInt(vehicleTypeID),
              delivery_booking_initiation_at: new Date(),
              user_id: parseInt(user.user_id),
              lat: Number(parseFloat(lat).toFixed(10)),
              lng: Number(parseFloat(lng).toFixed(10)),
              duration: duration,
            },
            include: {
              tbl_vehicle_types: true,
            },
          });
          Logger.log("info", {
            message:
              "DeliveryService:requestDeliveryBooking:$transaction:newDeliveryBooking",
            params: {
              userID: user.user_id,
              deliveryBookingID: newDeliveryBooking.delivery_booking_id,
            },
          });

          const newDeliveryBookingLog =
            await tx.tbl_delivery_booking_logs.create({
              data: {
                delivery_booking_id: newDeliveryBooking.delivery_booking_id,
                delivery_booking_action:
                  constants.DELIVERY_BOOKING_ACTIONS.REQUESTED,
                user_id: parseInt(user.user_id),
              },
            });
          Logger.log("info", {
            message:
              "DeliveryService:requestDeliveryBooking:$transaction:newDeliveryBookingLog",
            params: {
              userID: user.user_id,
              deliveryBookingID: newDeliveryBooking.delivery_booking_id,
              deliveryBookingLogID:
                newDeliveryBookingLog.delivery_booking_log_id,
            },
          });

          return {
            deliveryBooking: newDeliveryBooking,
            deliveryBookingLog: newDeliveryBookingLog,
          };
        }
      );
      Logger.log("success", {
        message: "DeliveryService:requestDeliveryBooking:$transaction:success",
        params: {
          userID: user.user_id,
          vehicleTypeID,
        },
      });
      return requestDeliveryBookingTransaction;
    } catch (error) {
      Logger.log("error", {
        message: "DeliveryService:requestDeliveryBooking:catch-1",
        params: { error },
      });
      throw error;
    }
  };

  // /**
  //  *
  //  * @param {object} param0
  //  * @param {import("@prisma/client").tbl_users} param0.user
  //  * @param {import("@prisma/client").tbl_bookings} param0.ongoingBooking
  //  */
  // static confirmDeliveryBooking = async ({ ongoingBooking, user }) => {
  //   try {
  //     Logger.log("info", {
  //       message: "DeliveryService:confirmDeliveryBooking:params",
  //       params: {
  //         userID: user.user_id,
  //         bookingID: ongoingBooking.booking_id,
  //         vehicleID: ongoingBooking.vehicle_id,
  //       },
  //     });
  //     const confirmDeliveryBookingTransaction = await prisma.$transaction(
  //       async (tx) => {
  //         const updatedVehicle = await tx.tbl_vehicles.update({
  //           where: {
  //             vehicle_id: parseInt(ongoingBooking.vehicle_id),
  //           },
  //           data: {
  //
  //             vehicle_status: constants.VEHICLE_STATUS.RIDING,
  //           },
  //         });
  //         Logger.log("info", {
  //           message:
  //             "DeliveryService:confirmDeliveryBooking:$transaction:updatedVehicle",
  //           params: {
  //             userID: user.user_id,
  //             bookingID: ongoingBooking.booking_id,
  //             vehicleID: updatedVehicle.vehicle_id,
  //             vehicleStatus: updatedVehicle.vehicle_status,
  //           },
  //         });
  //         const newBookingLog = await tx.tbl_booking_logs.create({
  //           data: {
  //             booking_id: ongoingBooking.booking_id,
  //             booking_action: constants.BOOKING_ACTIONS.START,
  //             user_id: parseInt(user.user_id),
  //           },
  //         });
  //         Logger.log("info", {
  //           message:
  //             "DeliveryService:confirmDeliveryBooking:$transaction:newBookingLog",
  //           params: {
  //             userID: user.user_id,
  //             bookingID: ongoingBooking.booking_id,
  //             vehicleID: updatedVehicle.vehicle_id,
  //             bookingLogID: newBookingLog.booking_log_id,
  //           },
  //         });
  //         const updatedBooking = await tx.tbl_bookings.update({
  //           where: {
  //             booking_id: parseInt(ongoingBooking.booking_id),
  //           },
  //           data: {
  //             booking_status: constants.BOOKING_STATUS.RIDING,
  //             booking_start_at: new Date(),
  //           },
  //           include: {
  //             tbl_vehicles: true,
  //             tbl_stations_tbl_bookings_booking_start_station_idTotbl_stations: true,
  //           },
  //         });
  //         Logger.log("info", {
  //           message:
  //             "DeliveryService:confirmDeliveryBooking:$transaction:updatedBooking",
  //           params: {
  //             userID: user.user_id,
  //             bookingID: updatedBooking.booking_id,
  //             vehicleID: updatedVehicle.vehicle_id,
  //             bookingLogID: newBookingLog.booking_log_id,
  //           },
  //         });
  //         return {
  //           booking: updatedBooking,
  //           vehicle: updatedVehicle,
  //           bookingLog: newBookingLog,
  //         };
  //       }
  //     );
  //     Logger.log("success", {
  //       message: "DeliveryService:confirmDeliveryBooking:$transaction:success",
  //       params: {
  //         userID: user.user_id,
  //         bookingID: ongoingBooking.booking_id,
  //         vehicleID: ongoingBooking.vehicle_id,
  //       },
  //     });
  //     return confirmDeliveryBookingTransaction;
  //   } catch (error) {
  //     Logger.log("error", {
  //       message: "DeliveryService:confirmDeliveryBooking:catch-1",
  //       params: { error },
  //     });
  //     throw error;
  //   }
  // };

  // /**
  //  *
  //  * @param {object} param0
  //  * @param {import("@prisma/client").tbl_bookings} param0.ongoingBooking
  //  */
  // static revertStartBooking = async ({ ongoingBooking }) => {
  //   try {
  //     Logger.log("info", {
  //       message: "DeliveryService:revertStartBooking:params",
  //       params: {
  //         bookingID: ongoingBooking.booking_id,
  //         vehicleID: ongoingBooking.vehicle_id,
  //       },
  //     });
  //     const revertStartBookingTransaction = await prisma.$transaction(
  //       async (tx) => {
  //         const updatedVehicle = await tx.tbl_vehicles.update({
  //           where: {
  //             vehicle_id: parseInt(ongoingBooking.vehicle_id),
  //           },
  //           data: {
  //
  //             vehicle_status: constants.VEHICLE_STATUS.READY,
  //           },
  //         });
  //         Logger.log("info", {
  //           message:
  //             "DeliveryService:revertStartBooking:$transaction:updatedVehicle",
  //           params: {
  //             bookingID: ongoingBooking.booking_id,
  //             vehicleID: updatedVehicle.vehicle_id,
  //             vehicleStatus: updatedVehicle.vehicle_status,
  //           },
  //         });

  //         const terminatedBooking = await tx.tbl_bookings.update({
  //           where: {
  //             booking_id: parseInt(ongoingBooking.booking_id),
  //           },
  //           data: {
  //
  //             booking_status: constants.BOOKING_STATUS.TERMINATED,
  //           },
  //           include: {
  //             tbl_users: true,
  //           },
  //         });
  //         Logger.log("info", {
  //           message:
  //             "DeliveryService:revertStartBooking:$transaction:terminatedBooking",
  //           params: {
  //             bookingID: terminatedBooking.booking_id,
  //             vehicleID: updatedVehicle.vehicle_id,
  //           },
  //         });

  //         return {
  //           booking: terminatedBooking,
  //           vehicle: updatedVehicle,
  //         };
  //       }
  //     );
  //     Logger.log("success", {
  //       message: "DeliveryService:revertStartBooking:$transaction:success",
  //       params: {
  //         bookingID: ongoingBooking.booking_id,
  //         vehicleID: ongoingBooking.vehicle_id,
  //       },
  //     });
  //     return revertStartBookingTransaction;
  //   } catch (error) {
  //     Logger.log("error", {
  //       message: "DeliveryService:revertStartBooking:catch-1",
  //       params: { error },
  //     });
  //     throw error;
  //   }
  // };
}

module.exports = { DeliveryService };
