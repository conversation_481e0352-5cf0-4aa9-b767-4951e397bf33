const { prisma } = require("../../../config/prisma");
const { socketIO } = require("../../../config/socket.io");
const constants = require("../../../constants");
const Logger = require("../../../utils/logger");

const paymentOrderSocketController = {};

paymentOrderSocketController.emitPaymentOrderOnUpdate = async ({
  paymentOrder,
  firebaseID,
}) => {
  Logger.log("info", {
    message: "paymentOrderSocketController:emitPaymentOrderOnUpdate",
    params: { firebaseID },
  });
  try {
    socketIO
      .to(firebaseID)
      .emit(
        constants.SOCKET_EVENTS.ON_CURRENT_PAYMENT_ORDER_UPDATE,
        paymentOrder
      );
    Logger.log("success", {
      message: "paymentOrderSocketController:emitPaymentOrderOnUpdate:success",
      params: { firebaseID },
    });
  } catch (error) {
    Logger.log("error", {
      message: "paymentOrderSocketController:emitPaymentOrderOnUpdate:catch-1",
      params: { error },
    });
  }
};

paymentOrderSocketController.emitPaymentOrderByIDOnUpdate = async ({
  paymentOrderID,
  firebaseID,
}) => {
  Logger.log("info", {
    message: "paymentOrderSocketController:emitPaymentOrderByIDOnUpdate",
    params: { firebaseID },
  });
  try {
    Logger.log("info", {
      message:
        "paymentOrderSocketController:emitPaymentOrderByIDOnUpdate:firebaseID",
      params: { firebaseID },
    });

    const paymentOrder = await prisma.tbl_payment_orders.findUnique({
      where: {
        payment_order_id: paymentOrderID,
      },
      include: {
        tbl_payment_transactions: {
          orderBy: {
            created_at: "desc",
          },
        },
      },
    });

    socketIO
      .to(firebaseID)
      .emit(
        constants.SOCKET_EVENTS.ON_CURRENT_PAYMENT_ORDER_UPDATE,
        paymentOrder
      );
    Logger.log("success", {
      message:
        "paymentOrderSocketController:emitPaymentOrderByIDOnUpdate:success",
      params: { firebaseID },
    });
  } catch (error) {
    Logger.log("error", {
      message:
        "paymentOrderSocketController:emitPaymentOrderByIDOnUpdate:catch-1",
      params: { error },
    });
  }
};
module.exports = paymentOrderSocketController;
