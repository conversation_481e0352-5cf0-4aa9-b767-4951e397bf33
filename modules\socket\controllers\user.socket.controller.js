// const { prisma } = require("../../../config/prisma");
const { Socket } = require("socket.io");
const { prisma } = require("../../../config/prisma");
const { socketIO } = require("../../../config/socket.io");
const constants = require("../../../constants");
const Logger = require("../../../utils/logger");
const stationSocketController = require("./station.socket.controller");
const vehicleSocketController = require("./vehicle.socket.controller");
const { UserService } = require("../../services/user.service");
const offerSocketController = require("./offer.socket.controller");

const userSocketController = {};

userSocketController.emitUserOnFirstConnect = async ({ firebaseID }) => {
  Logger.log("info", {
    message: "userSocketController:emitUserOnFirstConnect",
    params: { firebaseID },
  });
  try {
    const user = await prisma.tbl_users.findFirst({
      where: { firebase_id: firebaseID },
      include: {
        tbl_wallets: true,
        tbl_user_types: {
          include: {
            tbl_security_deposit_types: true,
          },
        },
      },
    });

    socketIO.to(firebaseID).emit(constants.SOCKET_EVENTS.ON_USER_UPDATE, user);
    Logger.log("success", {
      message: "userSocketController:emitUserOnFirstConnect:success",
      params: { firebaseID },
    });
  } catch (error) {
    Logger.log("error", {
      message: "userSocketController:emitUserOnFirstConnect:catch-1",
      params: { error },
    });
  }
};

userSocketController.updateUserGeolocation = async ({
  firebaseID,
  latitude,
  longitude,
}) => {
  Logger.log("info", {
    message: "userSocketController:updateUserGeolocation",
    params: { firebaseID, latitude, longitude },
  });
  try {
    const user = await prisma.tbl_users.findFirst({
      where: { firebase_id: firebaseID },
    });
    const updatedUser = await UserService.updateUserLocationData({
      user,
      lat: parseFloat(latitude),
      lng: parseFloat(longitude),
    });
    stationSocketController.emitStationsOnUserGeolocationUpdate({
      user: updatedUser,
      latitude,
      longitude,
    });
    vehicleSocketController.emitVehiclesOnUserGeolocationUpdate({
      user: updatedUser,
      latitude,
      longitude,
    });

    offerSocketController.emitEventOnUserLocationOffersMappingUpdate({
      user: updatedUser,
    });

    Logger.log("success", {
      message: "userSocketController:updateUserGeolocation:success",
      params: { firebaseID, latitude, longitude },
    });
  } catch (error) {
    Logger.log("error", {
      message: "userSocketController:updateUserGeolocation:catch-1",
      params: { error },
    });
  }
};

/**
 *
 * @param {object} param0
 * @param {String} param0.userID
 */
userSocketController.emitUserOnUpdate = async ({ userID }) => {
  Logger.log("info", {
    message: "userSocketController:emitUserOnUpdate",
    params: { userID },
  });
  try {
    const user = await prisma.tbl_users.findFirst({
      where: { user_id: parseInt(userID) },
      include: {
        tbl_wallets: true,
        tbl_user_types: {
          include: {
            tbl_security_deposit_types: true,
          },
        },
      },
    });
    socketIO
      .to(user.firebase_id)
      .emit(constants.SOCKET_EVENTS.ON_USER_UPDATE, user);
    Logger.log("success", {
      message: "userSocketController:emitUserOnUpdate:success",
      params: { userID: user.user_id },
    });
  } catch (error) {
    Logger.log("error", {
      message: "userSocketController:emitUserOnUpdate:catch-1",
      params: { error },
    });
  }
};

/**
 *
 * @param {object} param0
 * @param {Socket} param0.socket
 * @param {String} param0.firebaseID
 */
userSocketController.subscribeUserToRooms = async ({ socket, firebaseID }) => {
  try {
    Logger.log("info", {
      message: "userSocketController:subscribeUserToRooms",
      params: { firebaseID },
    });
    const user = await prisma.tbl_users.findFirst({
      where: { firebase_id: firebaseID },
    });
    if (user) {
      await socket.join([firebaseID]);
      Logger.log("success", {
        message: "userSocketController:subscribeUserToRooms:station & user",
        params: { firebaseID },
      });
    } else {
      await socket.join(firebaseID);
      Logger.log("success", {
        message: "userSocketController:subscribeUserToRooms:user",
        params: { firebaseID },
      });
    }
  } catch (error) {
    Logger.log("error", {
      message: "userSocketController:subscribeUserToRooms:catch-1",
      params: { error },
    });
  }
};

module.exports = userSocketController;
