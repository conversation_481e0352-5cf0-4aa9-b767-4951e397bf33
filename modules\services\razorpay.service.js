const { prisma } = require("../../config/prisma");
const { razorpayInstance } = require("../../config/razorpay");
const constants = require("../../constants");
const environment = require("../../environment");
const Logger = require("../../utils/logger");
const crypto = require("crypto");

class RazorpayService {
  constructor() {}
  static createRazorpayCustomerAccount = async ({
    firebaseID,
    phoneNumber,
    userID,
  }) => {
    try {
      Logger.log("info", {
        message: "RazorpayService:createRazorpayCustomerAccount:params",
        params: { firebaseID, phoneNumber, userID },
      });
      const razorpayCustomer = await razorpayInstance.customers.create({
        name: `user_id-${userID}`,
        contact: phoneNumber,
        fail_existing: 0,
        notes: {
          firebase_id: firebaseID,
        },
      });

      Logger.log("success", {
        message:
          "RazorpayService:createRazorpayCustomerAccount:razorpayCustomer",
        params: { razorpayCustomer },
      });
      return razorpayCustomer;
    } catch (error) {
      Logger.log("error", {
        message: "RazorpayService:createRazorpayCustomerAccount:catch-1",
        params: { error },
      });
      throw error;
    }
  };

  /**
   *
   * @param {object} param0
   * @param {Number} param0.userID
   * @param {String} param0.razorpayCustomerID
   * @param {Number} param0.amount
   * @param {String} param0.paymentPurpose
   * @param {Number} param0.paymentOrderReceiptID
   * @param {Number} param0.interWalletTransactionID
   * @param {any} param0.note
   * @returns
   */
  static createRazorpayOrder = async ({
    userID,
    razorpayCustomerID,
    amount,
    paymentPurpose,
    paymentOrderReceiptID,
    interWalletTransactionID,
    note,
  }) => {
    try {
      Logger.log("info", {
        message: "RazorpayService:createRazorpayOrder:params",
        params: {
          userID,
          razorpayCustomerID,
          amount,
          paymentPurpose,
          paymentOrderReceiptID,
          interWalletTransactionID,
        },
      });

      const razorpayOrder = await razorpayInstance.orders.create({
        amount: Math.floor(amount),
        currency: "INR",
        payment_capture: true,
        receipt: String(paymentOrderReceiptID),
        notes: {
          user_id: userID,
          razorpay_customer_id: razorpayCustomerID,
          payment_purpose: paymentPurpose,
          inter_wallet_transaction_id: interWalletTransactionID,
          ...note,
        },
      });

      const paymentOrder = await prisma.tbl_payment_orders.create({
        data: {
          user_id: parseInt(userID),
          amount: Math.floor(amount),
          payment_order_id: razorpayOrder.id,
          payment_order_status: constants.RAZORPAY_ORDER_STATUS.CREATED,
          payment_purpose_receipt_id: String(paymentOrderReceiptID),
          payment_purpose: paymentPurpose,
          inter_wallet_transaction_id: interWalletTransactionID,
        },
      });

      Logger.log("success", {
        message: "RazorpayService:createRazorpayOrder:razorpayCustomer",
        params: { razorpayOrder, paymentOrder },
      });
      return paymentOrder;
    } catch (error) {
      Logger.log("error", {
        message: "RazorpayService:createRazorpayOrder:catch-1",
        params: { error },
      });
      throw error;
    }
  };
}

module.exports = { RazorpayService };
