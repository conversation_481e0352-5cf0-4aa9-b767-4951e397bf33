class ReservationReceipt {
  constructor({
    reservation_receipt_id,
    reservation_time_charge,
    total_reservation_time,
    tax,
    other_charge,
    minimum_charge_addition,
    created_at,
    updated_at,
    initial_reservation_time_fare,
    final_reservation_time_fare,
    final_reservation_fare,
    reservation_start_at,
    reservation_end_at,
    surge_charge,
    tax_rate,
    inter_wallet_transaction_id,
  }) {
    this.reservation_receipt_id = reservation_receipt_id;
    this.reservation_time_charge = reservation_time_charge;
    this.total_reservation_time = total_reservation_time; // in microseconds

    this.tax = tax;
    this.tax_rate = tax_rate;
    this.other_charge = other_charge ? other_charge : 0;
    this.minimum_charge_addition = minimum_charge_addition
      ? minimum_charge_addition
      : 0;
    this.surge_charge = surge_charge ? surge_charge : 0;
    this.initial_reservation_time_fare = initial_reservation_time_fare;
    this.final_reservation_time_fare = final_reservation_time_fare;
    this.final_reservation_fare = final_reservation_fare;

    this.reservation_start_at = reservation_start_at;
    this.reservation_end_at = reservation_end_at;

    this.created_at = created_at;
    this.updated_at = updated_at;

    this.inter_wallet_transaction_id = inter_wallet_transaction_id;
  }

  finalCalculate = () => {
    const fareExcludingTaxes =
      parseInt(this.final_reservation_time_fare) +
      parseInt(this.other_charge) +
      parseInt(this.minimum_charge_addition) +
      parseInt(this.surge_charge);
    // this.tax = Math.floor((fareExcludingTaxes * this.tax_rate) / 100);
    this.tax = 0;
    this.final_reservation_fare = Math.floor(this.tax + fareExcludingTaxes);
  };
}

module.exports = { ReservationReceipt };
