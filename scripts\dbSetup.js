const environment = require("../environment");
const { prisma } = require("../config/prisma");
const constants = require("../constants");

const _sync_station = async () => {
  await prisma.tbl_stations.createMany({
    data: [
      {
        station_id: constants.OUTSTATION_ID,
        address: "Outstation",
        lat: 28.59519551,
        lng: 77.34702015,
      },
      {
        address: "Sector 22, Noida",
        lat: 28.59519551,
        lng: 77.34702015,
      },
      {
        address: "Sector 23, Noida",
        lat: 28.59268805,
        lng: 77.35236311,
      },
      {
        address: "Sector 10, Noida",
        lat: 28.59030787,
        lng: 77.33180237,
      },
      {
        address: "New Ashok Nagar, Delhi",
        lat: 28.59489053,
        lng: 77.31195831,
      },
    ],
  });
};

const _sync_coupon_types = async () => {
  await prisma.tbl_coupon_types.createMany({
    data: [
      {
        title: "Ride at ₹2 per min",
        description: "Ride time charge replacement from 2.7 units to 2 units",
        booking_receipt_target_field: "ride_time_charge",
        applicability_order: constants.COUPON_APPLICABILITY_ORDER.END,
        rule_action: constants.COUPON_RULE_ACTIONS.REPLACE,
        rule_action_value: 2,
      },
      {
        title: "Locking charge at ₹1 per min",
        description: "Pause time charge replacement to 1 units",
        booking_receipt_target_field: "pause_time_charge",
        applicability_order: constants.COUPON_APPLICABILITY_ORDER.END,
        rule_action: constants.COUPON_RULE_ACTIONS.REPLACE,
        rule_action_value: 1,
      },
      {
        title: "Ride is free for 30 mins",
        description: "Ride time reduction by 30 mins",
        booking_receipt_target_field: "total_ride_time",
        applicability_order: constants.COUPON_APPLICABILITY_ORDER.END,
        rule_action: constants.COUPON_RULE_ACTIONS.SUBSTRACT,
        rule_action_limiting_value: 30,
        rule_action_limiting_value_action:
          constants.COUPON_RULE_ACTION_LIMITING_VALUE_ACTIONS.ADD,
      },
      {
        title: "Locking charge at ₹0.8 per min",
        description: "Pause time charge replacement to 0.8 units",
        booking_receipt_target_field: "pause_time_charge",
        applicability_order: constants.COUPON_APPLICABILITY_ORDER.END,
        rule_action: constants.COUPON_RULE_ACTIONS.REPLACE,
        rule_action_value: 0.8,
      },
      {
        title: "Ride is free for 60 mins",
        description: "Ride time reduction by 60 mins",
        booking_receipt_target_field: "total_ride_time",
        applicability_order: constants.COUPON_APPLICABILITY_ORDER.END,
        rule_action: constants.COUPON_RULE_ACTIONS.SUBSTRACT,
        rule_action_limiting_value: 60,
        rule_action_limiting_value_action:
          constants.COUPON_RULE_ACTION_LIMITING_VALUE_ACTIONS.ADD,
      },
      {
        title: "Ride is free for 4 hours",
        description: "Ride time reduction by 240 mins",
        booking_receipt_target_field: "total_ride_time",
        applicability_order: constants.COUPON_APPLICABILITY_ORDER.END,
        rule_action: constants.COUPON_RULE_ACTIONS.SUBSTRACT,
        rule_action_value: 240,
        rule_action_limiting_value: 0,
        rule_action_limiting_value_action:
          constants.COUPON_RULE_ACTION_LIMITING_VALUE_ACTIONS.ADD,
      },
      {
        title: "Ride is free for 6 hours",
        description: "Ride time reduction by 360 mins",
        booking_receipt_target_field: "total_ride_time",
        applicability_order: constants.COUPON_APPLICABILITY_ORDER.END,
        rule_action: constants.COUPON_RULE_ACTIONS.SUBSTRACT,
        rule_action_value: 360,
        rule_action_limiting_value: 0,
        rule_action_limiting_value_action:
          constants.COUPON_RULE_ACTION_LIMITING_VALUE_ACTIONS.ADD,
      },
      {
        title: "First ride is free for 30 mins",
        description: "Ride time reduction for 30 mins",
        booking_receipt_target_field: "total_ride_time",
        applicability_order: constants.COUPON_APPLICABILITY_ORDER.END,
        usage_limit: 1,
        rule_action: constants.COUPON_RULE_ACTIONS.SUBSTRACT,
        rule_action_value: 30,
        rule_action_limiting_value: 0,
      },
    ],
  });
};

const _sync_subscription_types = async () => {
  await prisma.tbl_subscription_types.createMany({
    data: [
      {
        title: "Flash",
        validity_period: 14400,
        coupon_type_ids: [1, 2],
        amount: 7000,
      },
      {
        title: "Basic",
        validity_period: 14400 * 3,
        coupon_type_ids: [1, 2, 3],
        amount: 20000,
      },
      {
        title: "Pro",
        validity_period: 14400 * 9,
        coupon_type_ids: [4, 5, 8],
        amount: 50000,
      },
    ],
  });
};

const _sync_plan_types = async () => {
  await prisma.tbl_plan_types.createMany({
    data: [
      {
        title: "Mars",
        description: "Book EV for short trips around",
        validity_period: 240,
        coupon_type_ids: [6],
        amount: 17500,
      },
      {
        title: "Saturn",
        description: "Book EV for long trips around",
        validity_period: 360,
        coupon_type_ids: [7],
        amount: 29900,
      },
    ],
  });
};

const _sync_location_offers_map = async () => {
  await prisma.tbl_location_offers_map.createMany({
    data: [
      {
        title: "Ashok Nagar Offers",
        lat_1: 28.60045631,
        lng_1: 77.31005287,
        lat_2: 28.59314151,
        lng_2: 77.31425857,
        lat_3: 28.5871083,
        lng_3: 77.30764961,
        lat_4: 28.59314151,
        lng_4: 77.30318642,
        subscription_type_ids: [1, 2, 3],
        plan_type_ids: [1, 2],
      },
      {
        title: "Sector 23, Noida Offers",
        lat_1: 28.5940432,
        lng_1: 77.35048342,
        lat_2: 28.59630154,
        lng_2: 77.35315704,
        lat_3: 28.59251059,
        lng_3: 77.35716963,
        lat_4: 28.58874897,
        lng_4: 77.35245752,
        subscription_type_ids: [1, 2],
        plan_type_ids: [1],
      },
    ],
  });
};

const _sync_wallets = async () => {
  await prisma.tbl_wallets.createMany({
    data: [
      {
        wallet_id: constants.HOVER_WALLET_ID,
        balance: 0,
      },
    ],
  });
};

const _sync_security_deposits = async () => {
  await prisma.tbl_security_deposit_types.createMany({
    data: [
      {
        amount: 20000,
      },
    ],
  });
};

const _sync_user_types = async () => {
  await prisma.tbl_user_types.createMany({
    data: [
      {
        title: "customer-aa",
        security_deposit_type_id: 1,
        initial_coupon_type_id: 8,
      },
    ],
  });
};

const _sync_vehicle_types = async () => {
  await prisma.tbl_vehicle_types.createMany({
    data: [
      {
        description: "EV scooter",
        category: "EV",
      },
    ],
  });
};

const _sync_vehicles = async () => {
  await prisma.tbl_vehicles.createMany({
    data: [
      {
        vehicle_type_id: 1,
        lat: 28.59519551,
        lng: 77.34702015,
        station_id: 1,
      },
      {
        vehicle_type_id: 1,
        lat: 28.59268805,
        lng: 77.35236311,
        station_id: 2,
      },
      {
        vehicle_type_id: 1,
        lat: 28.59030787,
        lng: 77.33180237,
        station_id: 3,
      },
      {
        vehicle_type_id: 1,
        lat: 28.59489053,
        lng: 77.31195831,
        station_id: 4,
      },
    ],
  });
};

const d = async () => {
  await _sync_station();
  await _sync_coupon_types();
  await _sync_subscription_types();
  await _sync_plan_types();
  await _sync_location_offers_map();
  await _sync_wallets();
  await _sync_security_deposits();
  await _sync_user_types();
  await _sync_vehicle_types();
  await _sync_vehicles();
};

d();
