const BikeStatusEnum = Object.freeze({
  Booked: "Booked",
  Idle: "Idle",
  Locked: "Locked",
  Busy: "Busy",
  Blocked: "Blocked",
  Maintenance: "Maintenance",
});
const PassEnum = Object.freeze({
  Nill: "Nill",
  LSS: "LSS", // Low speed scooters
  HSS: "HSS", // High speed scooters
});
const BookingStatusEnum = Object.freeze({
  Booked: "Booked",
  Cancelled: "Cancelled",
  Auto_Cancelled: "Auto_Cancelled",
  Ended: "Ended",
});
const CommandsEnum = Object.freeze({
  Unlock: "Unlock",
  Lock: "Lock",
  Start: "Start",
  End: "End",
});

exports.BikeStatusEnum = BikeStatusEnum;
exports.BookingStatusEnum = BookingStatusEnum;
exports.CommandsEnum = CommandsEnum;
exports.PassEnum = PassEnum;
