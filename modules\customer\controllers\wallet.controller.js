const { prisma } = require("../../../config/prisma");
const Logger = require("../../../utils/logger");
const constants = require("../../../constants");
const { extractError } = require("../../../utils/error.utils");
const { WalletService } = require("../../services/wallet.service");
const { RazorpayService } = require("../../services/razorpay.service");
const walletController = {};

/**
 *
 * @param {import("express").Request} req
 * @param {import("express").Response} res
 * @returns
 */
walletController.activateWallet = async (req, res) => {
  try {
    const { user } = req;

    const updatedUser = WalletService.activateWallet(user);

    return res.json({ success: true, wallet: updatedUser.tbl_wallets });
  } catch (error) {
    Logger.log("error", {
      message: "walletController:activateWallet:catch-1",
      params: { error },
    });
    return res.json({ success: false, error: extractError(error) });
  }
};

/**
 *
 * @param {import("express").Request} req
 * @param {import("express").Response} res
 * @returns
 */
walletController.paySecurityDeposit = async (req, res) => {
  try {
    const { user } = req;
    Logger.log("info", {
      message: "walletController:paySecurityDeposit:params",
      params: { userID: user.user_id },
    });
    const { securityDepositID, amount } =
      await WalletService.createRequiredSecurityDepositEntry({
        user,
      });
    Logger.log("info", {
      message: "walletController:paySecurityDeposit:razorpay method",
      params: { userID: user.user_id, securityDepositID, amount },
    });
    const paymentOrder = await RazorpayService.createRazorpayOrder({
      amount: parseInt(amount),
      userID: parseInt(user.user_id),
      paymentOrderReceiptID: parseInt(securityDepositID),
      razorpayCustomerID: user.razorpay_customer_id,
      paymentPurpose: constants.PAYMENT_PURPOSE.SECURITY_DEPOSIT,
    });
    Logger.log("success", {
      message: "walletController:paySecurityDeposit:success",
      params: { userID: user.user_id, paymentOrder: paymentOrder },
    });
    return res.json({ success: true, paymentOrder: paymentOrder });
  } catch (error) {
    Logger.log("error", {
      message: "walletController:paySecurityDeposit:catch-1",
      params: { error },
    });
    return res.json({ success: false, error: extractError(error) });
  }
};

/**
 *
 * @param {import("express").Request} req
 * @param {import("express").Response} res
 * @returns
 */
walletController.rechargeWallet = async (req, res) => {
  try {
    const { user } = req;
    const { amount } = req.body;
    Logger.log("info", {
      message: "walletController:rechargeWallet:params",
      params: { userID: user.user_id },
    });
    const { walletRechargeTransactionID } =
      await WalletService.createRequiredWalletRechargeTransactionEntry({
        user,
        paymentSource: constants.PAYMENT_SOURCE.RAZORPAY,
      });

    Logger.log("info", {
      message: "walletController:rechargeWallet:razorpay method",
      params: { userID: user.user_id, amount },
    });
    const paymentOrder = await RazorpayService.createRazorpayOrder({
      amount: parseInt(amount),
      userID: parseInt(user.user_id),
      paymentOrderReceiptID: parseInt(walletRechargeTransactionID),
      razorpayCustomerID: user.razorpay_customer_id,
      paymentPurpose: constants.PAYMENT_PURPOSE.WALLET_RECHARGE,
    });
    Logger.log("success", {
      message: "walletController:rechargeWallet:success",
      params: { userID: user.user_id, paymentOrder: paymentOrder },
    });
    return res.json({ success: true, paymentOrder: paymentOrder });
  } catch (error) {
    Logger.log("error", {
      message: "walletController:rechargeWallet:catch-1",
      params: { error },
    });
    return res.json({ success: false, error: extractError(error) });
  }
};

/**
 *
 * @param {import("express").Request} req
 * @param {import("express").Response} res
 * @returns
 */
walletController.getWallet = async (req, res) => {
  try {
    const { user } = req;
    // const wallet = user.tbl_wallets;
    const wallet = await prisma.tbl_wallets.findFirst({
      where: {
        wallet_id: parseInt(user.wallet_id),
      },
      include: {
        tbl_security_deposit_transactions: {
          where: {
            security_deposit_transaction_status:
              constants.SECURITY_DEPOSIT_STATUS.SUCCESS,
          },
          orderBy: {
            created_at: "desc",
          },
          take: 1,
        },
      },
    });

    Logger.log("info", {
      message: "walletController:getWallet:wallet",
      params: { wallet },
    });
    return res.json({ success: true, wallet });
  } catch (error) {
    Logger.log("error", {
      message: "walletController:getWallet:catch-1",
      params: { error },
    });
    return res.json({
      success: false,
      error: constants.ERROR_CODES.SERVER_ERROR,
    });
  }
};

/**
 *
 * @param {import("express").Request} req
 * @param {import("express").Response} res
 * @returns
 */
walletController.paymentOrderHistory = async (req, res) => {
  try {
    const { user } = req;
    const { page } = req.query;
    let skip = 0;
    let take = constants.ORDERS_PAGE_SIZE;
    if (page) {
      skip = (parseInt(page) - 1) * constants.ORDERS_PAGE_SIZE;
      take = constants.ORDERS_PAGE_SIZE;
    }

    const paymentOrderHistory = await prisma.tbl_payment_orders.findMany({
      where: {
        user_id: parseInt(user.user_id),
      },
      orderBy: {
        created_at: "desc",
      },
      include: {
        tbl_offers: {
          include: {
            tbl_offer_types: true,
          },
        },
      },

      skip: skip,
      take: take,
    });

    return res.json({
      paymentOrderHistory,
      success: true,
      nextPage:
        paymentOrderHistory.length < constants.ORDERS_PAGE_SIZE
          ? undefined
          : parseInt(parseInt(page) + 1),
    });
  } catch (error) {
    return res.json({
      success: false,
      error: constants.ERROR_CODES.SERVER_ERROR,
    });
  }
};

/**
 *
 * @param {import("express").Request} req
 * @param {import("express").Response} res
 * @returns
 */
walletController.paymentOrderHistoryByID = async (req, res) => {
  try {
    const { user_id } = req.user;
    const { payment_order_id } = req.params;
    const paymentOrderHistoryByID = await prisma.tbl_payment_orders.findFirst({
      where: {
        payment_order_id: payment_order_id,
        user_id: parseInt(user_id),
      },
      include: {
        tbl_payment_transactions: {
          orderBy: {
            created_at: "desc",
          },
        },
      },
    });
    if (
      paymentOrderHistoryByID === null ||
      paymentOrderHistoryByID === undefined ||
      !paymentOrderHistoryByID
    ) {
      return res.json({
        success: false,
        error: constants.ERROR_CODES.INVALID_PAYMENT_ORDER_ID,
      });
    }

    return res.json({ paymentOrderHistoryByID, success: true });
  } catch (error) {
    Logger.log("error", {
      message: "walletController:paymentOrderHistoryByID:catch-1",
      params: { error },
    });
    return res.json({
      error: constants.ERROR_CODES.SERVER_ERROR,
      success: false,
    });
  }
};

/**
 *
 * @param {import("express").Request} req
 * @param {import("express").Response} res
 * @returns
 */
walletController.payBookingFare = async (req, res) => {
  try {
    const { user } = req;
    const { booking_id } = req.body;
    Logger.log("info", {
      message: "walletController:payBookingFare:params",
      params: { userID: user.user_id },
    });
    Logger.log("info", {
      message: "walletController:payBookingFare:razorpay method",
      params: { userID: user.user_id, booking_id },
    });
    const interWalletTransaction =
      await WalletService.payBookingPaymentFromWallet({
        bookingID: parseInt(booking_id),
        userID: parseInt(user.user_id),
      });
    Logger.log("success", {
      message: "walletController:payBookingFare:success",
      params: {
        userID: user.user_id,
        interWalletTransaction: interWalletTransaction,
      },
    });
    return res.json({
      success: true,
      interWalletTransaction: interWalletTransaction,
    });
  } catch (error) {
    Logger.log("error", {
      message: "walletController:payBookingFare:catch-1",
      params: { error },
    });
    return res.json({ success: false, error: extractError(error) });
  }
};

/**
 *
 * @param {import("express").Request} req
 * @param {import("express").Response} res
 * @returns
 */
walletController.payReservationFare = async (req, res) => {
  try {
    const { user } = req;
    const { reservation_id } = req.body;
    Logger.log("info", {
      message: "walletController:payReservationFare:params",
      params: { userID: user.user_id },
    });
    Logger.log("info", {
      message: "walletController:payReservationFare:razorpay method",
      params: { userID: user.user_id, reservation_id },
    });
    const interWalletTransaction =
      await WalletService.payReservationPaymentFromWallet({
        reservationID: parseInt(reservation_id),
        userID: parseInt(user.user_id),
      });
    Logger.log("success", {
      message: "walletController:payReservationFare:success",
      params: {
        userID: user.user_id,
        interWalletTransaction: interWalletTransaction,
      },
    });
    return res.json({
      success: true,
      interWalletTransaction: interWalletTransaction,
    });
  } catch (error) {
    Logger.log("error", {
      message: "walletController:payReservationFare:catch-1",
      params: { error },
    });
    return res.json({ success: false, error: extractError(error) });
  }
};

module.exports = walletController;
