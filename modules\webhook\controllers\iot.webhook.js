const constants = require("../../../constants");
const Logger = require("../../../utils/logger");
const { extractError } = require("../../../utils/error.utils");
const { prisma } = require("../../../config/prisma");
const { BookingService } = require("../../services/booking.service");
const bookingSocketController = require("../../socket/controllers/booking.socket.controller");
const clientErrorSocketController = require("../../socket/controllers/clientError.socket.controller");

const iotWebhookController = {};

/**
 *
 * @param {import("express").Request} req
 * @param {import("express").Response} res
 * @returns
 */
iotWebhookController.confirmStartBooking = async (req, res) => {
  try {
    const { imei, command, booking_id, booking_log_id } = req.body;
    Logger.log("info", {
      message: "iotWebhookController:confirmStartBooking:params",
      params: { imei, command, booking_id, booking_log_id },
    });
    if (booking_log_id && booking_id) {
      const ongoingBooking = await prisma.tbl_bookings.findUnique({
        where: {
          booking_id: parseInt(booking_id),
        },
        include: {
          tbl_booking_logs: {
            orderBy: { created_at: "asc" },
          },
          tbl_users: true,
          tbl_vehicles: true,
        },
      });
      Logger.log("info", {
        message: "iotWebhookController:confirmStartBooking",
        params: {
          imei: imei,
          command: command,
          bookingID: ongoingBooking?.booking_id,
          bookingStatus: ongoingBooking.booking_status,
        },
      });
      const confirmStartBookingTransaction =
        await BookingService.confirmStartBooking({
          ongoingBooking: ongoingBooking,
          user: ongoingBooking.tbl_users,
        });
      bookingSocketController.emitOngoingBookingOnUpdate({
        ongoingBooking: confirmStartBookingTransaction.booking,
        firebaseID: ongoingBooking.tbl_users.firebase_id,
      });
      Logger.log("success", {
        message:
          "iotWebhookController:confirmStartBooking:INITIATED_START:success",
        params: {
          imei: imei,
          command: command,
          confirmStartBookingTransactionID:
            confirmStartBookingTransaction.booking.booking_id,
        },
      });
    }
    return res.json({
      success: true,
    });
  } catch (error) {
    Logger.log("error", {
      message: "iotWebhookController:confirmStartBooking:catch-1",
      params: { error },
    });
    return res.json({ success: false, error: extractError(error) });
  }
};

/**
 *
 * @param {import("express").Request} req
 * @param {import("express").Response} res
 * @returns
 */
iotWebhookController.confirmPauseBooking = async (req, res) => {
  try {
    const { imei, command, booking_id, booking_log_id } = req.body;
    Logger.log("info", {
      message: "iotWebhookController:confirmPauseBooking:params",
      params: { imei, command, booking_id, booking_log_id },
    });
    if (booking_log_id && booking_id) {
      const ongoingBooking = await prisma.tbl_bookings.findUnique({
        where: {
          booking_id: parseInt(booking_id),
        },
        include: {
          tbl_booking_logs: {
            orderBy: { created_at: "asc" },
          },
          tbl_users: true,
          tbl_vehicles: true,
        },
      });
      Logger.log("info", {
        message: "iotWebhookController:confirmPauseBooking",
        params: {
          imei: imei,
          command: command,
          bookingID: ongoingBooking?.booking_id,
          bookingStatus: ongoingBooking.booking_status,
        },
      });
      const confirmPauseBookingTransaction =
        await BookingService.confirmPauseBooking({
          ongoingBooking: ongoingBooking,
          user: ongoingBooking.tbl_users,
        });
      bookingSocketController.emitOngoingBookingOnUpdate({
        ongoingBooking: confirmPauseBookingTransaction.booking,
        firebaseID: ongoingBooking.tbl_users.firebase_id,
      });
      Logger.log("success", {
        message:
          "iotWebhookController:confirmPauseBooking:INITIATED_START:success",
        params: {
          imei: imei,
          command: command,
          confirmPauseBookingTransactionID:
            confirmPauseBookingTransaction.booking.booking_id,
        },
      });
    }
    return res.json({
      success: true,
    });
  } catch (error) {
    Logger.log("error", {
      message: "iotWebhookController:confirmPauseBooking:catch-1",
      params: { error },
    });
    return res.json({ success: false, error: extractError(error) });
  }
};

/**
 *
 * @param {import("express").Request} req
 * @param {import("express").Response} res
 * @returns
 */
iotWebhookController.confirmResumeBooking = async (req, res) => {
  try {
    const { imei, command, booking_id, booking_log_id } = req.body;
    Logger.log("info", {
      message: "iotWebhookController:confirmResumeBooking:params",
      params: { imei, command, booking_id, booking_log_id },
    });
    if (booking_log_id && booking_id) {
      const ongoingBooking = await prisma.tbl_bookings.findUnique({
        where: {
          booking_id: parseInt(booking_id),
        },
        include: {
          tbl_booking_logs: {
            orderBy: { created_at: "asc" },
          },
          tbl_users: true,
          tbl_vehicles: true,
        },
      });
      Logger.log("info", {
        message: "iotWebhookController:confirmResumeBooking",
        params: {
          imei: imei,
          command: command,
          bookingID: ongoingBooking?.booking_id,
          bookingStatus: ongoingBooking.booking_status,
        },
      });
      const confirmResumeBookingTransaction =
        await BookingService.confirmResumeBooking({
          ongoingBooking: ongoingBooking,
          user: ongoingBooking.tbl_users,
        });
      bookingSocketController.emitOngoingBookingOnUpdate({
        ongoingBooking: confirmResumeBookingTransaction.booking,
        firebaseID: ongoingBooking.tbl_users.firebase_id,
      });
      Logger.log("success", {
        message:
          "iotWebhookController:confirmResumeBooking:INITIATED_START:success",
        params: {
          imei: imei,
          command: command,
          confirmResumeBookingTransactionID:
            confirmResumeBookingTransaction.booking.booking_id,
        },
      });
    }
    return res.json({
      success: true,
    });
  } catch (error) {
    Logger.log("error", {
      message: "iotWebhookController:confirmResumeBooking:catch-1",
      params: { error },
    });
    return res.json({ success: false, error: extractError(error) });
  }
};

/**
 *
 * @param {import("express").Request} req
 * @param {import("express").Response} res
 * @returns
 */
iotWebhookController.confirmEndBooking = async (req, res) => {
  try {
    const { imei, command, booking_id, booking_log_id } = req.body;
    Logger.log("info", {
      message: "iotWebhookController:confirmEndBooking:params",
      params: { imei, command, booking_id, booking_log_id },
    });
    if (booking_log_id && booking_id) {
      const ongoingBooking = await prisma.tbl_bookings.findUnique({
        where: {
          booking_id: parseInt(booking_id),
        },
        include: {
          tbl_booking_logs: {
            orderBy: { created_at: "asc" },
          },
          tbl_users: true,
          tbl_vehicles: true,
        },
      });
      Logger.log("info", {
        message: "iotWebhookController:confirmEndBooking",
        params: {
          imei: imei,
          command: command,
          bookingID: ongoingBooking?.booking_id,
          bookingStatus: ongoingBooking.booking_status,
        },
      });
      const confirmEndBookingTransaction =
        await BookingService.confirmEndBooking({
          ongoingBooking: ongoingBooking,
          user: ongoingBooking.tbl_users,
        });
      bookingSocketController.emitOngoingBookingOnUpdate({
        ongoingBooking: confirmEndBookingTransaction.booking,
        firebaseID: ongoingBooking.tbl_users.firebase_id,
      });
      Logger.log("success", {
        message:
          "iotWebhookController:confirmEndBooking:INITIATED_START:success",
        params: {
          imei: imei,
          command: command,
          confirmEndBookingTransactionID:
            confirmEndBookingTransaction.booking.booking_id,
        },
      });
    }
    return res.json({
      success: true,
    });
  } catch (error) {
    Logger.log("error", {
      message: "iotWebhookController:confirmEndBooking:catch-1",
      params: { error },
    });
    return res.json({ success: false, error: extractError(error) });
  }
};

/**
 *
 * @param {import("express").Request} req
 * @param {import("express").Response} res
 * @returns
 */
iotWebhookController.timeoutStartBooking = async (req, res) => {
  try {
    const { imei, command, booking_id, booking_log_id } = req.body;
    Logger.log("info", {
      message: "iotWebhookController:timeoutStartBooking:params",
      params: { imei, command, booking_id, booking_log_id },
    });
    if (booking_log_id && booking_id) {
      const ongoingBooking = await prisma.tbl_bookings.findUnique({
        where: {
          booking_id: parseInt(booking_id),
        },
        include: {
          tbl_booking_logs: {
            orderBy: { created_at: "asc" },
          },
          tbl_users: true,
          tbl_vehicles: true,
        },
      });
      Logger.log("info", {
        message: "iotWebhookController:timeoutStartBooking",
        params: {
          imei: imei,
          command: command,
          bookingID: ongoingBooking?.booking_id,
          bookingStatus: ongoingBooking.booking_status,
        },
      });
      const revertStartBookingTransaction =
        await BookingService.revertStartBooking({
          ongoingBooking,
        });
      await bookingSocketController.emitOngoingBookingOnUpdate({
        ongoingBooking: null,
        firebaseID: revertStartBookingTransaction.booking.tbl_users.firebase_id,
      });
      await clientErrorSocketController.emitClientError({
        error: constants.ERROR_CODES.VEHICLE_DID_NOT_RESPOND,
        firebaseID: revertStartBookingTransaction.booking.tbl_users.firebase_id,
      });
      Logger.log("success", {
        message:
          "iotWebhookController:timeoutStartBooking:INITIATED_START:success",
        params: {
          imei: imei,
          command: command,
          revertStartBookingTransactionID:
            revertStartBookingTransaction.booking.booking_id,
        },
      });
    }
    return res.json({
      success: true,
    });
  } catch (error) {
    Logger.log("error", {
      message: "iotWebhookController:timeoutStartBooking:catch-1",
      params: { error },
    });
    return res.json({ success: false, error: extractError(error) });
  }
};

/**
 *
 * @param {import("express").Request} req
 * @param {import("express").Response} res
 * @returns
 */
iotWebhookController.timeoutPauseBooking = async (req, res) => {
  try {
    const { imei, command, booking_id, booking_log_id } = req.body;
    Logger.log("info", {
      message: "iotWebhookController:timeoutPauseBooking:params",
      params: { imei, command, booking_id, booking_log_id },
    });
    if (booking_log_id && booking_id) {
      const ongoingBooking = await prisma.tbl_bookings.findUnique({
        where: {
          booking_id: parseInt(booking_id),
        },
        include: {
          tbl_booking_logs: {
            orderBy: { created_at: "asc" },
          },
          tbl_users: true,
          tbl_vehicles: true,
        },
      });
      Logger.log("info", {
        message: "iotWebhookController:timeoutPauseBooking",
        params: {
          imei: imei,
          command: command,
          bookingID: ongoingBooking?.booking_id,
          bookingStatus: ongoingBooking.booking_status,
        },
      });
      const revertPauseBookingTransaction =
        await BookingService.revertPauseBooking({
          ongoingBooking,
        });
      await bookingSocketController.emitOngoingBookingOnUpdate({
        ongoingBooking: revertPauseBookingTransaction.booking,
        firebaseID: revertPauseBookingTransaction.booking.tbl_users.firebase_id,
      });
      await clientErrorSocketController.emitClientError({
        error: constants.ERROR_CODES.VEHICLE_DID_NOT_RESPOND,
        firebaseID: revertPauseBookingTransaction.booking.tbl_users.firebase_id,
      });
      Logger.log("success", {
        message:
          "iotWebhookController:timeoutPauseBooking:INITIATED_START:success",
        params: {
          imei: imei,
          command: command,
          revertPauseBookingTransactionID:
            revertPauseBookingTransaction.booking.booking_id,
        },
      });
    }
    return res.json({
      success: true,
    });
  } catch (error) {
    Logger.log("error", {
      message: "iotWebhookController:timeoutPauseBooking:catch-1",
      params: { error },
    });
    return res.json({ success: false, error: extractError(error) });
  }
};

/**
 *
 * @param {import("express").Request} req
 * @param {import("express").Response} res
 * @returns
 */
iotWebhookController.timeoutResumeBooking = async (req, res) => {
  try {
    const { imei, command, booking_id, booking_log_id } = req.body;
    Logger.log("info", {
      message: "iotWebhookController:timeoutResumeBooking:params",
      params: { imei, command, booking_id, booking_log_id },
    });
    if (booking_log_id && booking_id) {
      const ongoingBooking = await prisma.tbl_bookings.findUnique({
        where: {
          booking_id: parseInt(booking_id),
        },
        include: {
          tbl_booking_logs: {
            orderBy: { created_at: "asc" },
          },
          tbl_users: true,
          tbl_vehicles: true,
        },
      });
      Logger.log("info", {
        message: "iotWebhookController:timeoutResumeBooking",
        params: {
          imei: imei,
          command: command,
          bookingID: ongoingBooking?.booking_id,
          bookingStatus: ongoingBooking.booking_status,
        },
      });
      const revertResumeBookingTransaction =
        await BookingService.revertResumeBooking({
          ongoingBooking,
        });
      await bookingSocketController.emitOngoingBookingOnUpdate({
        ongoingBooking: revertResumeBookingTransaction.booking,
        firebaseID:
          revertResumeBookingTransaction.booking.tbl_users.firebase_id,
      });
      await clientErrorSocketController.emitClientError({
        error: constants.ERROR_CODES.VEHICLE_DID_NOT_RESPOND,
        firebaseID:
          revertResumeBookingTransaction.booking.tbl_users.firebase_id,
      });
      Logger.log("success", {
        message:
          "iotWebhookController:timeoutResumeBooking:INITIATED_START:success",
        params: {
          imei: imei,
          command: command,
          revertResumeBookingTransactionID:
            revertResumeBookingTransaction.booking.booking_id,
        },
      });
    }
    return res.json({
      success: true,
    });
  } catch (error) {
    Logger.log("error", {
      message: "iotWebhookController:timeoutResumeBooking:catch-1",
      params: { error },
    });
    return res.json({ success: false, error: extractError(error) });
  }
};

/**
 *
 * @param {import("express").Request} req
 * @param {import("express").Response} res
 * @returns
 */
iotWebhookController.timeoutEndBooking = async (req, res) => {
  try {
    const { imei, command, booking_id, booking_log_id } = req.body;
    Logger.log("info", {
      message: "iotWebhookController:timeoutEndBooking:params",
      params: { imei, command, booking_id, booking_log_id },
    });
    if (booking_log_id && booking_id) {
      const ongoingBooking = await prisma.tbl_bookings.findUnique({
        where: {
          booking_id: parseInt(booking_id),
        },
        include: {
          tbl_booking_logs: {
            orderBy: { created_at: "asc" },
          },
          tbl_users: true,
          tbl_vehicles: true,
        },
      });
      Logger.log("info", {
        message: "iotWebhookController:timeoutEndBooking",
        params: {
          imei: imei,
          command: command,
          bookingID: ongoingBooking?.booking_id,
          bookingStatus: ongoingBooking.booking_status,
        },
      });
      const revertEndBookingTransaction = await BookingService.revertEndBooking(
        {
          ongoingBooking,
        }
      );
      await bookingSocketController.emitOngoingBookingOnUpdate({
        ongoingBooking: revertEndBookingTransaction.booking,
        firebaseID: revertEndBookingTransaction.booking.tbl_users.firebase_id,
      });
      await clientErrorSocketController.emitClientError({
        error: constants.ERROR_CODES.VEHICLE_DID_NOT_RESPOND,
        firebaseID: revertEndBookingTransaction.booking.tbl_users.firebase_id,
      });
      Logger.log("success", {
        message:
          "iotWebhookController:timeoutEndBooking:INITIATED_START:success",
        params: {
          imei: imei,
          command: command,
          revertEndBookingTransactionID:
            revertEndBookingTransaction.booking.booking_id,
        },
      });
    }
    return res.json({
      success: true,
    });
  } catch (error) {
    Logger.log("error", {
      message: "iotWebhookController:timeoutEndBooking:catch-1",
      params: { error },
    });
    return res.json({ success: false, error: extractError(error) });
  }
};


module.exports = iotWebhookController;
