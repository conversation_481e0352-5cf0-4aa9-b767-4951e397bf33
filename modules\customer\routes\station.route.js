const express = require("express");
const router = express.Router();
const stationController = require("../controllers/station.controller");
const authMiddleware = require("../middlewares/auth.middleware");
const validationMiddleware = require("../middlewares/validation.middleware");

//vehicle routes

// get all vehicles details
router.get("/", authMiddleware, stationController.getAllStations);
router.get("/nearby", authMiddleware, stationController.getNearbyStations);
router.get(
  "/:id",
  authMiddleware,
  validationMiddleware.checkStationIdParam,
  stationController.getStationById
);

router.get(
  "/:id/machines",
  authMiddleware,
  validationMiddleware.checkStationIdParam,
  stationController.getStationMachines
);

// get vehicle details by id

module.exports = router;
