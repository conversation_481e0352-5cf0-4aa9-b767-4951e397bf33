
const { socketIO } = require("../../../config/socket.io");
const constants = require("../../../constants");
const Logger = require("../../../utils/logger");

const clientErrorSocketController = {};


clientErrorSocketController.emitClientError = async ({ error, type, firebaseID }) => {
  Logger.log("info", {
    message: "clientErrorSocketController:emitClientError",
    params: { firebaseID, error },
  });
  try {
    socketIO.to(firebaseID).emit(constants.SOCKET_EVENTS.ON_SERVER_SIDE_ERROR, { type, error });
  } catch (error) {
    Logger.log("error", { message: "clientErrorSocketController:emitClientError:catch-1", params: { error } });
  }
};

module.exports = clientErrorSocketController;
