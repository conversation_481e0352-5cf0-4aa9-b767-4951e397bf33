const { prisma } = require("../../../config/prisma");
const { socketIO } = require("../../../config/socket.io");
const constants = require("../../../constants");
const Logger = require("../../../utils/logger");

const reservationSocketController = {};

/**
 *
 * @param {object} param0
 * @param {import("@prisma/client").tbl_reservations} param0.ongoingReservation
 * @param {String} param0.firebaseID
 */
reservationSocketController.emitOngoingReservationOnUpdate = async ({
  ongoingReservation,
  firebaseID,
}) => {
  Logger.log("info", {
    message: "reservationSocketController:emitOngoingReservationOnUpdate",
    params: { firebaseID },
  });

  try {
    socketIO
      .to(firebaseID)
      .emit(
        constants.SOCKET_EVENTS.ON_ONGOING_RESERVATION_UPDATE,
        ongoingReservation
      );
  } catch (error) {
    Logger.log("error", {
      message:
        "reservationSocketController:emitOngoingReservationOnUpdate:catch-1",
      params: { error },
    });
  }
};

module.exports = { reservationSocketController };
