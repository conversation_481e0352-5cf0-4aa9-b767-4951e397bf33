const dotenv = require("dotenv");
const path = require("path");
const p = path.resolve(__dirname, `${process.env.NODE_ENV}.env`);
console.log("setting path for environment...", p);
console.log("setting up environment variables...");
dotenv.config({
  path: p,
});

const environmentVariables = {
  NODE_ENV: process.env.NODE_ENV || "development",
  PORT: process.env.PORT || 8090,
  BROKER_PORT: process.env.BROKER_PORT || 1111,
  MONGODB_URL: process.env.MONGODB_URL,
  DATABASE_URL: process.env.DATABASE_URL,
  SLACK_AUTH_TOKEN: process.env.SLACK_AUTH_TOKEN,
  SLACK_ERROR_NOTIFICATION_HOOK: process.env.SLACK_ERROR_NOTIFICATION_HOOK,
  SLACK_WASH_BOOKING_NOTIFICATION_HOOK:
    process.env.SLACK_WASH_BOOKING_NOTIFICATION_HOOK,
  RAZORPAY_SECRET: process.env.RAZORPAY_SECRET,
  RAZORPAY_KEY_ID: process.env.RAZORPAY_KEY_ID,
  RAZORPAY_WEBHOOK_SECRET: process.env.RAZORPAY_WEBHOOK_SECRET,
  RAZORPAY_ORDERS_WEBHOOK_SECRET: process.env.RAZORPAY_ORDERS_WEBHOOK_SECRET,
  RAZORPAY_PAYMENTS_WEBHOOK_SECRET:
    process.env.RAZORPAY_PAYMENTS_WEBHOOK_SECRET,
  CLIENT_HOST: process.env.CLIENT_HOST,
  SERVER_HOST: process.env.SERVER_HOST,
  KYC_URL: process.env.KYC_URL,
  KYC_API_TOKEN: process.env.KYC_API_TOKEN,
  KYC_OTP_URL: process.env.KYC_OTP_URL,
  ADMIN_API_KEY: process.env.ADMIN_API_KEY,
};
console.log("environment variables set-----------------------------");
console.log(environmentVariables);
module.exports = environmentVariables;
