const { prisma } = require("../config/prisma");
const constants = require("../constants");
const Logger = require("./logger");
const calculations = {};
const R = 6371;
calculations.datesDiff = (a, b) => {
  const aDate = new Date(a);
  const bDate = new Date(b);
  return Math.abs(aDate.getTime() - bDate.getTime());
};
calculations.coordinatesDiff = (lat1, lng1, lat2, lng2) => {
  const dLat = ((lat2 - lat1) * Math.PI) / 180;
  const dLon = ((lng2 - lng1) * Math.PI) / 180;
  lat1 = (lat1 * Math.PI) / 180;
  lat2 = (lat2 * Math.PI) / 180;

  const a =
    Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.sin(dLon / 2) * Math.sin(dLon / 2) * Math.cos(lat1) * Math.cos(lat2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  return R * c;
};

/**
 *
 * @param {Array<import("@prisma/client").tbl_stations>} stations
 * @param {import("@prisma/client").tbl_vehicles} vehicle
 * @returns
 */
calculations.nearestStation = (stations, vehicle) => {
  // const vehicle = await prisma.tbl_vehicles.findFirst();
  // const stations = await prisma.tbl_stations.findMany();

  let ns = constants.OUTSTATION_ID;
  let minD = Infinity;

  stations.forEach((station) => {
    const d = calculations.coordinatesDiff(
      vehicle.lat,
      vehicle.lng,
      station.lat,
      station.lng
    );
    if (d < minD && station.station_id != constants.OUTSTATION_ID) {
      minD = d;
      ns = station.station_id;
    }
  });

  return { minimunDistance: minD, nearestStationID: ns };
};
calculations.nearestStationWithLatLng = (stations, lat, lng) => {
  let ns = null;
  let minD = Infinity;

  stations.forEach((station) => {
    const d = calculations.coordinatesDiff(lat, lng, station.lat, station.lng);
    if (d < minD && station.station_id != constants.OUTSTATION_ID) {
      minD = d;
      ns = station;
    }
  });

  return { minimunDistance: minD, nearestStation: ns };
};

calculations.calculateWashTimeFair = (time) => {
  // const totalTimeinMinutes = time / (60 * 1000);
  // const totalHours = Math.ceil(totalTimeinMinutes / 60);
  const finalCharge = 6 * 1;
  return finalCharge;
};
calculations.calculateFaultTimeFair = (totalFaultTime, totalWashTime) => {
  return 0;
};

calculations.toRad = (Value) => {
  return (Value * Math.PI) / 180;
};
calculations.distanceBetweenTwoCoordinates = (
  { lat1, lon1 },
  { lat2, lon2 }
) => {
  var R = 6371000; // m
  var dLat = calculations.toRad(lat2 - lat1);
  var dLon = calculations.toRad(lon2 - lon1);
  var latitude1 = calculations.toRad(lat1);
  var latitude2 = calculations.toRad(lat2);

  var a =
    Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.sin(dLon / 2) *
      Math.sin(dLon / 2) *
      Math.cos(latitude1) *
      Math.cos(latitude2);
  var c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  var d = R * c;
  return d;
};
calculations.checkIfNearStation = ({ v_lat, v_lng }, { s_lat, s_lng }) => {};
calculations.checkIfNearAnyStation = async ({ v_lat, v_lng }) => {
  Logger.pageLogger("checkingIfNearAnyStation", {
    message: "checking if near any of the stations...",
  });
  try {
    let isNearAnyStation = false;

    const stations = await prisma.tbl_stations.findMany();
    stations.forEach((station) => {
      const dv = calculations.distanceBetweenTwoCoordinates(
        { lat1: v_lat, lon1: v_lng },
        { lat2: station.lat, lon2: station.lng }
      );
      Logger.pageLogger("checkIfNearAnyStation : inside loop", {
        station: station.station_id,
        dv,
      });
      if (dv < 20) {
        isNearAnyStation = true;
      }
    });
    return isNearAnyStation;
  } catch (error) {
    Logger.pageLogger("checkingIfNearAnyStation : catch", {
      error,
    });
    return false;
  }
};
calculations.timeDiff = (a, b) => {
  const a_d = new Date(a);
  const b_d = new Date(b);

  return (a_d.getTime() - b_d.getTime()) / (1000 * 60);
};
module.exports = calculations;
