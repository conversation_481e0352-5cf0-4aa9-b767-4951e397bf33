const constants = require("../../../constants");
const calculations = require("../../../utils/calculations");
const Logger = require("../../../utils/logger");

const { prisma } = require("../../../config/prisma");
const { UserService } = require("../../services/user.service");
const { OfferService } = require("../../services/offer.service");

const checkMiddleware = {};

// profile related checks
/**
 *
 * @param {import("express").Request} req
 * @param {import("express").Response} res
 * @param {import("express").NextFunction} next
 * @returns
 */
checkMiddleware.isProfileCompleted = async (req, res, next) => {
  try {
    const { user } = req;
    Logger.log("info", {
      message: "checkMiddleware:isProfileCompleted:params",
      params: { user },
    });
    if (user && user.email && user.first_name && user.address1) {
      Logger.log("info", {
        message: "checkMiddleware:isProfileCompleted:success",
        params: { userID: user.user_id },
      });
      return next();
    } else {
      Logger.log("error", {
        message: "checkMiddleware:isProfileCompleted:catch-2",
        params: { error: constants.ERROR_CODES.USER_PROFILE_NOT_COMPLETE },
      });
      return res.json({
        success: false,
        error: constants.ERROR_CODES.USER_PROFILE_NOT_COMPLETE,
      });
    }
  } catch (error) {
    Logger.log("error", {
      message: "checkMiddleware:isProfileCompleted:catch-1",
      params: { error },
    });
    return res.json({
      success: false,
      error: constants.ERROR_CODES.SERVER_ERROR,
    });
  }
};

// wallet related checks
/**
 *
 * @param {import("express").Request} req
 * @param {import("express").Response} res
 * @param {import("express").NextFunction} next
 * @returns
 */
checkMiddleware.isWalletActivated = async (req, res, next) => {
  try {
    const { user } = req;
    Logger.log("info", {
      message: "checkMiddleware:isWalletActivated:params",
      params: { user },
    });
    if (UserService.isWalletActivated(user)) {
      Logger.log("info", {
        message: "checkMiddleware:isWalletActivated:success",
        params: {
          userID: user.user_id,
          walletID: user.wallet_id,
          // razorpayCustomerID: user.razorpay_customer_id,
        },
      });
      return next();
    } else if (user.tbl_wallets && user.tbl_wallets.is_disabled) {
      Logger.log("error", {
        message: "checkMiddleware:isWalletActivated:catch-2",
        params: {
          userID: user.user_id,
          error: constants.ERROR_CODES.WALLET_DISABLED,
          reason: user.tbl_wallets.disable_reason,
        },
      });
      return res.json({
        success: false,
        error: {
          ...constants.ERROR_CODES.WALLET_DISABLED,
          reason: user.tbl_wallets.disable_reason,
        },
      });
    } else {
      Logger.log("error", {
        message: "checkMiddleware:isWalletActivated:catch-2",
        params: {
          userID: user.user_id,
          error: constants.ERROR_CODES.WALLET_NOT_ACTIVATED,
        },
      });
      return res.json({
        success: false,
        error: constants.ERROR_CODES.WALLET_NOT_ACTIVATED,
      });
    }
  } catch (error) {
    Logger.log("error", {
      message: "checkMiddleware:isWalletActivated:catch-1",
      params: { error },
    });
    return res.json({
      success: false,
      error: constants.ERROR_CODES.SERVER_ERROR,
    });
  }
};

/**
 *
 * @param {import("express").Request} req
 * @param {import("express").Response} res
 * @param {import("express").NextFunction} next
 * @returns
 */
checkMiddleware.isWalletNotActivated = async (req, res, next) => {
  try {
    const { user } = req;
    Logger.log("info", {
      message: "checkMiddleware:isWalletNotActivated:params",
      params: { user },
    });
    if (!UserService.isWalletActivated(user)) {
      Logger.log("info", {
        message: "checkMiddleware:isWalletNotActivated:success",
        params: { userID: user.user_id },
      });
      return next();
    } else {
      Logger.log("error", {
        message: "checkMiddleware:isWalletNotActivated:catch-2",
        params: {
          userID: user.user_id,
          error: constants.ERROR_CODES.WALLET_ALREADY_ACTIVATED,
        },
      });
      return res.json({
        success: false,
        error: constants.ERROR_CODES.WALLET_ALREADY_ACTIVATED,
      });
    }
  } catch (error) {
    Logger.log("error", {
      message: "checkMiddleware:isWalletNotActivated:catch-1",
      params: { error },
    });
    return res.json({
      success: false,
      error: constants.ERROR_CODES.SERVER_ERROR,
    });
  }
};

/**
 *
 * @param {import("express").Request} req
 * @param {import("express").Response} res
 * @param {import("express").NextFunction} next
 * @returns
 */
checkMiddleware.isSufficientWalletBalance = async (req, res, next) => {
  try {
    const { user } = req;
    Logger.log("info", {
      message: "checkMiddleware:isSufficientWalletBalance:params",
      params: { user },
    });
    if (
      parseInt(user.tbl_wallets.balance) >= constants.WALLET_BALANCE_THRESHOLD
    ) {
      Logger.log("info", {
        message: "checkMiddleware:isSufficientWalletBalance:success",
        params: { userID: user.user_id, balance: user.tbl_wallets.balance },
      });
      next();
    } else {
      Logger.log("error", {
        message: "checkMiddleware:isSufficientWalletBalance:catch-2",
        params: {
          userID: user.user_id,
          balance: user.tbl_wallets.balance,
          error: constants.ERROR_CODES.INSUFFICIENT_BALANCE,
        },
      });
      return res.json({
        success: false,
        error: constants.ERROR_CODES.INSUFFICIENT_BALANCE,
      });
    }
  } catch (error) {
    Logger.log("error", {
      message: "checkMiddleware:isSufficientWalletBalance:catch-1",
      params: { error },
    });
    return res.json({
      success: false,
      error: constants.ERROR_CODES.SERVER_ERROR,
    });
  }
};

/**
 *
 * @param {import("express").Request} req
 * @param {import("express").Response} res
 * @param {import("express").NextFunction} next
 * @returns
 */
checkMiddleware.isSufficientWalletBalanceForSubscriptionTypePurchase = async (
  req,
  res,
  next
) => {
  try {
    const { user, subscriptionType } = req;
    const { payment_source } = req.body;

    Logger.log("info", {
      message:
        "checkMiddleware:isSufficientWalletBalanceForSubscriptionTypePurchase:params",
      params: {
        user,
        subscriptionTypeAmount: subscriptionType.amount,
        payment_source,
      },
    });
    if (
      (parseInt(user.tbl_wallets.balance) > parseInt(subscriptionType.amount) &&
        payment_source === constants.PAYMENT_SOURCE.WALLET) ||
      (parseInt(user.tbl_wallets.balance) >=
        constants.WALLET_BALANCE_THRESHOLD &&
        payment_source === constants.PAYMENT_SOURCE.WALLET_RAZORPAY) ||
      payment_source === constants.PAYMENT_SOURCE.RAZORPAY
    ) {
      Logger.log("success", {
        message:
          "checkMiddleware:isSufficientWalletBalanceForSubscriptionTypePurchase:success",
        params: { userID: user.user_id, balance: user.tbl_wallets.balance },
      });
      next();
    } else {
      Logger.log("error", {
        message:
          "checkMiddleware:isSufficientWalletBalanceForSubscriptionTypePurchase:catch-2",
        params: {
          userID: user.user_id,
          balance: user.tbl_wallets.balance,
          error: constants.ERROR_CODES.INSUFFICIENT_BALANCE,
        },
      });
      return res.json({
        success: false,
        error: constants.ERROR_CODES.INSUFFICIENT_BALANCE,
      });
    }
  } catch (error) {
    Logger.log("error", {
      message:
        "checkMiddleware:isSufficientWalletBalanceForSubscriptionTypePurchase:catch-1",
      params: { error },
    });
    return res.json({
      success: false,
      error: constants.ERROR_CODES.SERVER_ERROR,
    });
  }
};

/**
 *
 * @param {import("express").Request} req
 * @param {import("express").Response} res
 * @param {import("express").NextFunction} next
 * @returns
 */
checkMiddleware.isSufficientWalletBalanceForPlanTypePurchase = async (
  req,
  res,
  next
) => {
  try {
    const { user, planType } = req;
    const { payment_source } = req.body;

    Logger.log("info", {
      message:
        "checkMiddleware:isSufficientWalletBalanceForPlanTypePurchase:params",
      params: {
        user,
        planTypeAmount: planType.amount,
        payment_source,
      },
    });
    if (
      (parseInt(user.tbl_wallets.balance) > parseInt(planType.amount) &&
        payment_source === constants.PAYMENT_SOURCE.WALLET) ||
      (parseInt(user.tbl_wallets.balance) >=
        constants.WALLET_BALANCE_THRESHOLD &&
        payment_source === constants.PAYMENT_SOURCE.WALLET_RAZORPAY) ||
      payment_source === constants.PAYMENT_SOURCE.RAZORPAY
    ) {
      Logger.log("success", {
        message:
          "checkMiddleware:isSufficientWalletBalanceForPlanTypePurchase:success",
        params: { userID: user.user_id, balance: user.tbl_wallets.balance },
      });
      next();
    } else {
      Logger.log("error", {
        message:
          "checkMiddleware:isSufficientWalletBalanceForPlanTypePurchase:catch-2",
        params: {
          userID: user.user_id,
          balance: user.tbl_wallets.balance,
          error: constants.ERROR_CODES.INSUFFICIENT_BALANCE,
        },
      });
      return res.json({
        success: false,
        error: constants.ERROR_CODES.INSUFFICIENT_BALANCE,
      });
    }
  } catch (error) {
    Logger.log("error", {
      message:
        "checkMiddleware:isSufficientWalletBalanceForPlanTypePurchase:catch-1",
      params: { error },
    });
    return res.json({
      success: false,
      error: constants.ERROR_CODES.SERVER_ERROR,
    });
  }
};

/**
 *
 * @param {import("express").Request} req
 * @param {import("express").Response} res
 * @param {import("express").NextFunction} next
 * @returns
 */
checkMiddleware.isDepositValid = async (req, res, next) => {
  try {
    const { user } = req;
    Logger.log("info", {
      message: "checkMiddleware:isSubscriptionTypeValid:params",
      params: { userID: user.user_id },
    });
    if (
      user.tbl_wallets.security_deposit >=
      user.tbl_user_types.tbl_security_deposit_types.amount
    ) {
      return next();
    } else {
      Logger.log("info", {
        message: "checkMiddleware:isSubscriptionTypeValid:catch-3",
        params: {
          userID: user.user_id,
          error: constants.ERROR_CODES.SECURITY_DEPOSIT_NOT_PAID,
        },
      });
      return res.json({
        success: false,
        error: constants.ERROR_CODES.SECURITY_DEPOSIT_NOT_PAID,
      });
    }
  } catch (error) {
    Logger.log("error", {
      message: "checkMiddleware:isSubscriptionTypeValid:catch-1",
      params: { error },
    });
    return res.json({
      success: false,
      error: constants.ERROR_CODES.SERVER_ERROR,
    });
  }
};

/**
 *
 * @param {import("express").Request} req
 * @param {import("express").Response} res
 * @param {import("express").NextFunction} next
 * @returns
 */
checkMiddleware.isSecurityDepositDone = async (req, res, next) => {
  try {
    const { user } = req;
    Logger.log("info", {
      message: "checkMiddleware:isSecurityDepositDone:params",
      params: { userID: user.user_id },
    });

    if (
      user.tbl_wallets.security_deposit >=
      user.tbl_user_types.tbl_security_deposit_types.amount
    ) {
      return next();
    } else {
      Logger.log("info", {
        message: "checkMiddleware:isSecurityDepositDone:catch-3",
        params: {
          userID: user.user_id,
          error: constants.ERROR_CODES.SECURITY_DEPOSIT_NOT_PAID,
        },
      });
      return res.json({
        success: false,
        error: constants.ERROR_CODES.SECURITY_DEPOSIT_NOT_PAID,
      });
    }
  } catch (error) {
    Logger.log("error", {
      message: "checkMiddleware:isSecurityDepositDone:catch-1",
      params: { error },
    });
    return res.json({
      success: false,
      error: constants.ERROR_CODES.SERVER_ERROR,
    });
  }
};

/**
 *
 * @param {import("express").Request} req
 * @param {import("express").Response} res
 * @param {import("express").NextFunction} next
 * @returns
 */
checkMiddleware.isDepositPaymentValid = async (req, res, next) => {
  try {
    const { user } = req;
    Logger.log("info", {
      message: "checkMiddleware:isSubscriptionTypeValid:params",
      params: { userID: user.user_id },
    });

    if (
      user.tbl_wallets.security_deposit <
      user.tbl_user_types.tbl_security_deposit_types.amount
    ) {
      return next();
    } else {
      Logger.log("info", {
        message: "checkMiddleware:isSubscriptionTypeValid:catch-3",
        params: {
          userID: user.user_id,
          error: constants.ERROR_CODES.SECURITY_DEPOSIT_ALREADY_PAID,
        },
      });
      return res.json({
        success: false,
        error: constants.ERROR_CODES.SECURITY_DEPOSIT_ALREADY_PAID,
      });
    }
  } catch (error) {
    Logger.log("error", {
      message: "checkMiddleware:isSubscriptionTypeValid:catch-1",
      params: { error },
    });
    return res.json({
      success: false,
      error: constants.ERROR_CODES.SERVER_ERROR,
    });
  }
};

// offer related checks
/**
 *
 * @param {import("express").Request} req
 * @param {import("express").Response} res
 * @param {import("express").NextFunction} next
 * @returns
 */
checkMiddleware.isSubscriptionTypeValid = async (req, res, next) => {
  try {
    const { user } = req;
    const { subscription_type_id } = req.body;
    Logger.log("info", {
      message: "checkMiddleware:isSubscriptionTypeValid:params",
      params: { userID: user.user_id, subscription_type_id },
    });
    const subscriptionType = await prisma.tbl_subscription_types.findUnique({
      where: {
        subscription_type_id: parseInt(subscription_type_id),
      },
    });
    const allocatedSubscriptionTypeIDs =
      UserService.getAllocatedSubscriptionTypes(user);
    if (
      subscriptionType &&
      !subscriptionType.is_disabled &&
      allocatedSubscriptionTypeIDs.includes(
        subscriptionType.subscription_type_id
      )
    ) {
      Logger.log("success", {
        message: "checkMiddleware:isSubscriptionTypeValid:success",
        params: { userID: user.user_id, allocatedSubscriptionTypeIDs },
      });
      req.subscriptionType = subscriptionType;
      return next();
    }
    Logger.log("info", {
      message: "checkMiddleware:isSubscriptionTypeValid:catch-2",
      params: {
        userID: user.user_id,
        error: constants.ERROR_CODES.SUBSCRIPTION_TYPE_INVALID,
      },
    });
    return res.json({
      success: false,
      error: constants.ERROR_CODES.SUBSCRIPTION_TYPE_INVALID,
    });
  } catch (error) {
    Logger.log("error", {
      message: "checkMiddleware:isSubscriptionTypeValid:catch-1",
      params: { error },
    });
    return res.json({
      success: false,
      error: constants.ERROR_CODES.SERVER_ERROR,
    });
  }
};

/**
 *
 * @param {import("express").Request} req
 * @param {import("express").Response} res
 * @param {import("express").NextFunction} next
 * @returns
 */
checkMiddleware.checkMaxSubscriptionsAllowed = async (req, res, next) => {
  try {
    const { user } = req;
    Logger.log("info", {
      message: "checkMiddleware:checkMaxSubscriptionsAllowed:params",
      params: {
        userID: user.user_id,
      },
    });
    const canAssignSubscriptionTypeToUser =
      await OfferService.canAssignSubscriptionTypeToUser({ user });

    if (canAssignSubscriptionTypeToUser) {
      Logger.log("success", {
        message: "checkMiddleware:checkMaxSubscriptionsAllowed:success",
        params: { userID: user.user_id },
      });

      return next();
    }
    Logger.log("info", {
      message: "checkMiddleware:checkMaxSubscriptionsAllowed:catch-2",
      params: {
        userID: user.user_id,
        error: constants.ERROR_CODES.MAX_SUBSCRIPTION_LIMIT,
      },
    });
    return res.json({
      success: false,
      error: constants.ERROR_CODES.MAX_SUBSCRIPTION_LIMIT,
    });
  } catch (error) {
    Logger.log("error", {
      message: "checkMiddleware:checkMaxSubscriptionsAllowed:catch-1",
      params: { error },
    });
    return res.json({
      success: false,
      error: constants.ERROR_CODES.SERVER_ERROR,
    });
  }
};

/**
 *
 * @param {import("express").Request} req
 * @param {import("express").Response} res
 * @param {import("express").NextFunction} next
 * @returns
 */
checkMiddleware.isPlanTypeValid = async (req, res, next) => {
  try {
    const { user } = req;
    const { plan_type_id } = req.body;
    Logger.log("info", {
      message: "checkMiddleware:isPlanTypeValid:params",
      params: { userID: user.user_id, plan_type_id },
    });
    const planType = await prisma.tbl_plan_types.findUnique({
      where: {
        plan_type_id: parseInt(plan_type_id),
      },
    });
    const allocatedPlanTypeIDs = UserService.getAllocatedPlanTypes(user);
    if (
      planType &&
      !planType.is_disabled &&
      allocatedPlanTypeIDs.includes(planType.plan_type_id)
    ) {
      Logger.log("success", {
        message: "checkMiddleware:isPlanTypeValid:success",
        params: { userID: user.user_id, allocatedPlanTypeIDs },
      });
      req.planType = planType;
      return next();
    }
    Logger.log("info", {
      message: "checkMiddleware:isPlanTypeValid:catch-2",
      params: {
        userID: user.user_id,
        error: constants.ERROR_CODES.PLAN_TYPE_INVALID,
      },
    });
    return res.json({
      success: false,
      error: constants.ERROR_CODES.PLAN_TYPE_INVALID,
    });
  } catch (error) {
    Logger.log("error", {
      message: "checkMiddleware:isPlanTypeValid:catch-1",
      params: { error },
    });
    return res.json({
      success: false,
      error: constants.ERROR_CODES.SERVER_ERROR,
    });
  }
};

/**
 *
 * @param {import("express").Request} req
 * @param {import("express").Response} res
 * @param {import("express").NextFunction} next
 * @returns
 */
checkMiddleware.checkMaxPlansAllowed = async (req, res, next) => {
  try {
    const { user } = req;
    Logger.log("info", {
      message: "checkMiddleware:checkMaxPlansAllowed:params",
      params: { userID: user.user_id },
    });
    const canAssignPlanTypeToUser = await OfferService.canAssignPlanTypeToUser({
      user,
    });

    if (canAssignPlanTypeToUser) {
      Logger.log("success", {
        message: "checkMiddleware:checkMaxPlansAllowed:success",
        params: { userID: user.user_id },
      });

      return next();
    }
    Logger.log("info", {
      message: "checkMiddleware:checkMaxPlansAllowed:catch-2",
      params: {
        userID: user.user_id,
        error: constants.ERROR_CODES.MAX_PLAN_LIMIT,
      },
    });
    return res.json({
      success: false,
      error: constants.ERROR_CODES.MAX_PLAN_LIMIT,
    });
  } catch (error) {
    Logger.log("error", {
      message: "checkMiddleware:checkMaxPlansAllowed:catch-1",
      params: { error },
    });
    return res.json({
      success: false,
      error: constants.ERROR_CODES.SERVER_ERROR,
    });
  }
};

// machine related checks
/**
 *
 * @param {import("express").Request} req
 * @param {import("express").Response} res
 * @param {import("express").NextFunction} next
 * @returns
 */
checkMiddleware.isVehicleAvailable = async (req, res, next) => {
  try {
    const { user, vehicle, ongoingReservation } = req;

    Logger.log("info", {
      message: "checkMiddleware:isVehicleAvailable:params",
      params: {
        userID: user.user_id,
        vehicleID: vehicle.vehicle_id,
        ongoingReservationID: ongoingReservation?.reservation_id,
      },
    });
    if (!vehicle) {
      Logger.log("error", {
        message: "checkMiddleware:isVehicleAvailable:catch-2",
        params: {
          userID: user.user_id,
          error: constants.ERROR_CODES.INVALID_VEHICLE_ID,
        },
      });
      return res.json({
        success: false,
        error: constants.ERROR_CODES.INVALID_VEHICLE_ID,
      });
    } else if (
      (vehicle.vehicle_status === constants.VEHICLE_STATUS.READY &&
        !vehicle.tbl_vehicle_types.is_disabled) ||
      (ongoingReservation &&
        vehicle.vehicle_status === constants.VEHICLE_STATUS.RESERVED &&
        vehicle.vehicle_id == ongoingReservation.vehicle_id)
    ) {
      req.vehicle = vehicle;
      Logger.log("success", {
        message: "checkMiddleware:isVehicleAvailable:success",
        params: { userID: user.user_id, vehicleID: vehicle.vehicle_id },
      });
      return next();
    } else {
      Logger.log("error", {
        message: "checkMiddleware:isVehicleAvailable:catch-2",
        params: {
          userID: user.user_id,
          error: constants.ERROR_CODES.VEHICLE_NOT_AVAILABLE,
        },
      });
      return res.json({
        success: false,
        error: constants.ERROR_CODES.VEHICLE_NOT_AVAILABLE,
      });
    }
  } catch (error) {
    Logger.log("error", {
      message: "checkMiddleware:isVehicleAvailable:catch-1",
      params: { error },
    });
    return res.json({
      success: false,
      error: constants.ERROR_CODES.SERVER_ERROR,
    });
  }
};

/**
 *
 * @param {import("express").Request} req
 * @param {import("express").Response} res
 * @param {import("express").NextFunction} next
 * @returns
 */
checkMiddleware.isVehicleInStation = async (req, res, next) => {
  try {
    const { user, ongoingBooking } = req;
    const { outstationConsent } = req.body;
    Logger.log("info", {
      message: "checkMiddleware:isVehicleInStation:params",
      params: { user, ongoingBooking, outstationConsent },
    });
    const vehicle = await prisma.tbl_vehicles.findUnique({
      where: {
        vehicle_id: parseInt(ongoingBooking.vehicle_id),
      },
    });
    const stations = await prisma.tbl_stations.findMany();
    const { minimunDistance, nearestStationID } = calculations.nearestStation(
      stations,
      vehicle
    );
    Logger.log("info", {
      message: "checkMiddleware:isVehicleInStation:nearestStation",
      params: { userID: user.user_id, minimunDistance, nearestStationID },
    });
    req.isOutstation = true;
    req.rideEndStationID = constants.OUTSTATION_ID;

    if (minimunDistance < constants.OUTSTATION_THRESHOLD) {
      req.isOutstation = false;
      req.rideEndStationID = nearestStationID;
      Logger.log("info", {
        message: "checkMiddleware:isVehicleInStation:success",
        params: { userID: user.user_id, minimunDistance, nearestStationID },
      });
      return next();
    } else if (outstationConsent) {
      Logger.log("info", {
        message: "checkMiddleware:isVehicleInStation:success",
        params: { userID: user.user_id, outstationConsent },
      });
      return next();
    } else {
      Logger.log("error", {
        message: "checkMiddleware:isVehicleInStation:catch-2",
        params: { userID: user.user_id },
      });
      return res.json({
        success: false,
        error: constants.ERROR_CODES.VEHICLE_NOT_PARKED_NEAR_STATION,
      });
    }
  } catch (error) {
    Logger.log("error", {
      message: "checkMiddleware:isVehicleInStation:catch-1",
      params: { error },
    });
    return res.json({
      success: false,
      error: constants.ERROR_CODES.SERVER_ERROR,
    });
  }
};

// booking related checks
/**
 *
 * @param {import("express").Request} req
 * @param {import("express").Response} res
 * @param {import("express").NextFunction} next
 * @returns
 */
checkMiddleware.isNotBooked = async (req, res, next) => {
  try {
    const { user } = req;
    Logger.log("info", {
      message: "checkMiddleware:isNotBooked:params",
      params: { user },
    });
    const latestBooking = await prisma.tbl_bookings.findFirst({
      where: {
        user_id: parseInt(user.user_id),
      },
      orderBy: { updated_at: "desc" },
    });

    Logger.log("info", {
      message: "checkMiddleware:isNotBooked:latestBooking",
      params: { userID: user.user_id, latestBooking },
    });
    if (
      !latestBooking ||
      latestBooking.booking_status === constants.BOOKING_STATUS.TERMINATED ||
      latestBooking.booking_status === constants.BOOKING_STATUS.ENDED ||
      latestBooking.booking_status === constants.BOOKING_STATUS.CANCELLED
    ) {
      Logger.log("success", {
        message: "checkMiddleware:isNotBooked:success",
        params: { userID: user.user_id, latestBooking },
      });
      return next();
    } else {
      Logger.log("error", {
        message: "checkMiddleware:isNotBooked:catch-2",
        params: {
          userID: user.user_id,
          error: constants.ERROR_CODES.OPEN_BOOKINGS,
        },
      });
      return res.json({
        success: false,
        error: constants.ERROR_CODES.OPEN_BOOKINGS,
      });
    }
  } catch (error) {
    Logger.log("error", {
      message: "checkMiddleware:isNotBooked:catch-1",
      params: { error },
    });
    return res.json({
      success: false,
      error: constants.ERROR_CODES.SERVER_ERROR,
    });
  }
};

/**
 *
 * @param {import("express").Request} req
 * @param {import("express").Response} res
 * @param {import("express").NextFunction} next
 * @returns
 */
checkMiddleware.isBooked = async (req, res, next) => {
  try {
    const { user } = req;
    Logger.log("info", {
      message: "checkMiddleware:isBooked:params",
      params: { user },
    });
    const ongoingBooking = await prisma.tbl_bookings.findFirst({
      where: {
        user_id: parseInt(user.user_id),
      },
      include: {
        tbl_vehicles: true,
      },
      orderBy: { booking_initiation_at: "desc" },
    });

    Logger.log("info", {
      message: "checkMiddleware:isBooked:ongoingBooking",
      params: { userID: user.user_id, ongoingBooking },
    });
    if (
      ongoingBooking &&
      ongoingBooking.booking_status != constants.BOOKING_STATUS.ENDED &&
      ongoingBooking.booking_status != constants.BOOKING_STATUS.TERMINATED &&
      ongoingBooking.booking_status != constants.BOOKING_STATUS.CANCELLED
    ) {
      Logger.log("success", {
        message: "checkMiddleware:isBooked:success",
        params: {
          userID: user.user_id,
          ongoingBookingStatus: ongoingBooking.booking_status,
        },
      });
      req.ongoingBooking = ongoingBooking;
      return next();
    } else {
      Logger.log("error", {
        message: "checkMiddleware:isBooked:catch-2",
        params: {
          userID: user.user_id,
          error: constants.ERROR_CODES.NO_OPEN_BOOKINGS,
        },
      });
      return res.json({
        success: true,
        ongoingBooking: null,
      });
    }
  } catch (error) {
    Logger.log("error", {
      message: "checkMiddleware:isBooked:catch-1",
      params: { error },
    });
    return res.json({
      success: false,
      error: constants.ERROR_CODES.SERVER_ERROR,
    });
  }
};

/**
 *
 * @param {import("express").Request} req
 * @param {import("express").Response} res
 * @param {import("express").NextFunction} next
 * @returns
 */
checkMiddleware.isBookingRiding = async (req, res, next) => {
  try {
    const { user, ongoingBooking } = req;
    Logger.log("info", {
      message: "checkMiddleware:isBookingRiding:params",
      params: { user, ongoingBooking },
    });

    if (
      ongoingBooking &&
      ongoingBooking.booking_status === constants.BOOKING_STATUS.RIDING
    ) {
      Logger.log("success", {
        message: "checkMiddleware:isBookingRiding:success",
        params: { userID: user.user_id, ongoingBooking },
      });

      return next();
    } else {
      Logger.log("error", {
        message: "checkMiddleware:isBookingRiding:catch-2",
        params: {
          userID: user.user_id,
          error: constants.ERROR_CODES.BOOKING_NOT_RIDING,
        },
      });
      return res.json({
        success: false,
        error: constants.ERROR_CODES.BOOKING_NOT_RIDING,
      });
    }
  } catch (error) {
    Logger.log("error", {
      message: "checkMiddleware:isBookingRiding:catch-1",
      params: { error },
    });
    return res.json({
      success: false,
      error: constants.ERROR_CODES.SERVER_ERROR,
    });
  }
};

/**
 *
 * @param {import("express").Request} req
 * @param {import("express").Response} res
 * @param {import("express").NextFunction} next
 * @returns
 */
checkMiddleware.isBookingPaused = async (req, res, next) => {
  try {
    const { user, ongoingBooking } = req;
    Logger.log("info", {
      message: "checkMiddleware:isBookingPaused:params",
      params: { user, ongoingBooking },
    });

    if (
      ongoingBooking &&
      ongoingBooking.booking_status === constants.BOOKING_STATUS.PAUSED
    ) {
      Logger.log("success", {
        message: "checkMiddleware:isBookingPaused:success",
        params: { userID: user.user_id, ongoingBooking },
      });

      return next();
    } else {
      Logger.log("error", {
        message: "checkMiddleware:isBookingPaused:catch-2",
        params: {
          userID: user.user_id,
          error: constants.ERROR_CODES.BOOKING_NOT_PAUSED,
        },
      });
      return res.json({
        success: false,
        error: constants.ERROR_CODES.BOOKING_NOT_PAUSED,
      });
    }
  } catch (error) {
    Logger.log("error", {
      message: "checkMiddleware:isBookingPaused:catch-1",
      params: { error },
    });
    return res.json({
      success: false,
      error: constants.ERROR_CODES.SERVER_ERROR,
    });
  }
};

/**
 *
 * @param {import("express").Request} req
 * @param {import("express").Response} res
 * @param {import("express").NextFunction} next
 * @returns
 */
checkMiddleware.isBookingEnded = async (req, res, next) => {
  try {
    const { user } = req;
    const { booking_id } = req.body;
    Logger.log("info", {
      message: "checkMiddleware:isBookingEnded:params",
      params: { userID: user.user_id, bookingID: booking_id },
    });
    const booking = await prisma.tbl_bookings.findFirst({
      where: {
        booking_id: parseInt(booking_id),
        user_id: parseInt(user.user_id),
      },
    });
    Logger.log("info", {
      message: "checkMiddleware:isBookingEnded:params",
      params: { userID: user.user_id, bookingID: booking.booking_id },
    });
    if (
      booking &&
      (booking.booking_status === constants.BOOKING_STATUS.ENDED ||
        booking.booking_status === constants.BOOKING_STATUS.TERMINATED ||
        booking.booking_status === constants.BOOKING_STATUS.CANCELLED)
    ) {
      Logger.log("info", {
        message: "checkMiddleware:isBookingEnded:success",
        params: { userID: user.user_id, bookingID: booking.booking_id },
      });
      return next();
    } else {
      Logger.log("error", {
        message: "checkMiddleware:isBookingEnded:catch-2",
        params: {
          userID: user.user_id,
          error: constants.ERROR_CODES.NO_VALID_BOOKING,
        },
      });
      return res.json({
        success: false,
        error: constants.ERROR_CODES.NO_VALID_BOOKING,
      });
    }
  } catch (error) {
    Logger.log("error", {
      message: "checkMiddleware:isBookingEnded:catch-1",
      params: { error },
    });
    return res.json({
      success: false,
      error: constants.ERROR_CODES.SERVER_ERROR,
    });
  }
};

// reservation related

/**
 *
 * @param {import("express").Request} req
 * @param {import("express").Response} res
 * @param {import("express").NextFunction} next
 * @returns
 */
checkMiddleware.isReserved = async (req, res, next) => {
  try {
    const { user } = req;
    Logger.log("info", {
      message: "checkMiddleware:isReserved:params",
      params: { user },
    });
    const ongoingReservation = await prisma.tbl_reservations.findFirst({
      where: {
        reservation_status: constants.RESERVATION_STATUS.RESERVED,

        user_id: parseInt(user.user_id),
      },
      include: {
        tbl_vehicles: true,
        tbl_reservation_types: true,
        tbl_stations: true,
      },
      orderBy: { updated_at: "desc" },
    });

    Logger.log("info", {
      message: "checkMiddleware:isReserved:ongoingReservation",
      params: { userID: user.user_id, ongoingReservation },
    });
    if (ongoingReservation) {
      Logger.log("success", {
        message: "checkMiddleware:isReserved:success",
        params: { userID: user.user_id, ongoingReservation },
      });
      req.ongoingReservation = ongoingReservation;
      return next();
    } else {
      Logger.log("error", {
        message: "checkMiddleware:isReserved:catch-2",
        params: {
          userID: user.user_id,
          error: constants.ERROR_CODES.NO_RESERVATION,
        },
      });
      return res.json({
        success: false,
        error: constants.ERROR_CODES.NO_RESERVATION,
      });
    }
  } catch (error) {
    Logger.log("error", {
      message: "checkMiddleware:isReserved:catch-1",
      params: { error },
    });
    return res.json({
      success: false,
      error: constants.ERROR_CODES.SERVER_ERROR,
    });
  }
};

/**
 *
 * @param {import("express").Request} req
 * @param {import("express").Response} res
 * @param {import("express").NextFunction} next
 * @returns
 */
checkMiddleware.isNotReserved = async (req, res, next) => {
  try {
    const { user, vehicle } = req;
    Logger.log("info", {
      message: "checkMiddleware:isNotReserved:params",
      params: { userID: user.user_id, vehicleID: vehicle.vehicle_id },
    });
    const latestReservation = await prisma.tbl_reservations.findFirst({
      where: {
        user_id: parseInt(user.user_id),
      },
      include: {
        tbl_vehicles: true,
        tbl_reservation_types: true,
        tbl_stations: true,
      },
      orderBy: { updated_at: "desc" },
    });

    Logger.log("info", {
      message: "checkMiddleware:isNotReserved:latestReservation",
      params: { userID: user.user_id, latestReservation },
    });
    if (
      !latestReservation ||
      latestReservation.reservation_status ===
        constants.RESERVATION_STATUS.TERMINATED ||
      latestReservation.reservation_status ===
        constants.RESERVATION_STATUS.ENDED
    ) {
      Logger.log("success", {
        message: "checkMiddleware:isNotReserved:success",
        params: { userID: user.user_id, latestReservation },
      });

      return next();
    } else if (
      !latestReservation ||
      (latestReservation.reservation_status ===
        constants.RESERVATION_STATUS.RESERVED &&
        latestReservation.vehicle_id == parseInt(vehicle.vehicle_id))
    ) {
      Logger.log("success", {
        message: "checkMiddleware:isNotReserved:success",
        params: { userID: user.user_id, latestReservation },
      });
      req.ongoingReservation = latestReservation;

      return next();
    } else {
      Logger.log("error", {
        message: "checkMiddleware:isNotReserved:catch-2",
        params: {
          userID: user.user_id,
          error: constants.ERROR_CODES.OPEN_RESERVATION,
        },
      });
      return res.json({
        success: false,
        error: constants.ERROR_CODES.OPEN_RESERVATION,
      });
    }
  } catch (error) {
    Logger.log("error", {
      message: "checkMiddleware:isNotReserved:catch-1",
      params: { error },
    });
    return res.json({
      success: false,
      error: constants.ERROR_CODES.SERVER_ERROR,
    });
  }
};

// push notification related checks
/**
 *
 * @param {import("express").Request} req
 * @param {import("express").Response} res
 * @param {import("express").NextFunction} next
 * @returns
 */
checkMiddleware.doesPushNotificationTokenBelongToUser = async (
  req,
  res,
  next
) => {
  try {
    const { user } = req;
    const { push_notification_token } = req.params;
    Logger.log("info", {
      message: "checkMiddleware:doesPushNotificationTokenBelongToUser:params",
      params: { user, push_notification_token },
    });
    const tokenRecord = await prisma.tbl_web_push_notifications_token.findFirst(
      {
        where: {
          push_notification_token,
          user_id: parseInt(user.user_id),
        },
      }
    );
    if (tokenRecord) {
      Logger.log("info", {
        message:
          "checkMiddleware:doesPushNotificationTokenBelongToUser:success",
        params: { userID: user.user_id, push_notification_token },
      });
      next();
    } else {
      Logger.log("error", {
        message:
          "checkMiddleware:doesPushNotificationTokenBelongToUser:catch-2",
        params: { error: "Token does not belong to user" },
      });
      return res.json({
        success: false,
        error: constants.ERROR_CODES.SERVER_ERROR,
      });
    }
  } catch (error) {
    Logger.log("error", {
      message: "checkMiddleware:doesPushNotificationTokenBelongToUser:catch-1",
      params: { error },
    });
    return res.json({
      success: false,
      error: constants.ERROR_CODES.SERVER_ERROR,
    });
  }
};

// transaction related checks
/**
 *
 * @param {import("express").Request} req
 * @param {import("express").Response} res
 * @param {import("express").NextFunction} next
 * @returns
 */
checkMiddleware.isAnyPendingTransaction = async (req, res, next) => {
  try {
    const { user, ongoingBooking } = req;
    Logger.log("info", {
      message: "checkMiddleware:isAnyPendingTransaction:params",
      params: { user, ongoingBooking },
    });
    const latestTransaction = await prisma.tbl_transactions.findFirst({
      where: { sender_wallet_id: user.tbl_wallets.wallet_id },
      orderBy: { created_at: "desc" },
    });
    Logger.log("info", {
      message: "checkMiddleware:isAnyPendingTransaction:latestTransaction",
      params: { latestTransaction },
    });
    if (
      (!latestTransaction ||
        latestTransaction.is_refund ||
        latestTransaction.transaction_status ===
          constants.TRANSACTION_STATUS.SUCCESS) &&
      (!ongoingBooking || (ongoingBooking && ongoingBooking.receipt_id))
    ) {
      Logger.log("success", {
        message: "checkMiddleware:isAnyPendingTransaction:success",
        params: { userID: user.user_id, latestTransaction },
      });
      next();
    } else {
      Logger.log("error", {
        message: "checkMiddleware:isAnyPendingTransaction:catch-2",
        params: {
          userID: user.user_id,
          error: constants.ERROR_CODES.PENDING_PAYMENT,
        },
      });
      return res.json({
        success: false,
        error: constants.ERROR_CODES.PENDING_PAYMENT,
      });
    }
  } catch (error) {
    Logger.log("error", {
      message: "checkMiddleware:isAnyPendingTransaction:catch-1",
      params: { error },
    });
    return res.json({
      success: false,
      error: constants.ERROR_CODES.SERVER_ERROR,
    });
  }
};

// receipt related checks
/**
 *
 * @param {import("express").Request} req
 * @param {import("express").Response} res
 * @param {import("express").NextFunction} next
 * @returns
 */
checkMiddleware.isReceiptNotAttached = async (req, res, next) => {
  try {
    const { user, washMasterRecord } = req;

    const userID = parseInt(user.user_id);
    Logger.log("info", {
      message: "checkMiddleware:isReceiptNotAttached:params",
      params: { user: userID, master_id: washMasterRecord.master_id },
    });
    if (washMasterRecord.tbl_receipts) {
      Logger.log("info", {
        message: "checkMiddleware:isReceiptNotAttached:catch-2",
        params: { error: constants.ERROR_CODES.RECEIPT_ALREADY_GENERATED },
      });
      return res.json({
        success: false,
        error: constants.ERROR_CODES.RECEIPT_ALREADY_GENERATED,
      });
    } else {
      Logger.log("info", {
        message: "checkMiddleware:isReceiptNotAttached:success",
        params: { user: userID, master_id: washMasterRecord.master_id },
      });

      return next();
    }
  } catch (error) {
    Logger.log("error", {
      message: "checkMiddleware:isReceiptNotAttached:catch-1",
      params: { error },
    });
    return res.json({ success: false, error: extractError(error) });
  }
};

// delivery related checks
/**
 *
 * @param {import("express").Request} req
 * @param {import("express").Response} res
 * @param {import("express").NextFunction} next
 * @returns
 */
checkMiddleware.isMaxDeliveryNotBooked = async (req, res, next) => {
  try {
    const { user } = req;
    Logger.log("info", {
      message: "checkMiddleware:isMaxDeliveryNotBooked:params",
      params: { user },
    });
    const latestDeliveryBookings = await prisma.tbl_delivery_bookings.findMany({
      where: {
        user_id: parseInt(user.user_id),
        OR: [
          {
            delivery_booking_status:
              constants.DELIVERY_BOOKING_STATUS.ON_DELIVERY,
          },
          {
            delivery_booking_status:
              constants.DELIVERY_BOOKING_STATUS.REQUESTED,
          },
          {
            delivery_booking_status:
              constants.DELIVERY_BOOKING_STATUS.VEHICLE_ASSIGNED,
          },
        ],
      },
      orderBy: { delivery_booking_initiation_at: "desc" },
    });

    Logger.log("info", {
      message:
        "checkMiddleware:isMaxDeliveryNotBooked:latestDeliveryBookingsLength",
      params: {
        userID: user.user_id,
        latestDeliveryBookingsLength: latestDeliveryBookings.length,
      },
    });
    if (
      !latestDeliveryBookings ||
      latestDeliveryBookings.length < constants.DEFAULT_MAX_DELIVERY_BOOKINGS
    ) {
      Logger.log("success", {
        message: "checkMiddleware:isMaxDeliveryNotBooked:success",
        params: {
          userID: user.user_id,
          latestDeliveryBookingsLength: latestDeliveryBookings.length,
        },
      });
      return next();
    } else {
      Logger.log("error", {
        message: "checkMiddleware:isMaxDeliveryNotBooked:catch-2",
        params: {
          userID: user.user_id,
          error: constants.ERROR_CODES.OPEN_BOOKINGS,
        },
      });
      return res.json({
        success: false,
        error: constants.ERROR_CODES.OPEN_BOOKINGS,
      });
    }
  } catch (error) {
    Logger.log("error", {
      message: "checkMiddleware:isMaxDeliveryNotBooked:catch-1",
      params: { error },
    });
    return res.json({
      success: false,
      error: constants.ERROR_CODES.SERVER_ERROR,
    });
  }
};

module.exports = checkMiddleware;
