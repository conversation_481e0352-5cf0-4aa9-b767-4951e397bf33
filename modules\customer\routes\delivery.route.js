const express = require("express");
const router = express.Router();

const authMiddleware = require("../middlewares/auth.middleware");
const checkMiddleware = require("../middlewares/check.middleware");
const validationMiddleware = require("../middlewares/validation.middleware");
const bookingController = require("../controllers/booking.controller");
const {
  interceptorMiddleware,
} = require("../middlewares/interceptor.middleware");
const deliveryController = require("../controllers/delivery.controller");

router.get("/", authMiddleware, deliveryController.getDeliveryBookings);

router.get("/:id", authMiddleware, deliveryController.getDeliveryBookingByID);

router.post(
  "/request",
  authMiddleware,
  validationMiddleware.checkVehicleTypeFieldInBody,
  checkMiddleware.isProfileCompleted,
  checkMiddleware.isWalletActivated,
  checkMiddleware.isSecurityDepositDone,
  checkMiddleware.isSufficientWalletBalance,
  checkMiddleware.isMaxDeliveryNotBooked,
  deliveryController.requestBooking
);

module.exports = router;
