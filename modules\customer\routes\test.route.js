const express = require("express");
var admin = require("firebase-admin");
const Logger = require("../../../utils/logger");
const { UserService } = require("../../services/user.service");
const { receiptController } = require("../controllers/receipt.controller");
const { IOTService } = require("../../services/iot.service");
const router = express.Router();
router.post("/fcm", async (req, res) => {
  try {
    const message = await UserService.generateFirebasePushNotification({
      title: req.body.title,
      description: req.body.description,
      userIDs: [req.body.user_id],
    });
    const result = message
      ? admin.messaging().sendMulticast({
          tokens: message.tokens,
          notification: {
            title: "`$FooCorp` up 1.43% on the day",
            body: "FooCorp gained 11.80 points to close at 835.67, up 1.43% on the day.",
          },
          android: {
            notification: {
              icon: "stock_ticker_update",
              color: "#7e55c3",
            },
          },
        })
      : null;
    Logger.log("info", {
      message: "firebasePushNotificationService:sendMulticastMessages:success",
      params: { result },
    });
    return res.json({ success: true });
  } catch (error) {
    Logger.log("error", {
      message: "firebasePushNotificationService:sendMulticastMessages:catch-1",
      params: { error },
    });
    return res.json({ success: false, error });
  }
});

router.post("/sockets", (req, res) => {
  const { imei, booking_log_id } = req.body;
  IOTService.sendCommand({
    imei: imei,
    command: req.body.command,
    bookingLogID: booking_log_id,
  });
  return res.json({ success: true });
});
router.post("/generate", receiptController.generateBookingReceipt);

module.exports = router;
