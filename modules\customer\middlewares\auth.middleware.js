var admin = require("firebase-admin");
const { prisma } = require("../../../config/prisma");
const constants = require("../../../constants");
const Logger = require("../../../utils/logger");
const { UserService } = require("../../services/user.service");
module.exports = async function (req, res, next) {
  if (
    req.headers.authorization &&
    req.headers.authorization.startsWith("Bearer ")
  ) {
    try {
      let idToken = req.headers.authorization.split("Bearer ")[1];
      const decodedIdToken = await admin.auth().verifyIdToken(idToken);

      Logger.log("info", {
        message: "authMiddleware:success",
        params: { uid: decodedIdToken.uid, phone: decodedIdToken.phone_number },
      });
      const dbUser = await prisma.tbl_users.findFirst({
        where: { firebase_id: decodedIdToken.uid },
        include: {
          tbl_wallets: true,
          tbl_user_types: {
            include: {
              tbl_security_deposit_types: true,
            },
          },
          tbl_location_offers_map: true,
        },
      });
      Logger.log("info", {
        message: "authMiddleware:dbUser",
        params: { uid: decodedIdToken.uid, dbUser },
      });
      if (!dbUser) {
        Logger.log("success", {
          message: "authMiddleware:success",
          params: { uid: decodedIdToken.uid },
        });
        req.user = dbUser;
        req.firebaseUser = decodedIdToken;
        return next();
      } else if (
        (dbUser && dbUser.user_type_id == constants.USER_TYPE_IDS.MAINTAINER) ||
        (dbUser && dbUser.user_type_id == constants.USER_TYPE_IDS.ADMIN) ||
        dbUser.is_disabled
      ) {
        Logger.log("error", {
          message: "authMiddleware:catch-3",
          params: { error: constants.ERROR_CODES.INVALID_USER },
        });
        // update last seen
        await UserService.updateUserLastSeen(dbUser);
        return res.send({
          error: constants.ERROR_CODES.INVALID_USER,
        });
      } else {
        Logger.log("success", {
          message: "authMiddleware:success",
          params: { uid: decodedIdToken.uid },
        });
        // update last seen
        await UserService.updateUserLastSeen(dbUser);
        req.user = dbUser;
        req.firebaseUser = decodedIdToken;
        return next();
      }
    } catch (error) {
      Logger.log("error", {
        message: "authMiddleware:catch-2",
        params: { error },
      });
      return res.send({ error: constants.ERROR_CODES.USER_AUTH_TOKEN_EXPIRED });
    }
  } else {
    Logger.log("error", {
      message: "authMiddleware:catch-1",
      params: { error: constants.ERROR_CODES.USER_AUTH_TOKEN_NOT_FOUND },
    });
    return res.send({ error: constants.ERROR_CODES.USER_AUTH_TOKEN_NOT_FOUND });
  }
};
