const Logger = require("../../utils/logger");
const { prisma } = require("../../config/prisma");
const constants = require("../../constants");
const { VehicleService } = require("./vehicle.service");
const { OfferService } = require("./offer.service");
const { BookingReceiptService } = require("./bookingReceipt.service");
const { Bike } = require("../models/mongoose/bike");
const { BikeStatusEnum } = require("../models/mongoose/enums");

class BookingService {
  constructor() {}

  /**
   *
   * @param {object} param0
   * @param {Number} param0.bookingID
   */
  static saveBookingPrevState = async ({ bookingID }) => {
    try {
      Logger.log("info", {
        message: "BookingService:saveBookingPrevState:params",
        params: { bookingID },
      });
      const currentBookingData = await prisma.tbl_bookings.findUnique({
        where: { booking_id: parseInt(bookingID) },
      });
      const previousBookingData =
        await prisma.tbl_bookings_prev_state.findUnique({
          where: { booking_id: parseInt(bookingID) },
        });
      const { booking_id, ...currentData } = currentBookingData;
      if (previousBookingData) {
        const updatedBookingPrevState =
          await prisma.tbl_bookings_prev_state.update({
            where: { booking_id: parseInt(bookingID) },
            data: { ...currentData },
          });
        Logger.log("success", {
          message: "BookingService:saveBookingPrevState:updated",
          params: { updatedBookingPrevState },
        });
        return updatedBookingPrevState;
      } else {
        const newBookingPrevState = await prisma.tbl_bookings_prev_state.create(
          {
            data: { ...currentBookingData },
          }
        );
        Logger.log("success", {
          message: "BookingService:saveBookingPrevState:created",
          params: { newBookingPrevState },
        });
        return newBookingPrevState;
      }
    } catch (error) {
      Logger.log("error", {
        message: "BookingService:saveBookingPrevState:catch-1",
        params: { error },
      });
    }
  };

  /**
   *
   * @param {object} param0
   * @param {Number} param0.bookingID
   * @param {any} param0.data
   */
  static saveBookingPrevStateWithData = async ({ bookingID, data }) => {
    try {
      Logger.log("info", {
        message: "BookingService:saveBookingPrevStateWithData:params",
        params: { bookingID },
      });
      const previousBookingData =
        await prisma.tbl_bookings_prev_state.findUnique({
          where: { booking_id: parseInt(bookingID) },
        });
      const { tbl_machines, ...currentData } = data;
      if (previousBookingData) {
        const updatedBookingPrevState =
          await prisma.tbl_bookings_prev_state.update({
            where: { booking_id: parseInt(bookingID) },
            data: currentData,
          });
        Logger.log("success", {
          message: "BookingService:saveBookingPrevStateWithData:updated",
          params: { updatedBookingPrevState },
        });
        return updatedBookingPrevState;
      } else {
        const newBookingPrevState = await prisma.tbl_bookings_prev_state.create(
          {
            data: { ...currentData, booking_id: parseInt(bookingID) },
          }
        );
        Logger.log("success", {
          message: "BookingService:saveBookingPrevStateWithData:created",
          params: { newBookingPrevState },
        });
        return newBookingPrevState;
      }
    } catch (error) {
      Logger.log("error", {
        message: "BookingService:saveBookingPrevStateWithData:catch-1",
        params: { error },
      });
    }
  };

  /**
   *
   * @param {object} param0
   * @param {import("@prisma/client").tbl_users} param0.user
   */
  static getCompletedUserBookings = async ({ user, skip, take }) => {
    try {
      Logger.log("info", {
        message: "BookingService:getCompletedUserBookings:params",
        params: {
          userID: user.user_id,
          skip,
          take,
        },
      });
      const bookings = await prisma.tbl_bookings.findMany({
        where: {
          user_id: parseInt(user.user_id),
          OR: [
            { booking_status: constants.BOOKING_STATUS.ENDED },
            { booking_status: constants.BOOKING_STATUS.TERMINATED },
            { booking_status: constants.BOOKING_STATUS.CANCELLED },
          ],
        },
        include: {
          tbl_booking_logs: {
            orderBy: { created_at: "asc" },
          },
          tbl_booking_receipts: true,
          tbl_stations_tbl_bookings_booking_end_station_idTotbl_stations: true,
          tbl_stations_tbl_bookings_booking_start_station_idTotbl_stations: true,
          tbl_vehicles: true,
        },
        orderBy: {
          booking_initiation_at: "desc",
        },
        skip: skip,
        take: take,
      });

      Logger.log("success", {
        message: "BookingService:getCompletedUserBookings:success",
        params: {
          userID: user.user_id,
          bookingsLength: bookings?.length,
        },
      });
      return bookings;
    } catch (error) {
      Logger.log("error", {
        message: "BookingService:getCompletedUserBookings:catch-1",
        params: { error },
      });
      throw error;
    }
  };

  /**
   *
   * @param {object} param0
   * @param {import("@prisma/client").tbl_users} param0.user
   * @param {Number} param0.bookingID
   */
  static getUserBookingByID = async ({ user, bookingID }) => {
    try {
      Logger.log("info", {
        message: "BookingService:getUserBookingByID:params",
        params: {
          userID: user.user_id,
          bookingID,
        },
      });
      const booking = await prisma.tbl_bookings.findFirst({
        where: {
          user_id: parseInt(user.user_id),
          booking_id: parseInt(bookingID),
        },
        include: {
          tbl_booking_logs: {
            orderBy: { created_at: "asc" },
          },
          tbl_booking_receipts: {
            include: {
              tbl_coupons: true,
              tbl_plans: true,
              tbl_subscriptions: true,
              tbl_inter_wallet_transactions: true,
            },
          },
          tbl_stations_tbl_bookings_booking_end_station_idTotbl_stations: true,
          tbl_stations_tbl_bookings_booking_start_station_idTotbl_stations: true,
          tbl_vehicles: true,
        },
      });

      Logger.log("success", {
        message: "BookingService:getUserBookingByID:success",
        params: {
          userID: user.user_id,
          bookingID: booking.booking_id,
        },
      });
      return booking;
    } catch (error) {
      Logger.log("error", {
        message: "BookingService:getUserBookingByID:catch-1",
        params: { error },
      });
      throw error;
    }
  };

  /**
   *
   * @param {object} param0
   * @param {import("@prisma/client").tbl_users} param0.user
   * @param {import("@prisma/client").tbl_vehicle_types} param0.vehicle
   * @param {Number} param0.planID
   */
  static initiateStartBooking = async ({ user, vehicle, planID }) => {
    try {
      Logger.log("info", {
        message: "BookingService:initiateStartBooking:params",
        params: { userID: user.user_id, vehicle: vehicle.vehicle_id, planID },
      });
      const isPlanActivated = planID
        ? await OfferService.activateUserPlanByID({ user, id: planID })
        : true;
      Logger.log("info", {
        message: "BookingService:initiateStartBooking:isPlanActivated",
        params: {
          userID: user.user_id,
          vehicle: vehicle.vehicle_id,
          planID,
          isPlanActivated,
        },
      });
      if (!isPlanActivated) {
        Logger.log("error", {
          message: "BookingService:initiateStartBooking:catch-2",
          params: {
            userID: user.user_id,
            vehicle: vehicle.vehicle_id,
            planID,
            isPlanActivated,
            error: constants.ERROR_CODES.PLAN_TYPE_INVALID,
          },
        });
        throw constants.ERROR_CODES.PLAN_TYPE_INVALID;
      }
      const initiateStartBookingTransaction = await prisma.$transaction(
        async (tx) => {
          const updatedVehicle = await tx.tbl_vehicles.update({
            where: {
              vehicle_id: parseInt(vehicle.vehicle_id),
            },
            data: {
              vehicle_status: constants.VEHICLE_STATUS.INITIATED_UNLOCK,
            },
          });
          Logger.log("info", {
            message:
              "BookingService:initiateStartBooking:$transaction:updatedVehicle",
            params: {
              userID: user.user_id,
              vehicleID: updatedVehicle.vehicle_id,
              vehicleStatus: updatedVehicle.vehicle_status,
            },
          });

          const newBooking = await tx.tbl_bookings.create({
            data: {
              booking_status: constants.BOOKING_STATUS.INITIATED_RIDE,
              vehicle_id: parseInt(vehicle.vehicle_id),
              booking_start_station_id: parseInt(vehicle.station_id),
              booking_initiation_at: new Date(),
              user_id: parseInt(user.user_id),
              user_selected_plan_id: planID,
            },
            include: {
              tbl_vehicles: true,
              tbl_stations_tbl_bookings_booking_start_station_idTotbl_stations: true,
            },
          });
          Logger.log("info", {
            message:
              "BookingService:initiateStartBooking:$transaction:newBooking",
            params: {
              userID: user.user_id,
              vehicleID: updatedVehicle.vehicle_id,
              bookingID: newBooking.booking_id,
            },
          });

          const newBookingLog = await tx.tbl_booking_logs.create({
            data: {
              booking_id: newBooking.booking_id,
              booking_action: constants.BOOKING_ACTIONS.INITIATE_START,
              user_id: parseInt(user.user_id),
              lat: Number(parseFloat(vehicle.lat).toFixed(10)),
              lng: Number(parseFloat(vehicle.lng).toFixed(10)),
              battery: vehicle.battery,
            },
          });
          Logger.log("info", {
            message:
              "BookingService:initiateStartBooking:$transaction:newBookingLog",
            params: {
              userID: user.user_id,
              newBookingID: newBooking.booking_id,
              vehicleID: updatedVehicle.vehicle_id,
              bookingLogID: newBookingLog.booking_log_id,
            },
          });

          return {
            booking: newBooking,
            vehicle: updatedVehicle,
            bookingLog: newBookingLog,
          };
        }
      );
      Logger.log("success", {
        message: "BookingService:initiateStartBooking:$transaction:success",
        params: {
          userID: user.user_id,
          vehicleID: vehicle.vehicle_id,
        },
      });
      return initiateStartBookingTransaction;
    } catch (error) {
      Logger.log("error", {
        message: "BookingService:initiateStartBooking:catch-1",
        params: { error },
      });
      throw error;
    }
  };

  /**
   *
   * @param {object} param0
   * @param {import("@prisma/client").tbl_users} param0.user
   * @param {import("@prisma/client").tbl_bookings} param0.ongoingBooking
   */
  static confirmStartBooking = async ({ ongoingBooking, user }) => {
    try {
      Logger.log("info", {
        message: "BookingService:confirmStartBooking:params",
        params: {
          userID: user.user_id,
          bookingID: ongoingBooking.booking_id,
          vehicleID: ongoingBooking.vehicle_id,
        },
      });
      const confirmStartBookingTransaction = await prisma.$transaction(
        async (tx) => {
          const updatedVehicle = await tx.tbl_vehicles.update({
            where: {
              vehicle_id: parseInt(ongoingBooking.vehicle_id),
            },
            data: {
              vehicle_status: constants.VEHICLE_STATUS.RIDING,
            },
          });
          Logger.log("info", {
            message:
              "BookingService:confirmStartBooking:$transaction:updatedVehicle",
            params: {
              userID: user.user_id,
              bookingID: ongoingBooking.booking_id,
              vehicleID: updatedVehicle.vehicle_id,
              vehicleStatus: updatedVehicle.vehicle_status,
            },
          });
          const newBookingLog = await tx.tbl_booking_logs.create({
            data: {
              booking_id: ongoingBooking.booking_id,
              booking_action: constants.BOOKING_ACTIONS.START,
              user_id: parseInt(user.user_id),
              lat: Number(parseFloat(updatedVehicle.lat).toFixed(10)),
              lng: Number(parseFloat(updatedVehicle.lng).toFixed(10)),
              battery: updatedVehicle.battery,
            },
          });
          Logger.log("info", {
            message:
              "BookingService:confirmStartBooking:$transaction:newBookingLog",
            params: {
              userID: user.user_id,
              bookingID: ongoingBooking.booking_id,
              vehicleID: updatedVehicle.vehicle_id,
              bookingLogID: newBookingLog.booking_log_id,
            },
          });
          const updatedBooking = await tx.tbl_bookings.update({
            where: {
              booking_id: parseInt(ongoingBooking.booking_id),
            },
            data: {
              booking_status: constants.BOOKING_STATUS.RIDING,
              booking_start_at: new Date(),
            },
            include: {
              tbl_vehicles: true,
              tbl_stations_tbl_bookings_booking_start_station_idTotbl_stations: true,
            },
          });
          Logger.log("info", {
            message:
              "BookingService:confirmStartBooking:$transaction:updatedBooking",
            params: {
              userID: user.user_id,
              bookingID: updatedBooking.booking_id,
              vehicleID: updatedVehicle.vehicle_id,
              bookingLogID: newBookingLog.booking_log_id,
            },
          });
          return {
            booking: updatedBooking,
            vehicle: updatedVehicle,
            bookingLog: newBookingLog,
          };
        }
      );
      //update old vehicle database status
      try {
        await Bike.findOneAndUpdate(
          {
            np: confirmStartBookingTransaction.vehicle.vehicle_number,
          },
          {
            status: BikeStatusEnum.Busy,
          }
        );
      } catch (error) {
        Logger.log("error", {
          message: "BookingService:confirmStartBooking:catch-2",
          params: { error },
        });
      }

      Logger.log("success", {
        message: "BookingService:confirmStartBooking:$transaction:success",
        params: {
          userID: user.user_id,
          bookingID: ongoingBooking.booking_id,
          vehicleID: ongoingBooking.vehicle_id,
        },
      });
      return confirmStartBookingTransaction;
    } catch (error) {
      Logger.log("error", {
        message: "BookingService:confirmStartBooking:catch-1",
        params: { error },
      });
      throw error;
    }
  };

  /**
   *
   * @param {object} param0
   * @param {import("@prisma/client").tbl_bookings} param0.ongoingBooking
   */
  static revertStartBooking = async ({ ongoingBooking }) => {
    try {
      Logger.log("info", {
        message: "BookingService:revertStartBooking:params",
        params: {
          bookingID: ongoingBooking.booking_id,
          vehicleID: ongoingBooking.vehicle_id,
        },
      });
      const revertStartBookingTransaction = await prisma.$transaction(
        async (tx) => {
          const updatedVehicle = await tx.tbl_vehicles.update({
            where: {
              vehicle_id: parseInt(ongoingBooking.vehicle_id),
            },
            data: {
              vehicle_status: constants.VEHICLE_STATUS.READY,
            },
          });
          Logger.log("info", {
            message:
              "BookingService:revertStartBooking:$transaction:updatedVehicle",
            params: {
              bookingID: ongoingBooking.booking_id,
              vehicleID: updatedVehicle.vehicle_id,
              vehicleStatus: updatedVehicle.vehicle_status,
            },
          });

          const terminatedBooking = await tx.tbl_bookings.update({
            where: {
              booking_id: parseInt(ongoingBooking.booking_id),
            },
            data: {
              booking_status: constants.BOOKING_STATUS.TERMINATED,
            },
            include: {
              tbl_users: true,
            },
          });
          Logger.log("info", {
            message:
              "BookingService:revertStartBooking:$transaction:terminatedBooking",
            params: {
              bookingID: terminatedBooking.booking_id,
              vehicleID: updatedVehicle.vehicle_id,
            },
          });

          return {
            booking: terminatedBooking,
            vehicle: updatedVehicle,
          };
        }
      );
      Logger.log("success", {
        message: "BookingService:revertStartBooking:$transaction:success",
        params: {
          bookingID: ongoingBooking.booking_id,
          vehicleID: ongoingBooking.vehicle_id,
        },
      });
      return revertStartBookingTransaction;
    } catch (error) {
      Logger.log("error", {
        message: "BookingService:revertStartBooking:catch-1",
        params: { error },
      });
      throw error;
    }
  };

  /**
   *
   * @param {object} param0
   * @param {import("@prisma/client").tbl_users} param0.user
   * @param {import("@prisma/client").tbl_bookings} param0.ongoingBooking
   */
  static initiatePauseBooking = async ({ user, ongoingBooking }) => {
    try {
      Logger.log("info", {
        message: "BookingService:initiatePauseBooking:params",
        params: {
          userID: user.user_id,
          vehicleID: ongoingBooking.vehicle_id,
          ongoingBookingID: ongoingBooking.booking_id,
        },
      });
      const initiatePauseBookingTransaction = await prisma.$transaction(
        async (tx) => {
          const updatedVehicle = await tx.tbl_vehicles.update({
            where: { vehicle_id: parseInt(ongoingBooking.vehicle_id) },
            data: {
              vehicle_status: constants.VEHICLE_STATUS.INITIATED_LOCK_TEMP,
            },
          });
          Logger.log("info", {
            message:
              "BookingService:initiatePauseBooking:$transaction:updatedVehicle",
            params: {
              userID: user.user_id,
              bookingID: ongoingBooking.booking_id,
              vehicleID: updatedVehicle.vehicle_id,
              vehicleStatus: updatedVehicle.vehicle_status,
            },
          });
          const newBookingLog = await tx.tbl_booking_logs.create({
            data: {
              booking_id: ongoingBooking.booking_id,
              booking_action: constants.BOOKING_ACTIONS.INITIATE_PAUSE,
              user_id: parseInt(user.user_id),
              lat: Number(parseFloat(updatedVehicle.lat).toFixed(10)),
              lng: Number(parseFloat(updatedVehicle.lng).toFixed(10)),
              battery: updatedVehicle.battery,
            },
          });
          Logger.log("info", {
            message:
              "BookingService:initiatePauseBooking:$transaction:newBookingLog",
            params: {
              userID: user.user_id,
              ongoingBookingID: ongoingBooking.booking_id,
              vehicleID: updatedVehicle.vehicle_id,
              bookingLogID: newBookingLog.booking_log_id,
            },
          });
          const updatedBooking = await tx.tbl_bookings.update({
            where: { booking_id: parseInt(ongoingBooking.booking_id) },
            data: {
              booking_status: constants.BOOKING_STATUS.INITIATED_PAUSE,
            },
            include: {
              tbl_vehicles: true,
            },
          });
          Logger.log("info", {
            message:
              "BookingService:initiatePauseBooking:$transaction:updatedBooking",
            params: {
              userID: user.user_id,
              bookingID: updatedBooking.booking_id,
              vehicleID: updatedVehicle.vehicle_id,
            },
          });

          return {
            booking: updatedBooking,
            vehicle: updatedVehicle,
            bookingLog: newBookingLog,
          };
        }
      );
      Logger.log("success", {
        message: "BookingService:initiatePauseBooking:$transaction:success",
        params: {
          userID: user.user_id,
          ongoingBookingID: ongoingBooking.booking_id,
          vehicleID: ongoingBooking.vehicle_id,
        },
      });
      return initiatePauseBookingTransaction;
    } catch (error) {
      Logger.log("error", {
        message: "BookingService:initiatePauseBooking:catch-1",
        params: { error },
      });
      throw error;
    }
  };

  /**
   *
   * @param {object} param0
   * @param {import("@prisma/client").tbl_users} param0.user
   * @param {import("@prisma/client").tbl_bookings} param0.ongoingBooking
   */
  static confirmPauseBooking = async ({ user, ongoingBooking }) => {
    try {
      Logger.log("info", {
        message: "BookingService:confirmPauseBooking:params",
        params: {
          userID: user.user_id,
          ongoingBookingID: ongoingBooking.booking_id,
          vehicleID: ongoingBooking.vehicle_id,
        },
      });
      const confirmPauseBookingTransaction = await prisma.$transaction(
        async (tx) => {
          const updatedVehicle = await tx.tbl_vehicles.update({
            where: { vehicle_id: parseInt(ongoingBooking.vehicle_id) },
            data: {
              vehicle_status: constants.VEHICLE_STATUS.PAUSED,
            },
          });
          Logger.log("info", {
            message:
              "BookingService:confirmPauseBooking:$transaction:updatedVehicle",
            params: {
              userID: user.user_id,
              bookingID: ongoingBooking.booking_id,
              vehicleID: updatedVehicle.vehicle_id,
              vehicleStatus: updatedVehicle.vehicle_status,
            },
          });

          const newBookingLog = await tx.tbl_booking_logs.create({
            data: {
              booking_id: ongoingBooking.booking_id,
              booking_action: constants.BOOKING_ACTIONS.PAUSE,
              user_id: parseInt(user.user_id),
              lat: Number(parseFloat(updatedVehicle.lat).toFixed(10)),
              lng: Number(parseFloat(updatedVehicle.lng).toFixed(10)),
              battery: updatedVehicle.battery,
            },
          });
          Logger.log("info", {
            message:
              "BookingService:confirmPauseBooking:$transaction:newBookingLog",
            params: {
              userID: user.user_id,
              bookingID: ongoingBooking.booking_id,
              vehicleID: updatedVehicle.vehicle_id,
              bookingLogID: newBookingLog.booking_log_id,
            },
          });
          const updatedBooking = await tx.tbl_bookings.update({
            where: { booking_id: parseInt(ongoingBooking.booking_id) },
            data: {
              last_pause_at: new Date(),
              booking_status: constants.BOOKING_STATUS.PAUSED,
            },
            include: {
              tbl_vehicles: true,
            },
          });
          Logger.log("info", {
            message:
              "BookingService:confirmPauseBooking:$transaction:updatedBooking",
            params: {
              userID: user.user_id,
              bookingID: updatedBooking.booking_id,
              vehicleID: updatedVehicle.vehicle_id,
              bookingLogID: newBookingLog.booking_log_id,
            },
          });
          return {
            booking: updatedBooking,
            vehicle: updatedVehicle,
            bookingLog: newBookingLog,
          };
        }
      );
      //update old vehicle database status
      try {
        await Bike.findOneAndUpdate(
          {
            np: confirmPauseBookingTransaction.vehicle.vehicle_number,
          },
          {
            status: BikeStatusEnum.Locked,
          }
        );
      } catch (error) {
        Logger.log("error", {
          message: "BookingService:confirmPauseBooking:catch-2",
          params: { error },
        });
      }
      Logger.log("success", {
        message: "BookingService:confirmPauseBooking:$transaction:success",
        params: {
          userID: user.user_id,
          vehicleID: ongoingBooking.vehicle_id,
          ongoingBookingID: ongoingBooking.booking_id,
        },
      });
      return confirmPauseBookingTransaction;
    } catch (error) {
      Logger.log("error", {
        message: "BookingService:confirmPauseBooking:catch-1",
        params: { error },
      });
      throw error;
    }
  };

  /**
   *
   * @param {object} param0
   * @param {import("@prisma/client").tbl_bookings} param0.ongoingBooking
   */
  static revertPauseBooking = async ({ ongoingBooking }) => {
    try {
      Logger.log("info", {
        message: "BookingService:revertPauseBooking:params",
        params: {
          ongoingBookingID: ongoingBooking.booking_id,
          vehicleID: ongoingBooking.vehicle_id,
        },
      });
      const revertPauseBookingTransaction = await prisma.$transaction(
        async (tx) => {
          const updatedVehicle = await tx.tbl_vehicles.update({
            where: { vehicle_id: parseInt(ongoingBooking.vehicle_id) },
            data: {
              vehicle_status: constants.VEHICLE_STATUS.RIDING,
            },
          });
          Logger.log("info", {
            message:
              "BookingService:revertPauseBooking:$transaction:updatedVehicle",
            params: {
              bookingID: ongoingBooking.booking_id,
              vehicleID: updatedVehicle.vehicle_id,
              vehicleStatus: updatedVehicle.vehicle_status,
            },
          });
          const updatedBooking = await tx.tbl_bookings.update({
            where: { booking_id: parseInt(ongoingBooking.booking_id) },
            data: {
              booking_status: constants.BOOKING_STATUS.RIDING,
            },
            include: {
              tbl_vehicles: true,
              tbl_users: true,
            },
          });
          Logger.log("info", {
            message:
              "BookingService:revertPauseBooking:$transaction:updatedBooking",
            params: {
              bookingID: updatedBooking.booking_id,
              vehicleID: updatedVehicle.vehicle_id,
            },
          });

          return {
            booking: updatedBooking,
            vehicle: updatedVehicle,
          };
        }
      );
      Logger.log("success", {
        message: "BookingService:revertPauseBooking:$transaction:success",
        params: {
          vehicleID: ongoingBooking.vehicle_id,
          ongoingBookingID: ongoingBooking.booking_id,
        },
      });
      return revertPauseBookingTransaction;
    } catch (error) {
      Logger.log("error", {
        message: "BookingService:revertPauseBooking:catch-1",
        params: { error },
      });
      throw error;
    }
  };

  /**
   *
   * @param {object} param0
   * @param {import("@prisma/client").tbl_users} param0.user
   * @param {import("@prisma/client").tbl_bookings} param0.ongoingBooking
   */
  static initiateResumeBooking = async ({ user, ongoingBooking }) => {
    try {
      Logger.log("info", {
        message: "BookingService:initiateResumeBooking:params",
        params: {
          userID: user.user_id,
          vehicleID: ongoingBooking.vehicle_id,
          ongoingBookingID: ongoingBooking.booking_id,
        },
      });
      const initiateResumeBookingTransaction = await prisma.$transaction(
        async (tx) => {
          const updatedVehicle = await tx.tbl_vehicles.update({
            where: {
              vehicle_id: parseInt(ongoingBooking.vehicle_id),
            },
            data: {
              vehicle_status: constants.VEHICLE_STATUS.INITIATED_UNLOCK_TEMP,
            },
          });
          Logger.log("info", {
            message:
              "BookingService:initiateResumeBooking:$transaction:updatedVehicle",
            params: {
              userID: user.user_id,
              bookingID: ongoingBooking.booking_id,
              vehicleID: updatedVehicle.vehicle_id,
              vehicleStatusID: updatedVehicle.vehicle_status,
            },
          });
          const newBookingLog = await tx.tbl_booking_logs.create({
            data: {
              booking_id: ongoingBooking.booking_id,
              booking_action: constants.BOOKING_ACTIONS.INITIATE_RESUME,
              user_id: parseInt(user.user_id),
              lat: Number(parseFloat(updatedVehicle.lat).toFixed(10)),
              lng: Number(parseFloat(updatedVehicle.lng).toFixed(10)),
              battery: updatedVehicle.battery,
            },
          });
          Logger.log("info", {
            message:
              "BookingService:initiateResumeBooking:$transaction:newBookingLog",
            params: {
              userID: user.user_id,
              ongoingBookingID: ongoingBooking.booking_id,
              vehicleID: updatedVehicle.vehicle_id,
              bookingLogID: newBookingLog.booking_log_id,
            },
          });
          const updatedBooking = await tx.tbl_bookings.update({
            where: { booking_id: parseInt(ongoingBooking.booking_id) },
            data: {
              booking_status: constants.BOOKING_STATUS.INITIATED_RESUME,
            },
            include: {
              tbl_vehicles: true,
            },
          });
          Logger.log("info", {
            message:
              "BookingService:initiateResumeBooking:$transaction:updatedBooking",
            params: {
              userID: user.user_id,
              bookingID: updatedBooking.booking_id,
              vehicleID: updatedVehicle.vehicle_id,
            },
          });

          return {
            booking: updatedBooking,
            vehicle: updatedVehicle,
            bookingLog: newBookingLog,
          };
        }
      );
      Logger.log("success", {
        message: "BookingService:initiateResumeBooking:$transaction:success",
        params: {
          userID: user.user_id,
          vehicleID: ongoingBooking.vehicle_id,
          ongoingBookingID: ongoingBooking.booking_id,
        },
      });
      return initiateResumeBookingTransaction;
    } catch (error) {
      Logger.log("error", {
        message: "BookingService:initiateResumeBooking:catch-1",
        params: { error },
      });
      throw error;
    }
  };

  /**
   *
   * @param {object} param0
   * @param {import("@prisma/client").tbl_users} param0.user
   * @param {import("@prisma/client").tbl_bookings} param0.ongoingBooking
   */
  static confirmResumeBooking = async ({ user, ongoingBooking }) => {
    try {
      Logger.log("info", {
        message: "BookingService:confirmResumeBooking:params",
        params: {
          userID: user.user_id,
          vehicleID: ongoingBooking.vehicle_id,
          ongoingBookingID: ongoingBooking.booking_id,
        },
      });
      const confirmResumeBookingTransaction = await prisma.$transaction(
        async (tx) => {
          const updatedVehicle = await tx.tbl_vehicles.update({
            where: {
              vehicle_id: parseInt(ongoingBooking.vehicle_id),
            },
            data: {
              vehicle_status: constants.VEHICLE_STATUS.RIDING,
            },
          });
          Logger.log("info", {
            message:
              "BookingService:confirmResumeBooking:$transaction:updatedVehicle",
            params: {
              userID: user.user_id,
              bookingID: ongoingBooking.booking_id,
              vehicleID: updatedVehicle.vehicle_id,
              vehicleStatusID: updatedVehicle.vehicle_status,
            },
          });

          const newBookingLog = await tx.tbl_booking_logs.create({
            data: {
              booking_id: ongoingBooking.booking_id,
              booking_action: constants.BOOKING_ACTIONS.RESUME,
              user_id: parseInt(user.user_id),
              lat: Number(parseFloat(updatedVehicle.lat).toFixed(10)),
              lng: Number(parseFloat(updatedVehicle.lng).toFixed(10)),
              battery: updatedVehicle.battery,
            },
          });
          Logger.log("info", {
            message:
              "BookingService:confirmResumeBooking:$transaction:newBookingLog",
            params: {
              userID: user.user_id,
              bookingID: ongoingBooking.booking_id,
              vehicleID: updatedVehicle.vehicle_id,
              bookingLogID: newBookingLog.booking_log_id,
            },
          });

          const updatedBooking = await tx.tbl_bookings.update({
            where: { booking_id: parseInt(ongoingBooking.booking_id) },
            data: {
              last_resume_at: new Date(),
              booking_status: constants.BOOKING_STATUS.RIDING,
            },
            include: {
              tbl_vehicles: true,
            },
          });
          Logger.log("info", {
            message:
              "BookingService:confirmResumeBooking:$transaction:updatedBooking",
            params: {
              userID: user.user_id,
              bookingID: updatedBooking.booking_id,
              vehicleID: updatedVehicle.vehicle_id,
              bookingLogID: newBookingLog.booking_log_id,
            },
          });

          return {
            booking: updatedBooking,
            vehicle: updatedVehicle,
            bookingLog: newBookingLog,
          };
        }
      );
      //update old vehicle database status
      try {
        await Bike.findOneAndUpdate(
          {
            np: confirmResumeBookingTransaction.vehicle.vehicle_number,
          },
          {
            status: BikeStatusEnum.Busy,
          }
        );
      } catch (error) {
        Logger.log("error", {
          message: "BookingService:confirmResumeBooking:catch-2",
          params: { error },
        });
      }
      Logger.log("success", {
        message: "BookingService:confirmResumeBooking:$transaction:success",
        params: {
          userID: user.user_id,
          vehicleID: ongoingBooking.vehicle_id,
          ongoingBookingID: ongoingBooking.booking_id,
        },
      });
      return confirmResumeBookingTransaction;
    } catch (error) {
      Logger.log("error", {
        message: "BookingService:confirmResumeBooking:catch-1",
        params: { error },
      });
      throw error;
    }
  };

  /**
   *
   * @param {object} param0
   * @param {import("@prisma/client").tbl_bookings} param0.ongoingBooking
   */
  static revertResumeBooking = async ({ ongoingBooking }) => {
    try {
      Logger.log("info", {
        message: "BookingService:revertResumeBooking:params",
        params: {
          vehicleID: ongoingBooking.vehicle_id,
          ongoingBookingID: ongoingBooking.booking_id,
        },
      });
      const revertResumeBookingTransaction = await prisma.$transaction(
        async (tx) => {
          const updatedVehicle = await tx.tbl_vehicles.update({
            where: {
              vehicle_id: parseInt(ongoingBooking.vehicle_id),
            },
            data: {
              vehicle_status: constants.VEHICLE_STATUS.PAUSED,
            },
          });
          Logger.log("info", {
            message:
              "BookingService:revertResumeBooking:$transaction:updatedVehicle",
            params: {
              bookingID: ongoingBooking.booking_id,
              vehicleID: updatedVehicle.vehicle_id,
              vehicleStatusID: updatedVehicle.vehicle_status,
            },
          });

          const updatedBooking = await tx.tbl_bookings.update({
            where: { booking_id: parseInt(ongoingBooking.booking_id) },
            data: {
              booking_status: constants.BOOKING_STATUS.PAUSED,
            },
            include: {
              tbl_vehicles: true,
              tbl_users: true,
            },
          });
          Logger.log("info", {
            message:
              "BookingService:revertResumeBooking:$transaction:updatedBooking",
            params: {
              bookingID: updatedBooking.booking_id,
              vehicleID: updatedVehicle.vehicle_id,
            },
          });

          return {
            booking: updatedBooking,
            vehicle: updatedVehicle,
          };
        }
      );
      Logger.log("success", {
        message: "BookingService:revertResumeBooking:$transaction:success",
        params: {
          vehicleID: ongoingBooking.vehicle_id,
          ongoingBookingID: ongoingBooking.booking_id,
        },
      });
      return revertResumeBookingTransaction;
    } catch (error) {
      Logger.log("error", {
        message: "BookingService:revertResumeBooking:catch-1",
        params: { error },
      });
      throw error;
    }
  };

  /**
   *
   * @param {object} param0
   * @param {import("@prisma/client").tbl_users} param0.user
   * @param {import("@prisma/client").tbl_bookings} param0.ongoingBooking
   * @param {Number} param0.rideEndStationID
   * @param {Number} param0.outstationDistance
   */
  static initiateEndBooking = async ({
    user,
    ongoingBooking,
    rideEndStationID,
    outstationDistance,
  }) => {
    try {
      Logger.log("info", {
        message: "BookingService:initiateEndBooking:params",
        params: {
          userID: user.user_id,
          vehicleID: ongoingBooking.vehicle_id,
          ongoingBookingID: ongoingBooking.booking_id,
          rideEndStationID,
          outstationDistance,
        },
      });
      await this.saveBookingPrevState({ bookingID: ongoingBooking.booking_id });
      await VehicleService.saveVehiclePrevState({
        vehicleID: ongoingBooking.vehicle_id,
      });

      const initiateEndBookingTransaction = await prisma.$transaction(
        async (tx) => {
          const updatedVehicle = await tx.tbl_vehicles.update({
            where: {
              vehicle_id: parseInt(ongoingBooking.vehicle_id),
            },
            data: {
              vehicle_status: constants.VEHICLE_STATUS.INITIATED_LOCK,
            },
          });
          Logger.log("info", {
            message:
              "BookingService:initiateEndBooking:$transaction:updatedVehicle",
            params: {
              userID: user.user_id,
              ongoingBookingID: ongoingBooking.booking_id,
              vehicleID: updatedVehicle.vehicle_id,
              vehicleStatus: updatedVehicle.vehicle_status,
            },
          });

          const newBookingLog = await tx.tbl_booking_logs.create({
            data: {
              booking_id: ongoingBooking.booking_id,
              booking_action: constants.BOOKING_ACTIONS.INITIATE_END,
              user_id: parseInt(user.user_id),
              lat: Number(parseFloat(updatedVehicle.lat).toFixed(10)),
              lng: Number(parseFloat(updatedVehicle.lng).toFixed(10)),
              battery: updatedVehicle.battery,
            },
          });
          Logger.log("info", {
            message:
              "BookingService:initiateEndBooking:$transaction:newBookingLog",
            params: {
              userID: user.user_id,
              ongoingBookingID: ongoingBooking.booking_id,
              vehicleID: updatedVehicle.vehicle_id,
              bookingLogID: newBookingLog.booking_log_id,
            },
          });

          const updatedBooking = await tx.tbl_bookings.update({
            where: { booking_id: parseInt(ongoingBooking.booking_id) },
            data: {
              booking_status: constants.BOOKING_STATUS.INITIATED_END,
              booking_end_station_id: parseInt(rideEndStationID),
              outstation_distance: outstationDistance,
            },
            include: {
              tbl_vehicles: true,
            },
          });
          Logger.log("info", {
            message:
              "BookingService:initiateEndBooking:$transaction:updatedBooking",
            params: {
              userID: user.user_id,
              bookingID: updatedBooking.booking_id,
              vehicleID: updatedVehicle.vehicle_id,
            },
          });

          return {
            booking: updatedBooking,
            vehicle: updatedVehicle,
            bookingLog: newBookingLog,
          };
        }
      );
      Logger.log("success", {
        message: "BookingService:initiateEndBooking:$transaction:success",
        params: {
          userID: user.user_id,
          vehicleID: ongoingBooking.vehicle_id,
          ongoingBookingID: ongoingBooking.booking_id,
        },
      });
      return initiateEndBookingTransaction;
    } catch (error) {
      Logger.log("error", {
        message: "BookingService:initiateEndBooking:catch-1",
        params: { error },
      });
      throw error;
    }
  };

  /**
   *
   * @param {object} param0
   * @param {import("@prisma/client").tbl_users} param0.user
   * @param {import("@prisma/client").tbl_bookings} param0.ongoingBooking
   */
  static confirmEndBooking = async ({ user, ongoingBooking }) => {
    try {
      Logger.log("info", {
        message: "BookingService:confirmEndBooking:params",
        params: {
          userID: user.user_id,
          vehicleID: ongoingBooking.vehicle_id,
          ongoingBookingID: ongoingBooking.booking_id,
          rideEndStationID: ongoingBooking.booking_end_station_id,
        },
      });
      const confirmEndBookingTransaction = await prisma.$transaction(
        async (tx) => {
          const updatedVehicle = await tx.tbl_vehicles.update({
            where: {
              vehicle_id: parseInt(ongoingBooking.vehicle_id),
            },
            data: {
              vehicle_status: constants.VEHICLE_STATUS.READY,
              station_id: ongoingBooking.booking_end_station_id,
            },
          });
          Logger.log("info", {
            message:
              "BookingService:confirmEndBooking:$transaction:updatedVehicle",
            params: {
              userID: user.user_id,
              ongoingBookingID: ongoingBooking.booking_id,
              vehicleID: updatedVehicle.vehicle_id,
              vehicleStatus: updatedVehicle.vehicle_status,
            },
          });

          const newBookingLog = await tx.tbl_booking_logs.create({
            data: {
              booking_id: ongoingBooking.booking_id,
              booking_action: constants.BOOKING_ACTIONS.END,
              user_id: parseInt(user.user_id),
              lat: Number(parseFloat(updatedVehicle.lat).toFixed(10)),
              lng: Number(parseFloat(updatedVehicle.lng).toFixed(10)),
              battery: updatedVehicle.battery,
            },
          });
          Logger.log("info", {
            message:
              "BookingService:confirmEndBooking:$transaction:newBookingLog",
            params: {
              userID: user.user_id,
              ongoingBookingID: ongoingBooking.booking_id,
              vehicleID: updatedVehicle.vehicle_id,
              bookingLogID: newBookingLog.booking_log_id,
            },
          });

          const updatedBooking = await tx.tbl_bookings.update({
            where: { booking_id: parseInt(ongoingBooking.booking_id) },
            data: {
              booking_end_at: new Date(),
              booking_status: constants.BOOKING_STATUS.ENDED,
            },
            include: {
              tbl_users: {
                include: {
                  tbl_location_offers_map: true,
                },
              },
              tbl_booking_logs: {
                orderBy: { created_at: "asc" },
              },
              tbl_vehicles: {
                include: {
                  tbl_vehicle_types: true,
                },
              },
            },
          });
          Logger.log("info", {
            message:
              "BookingService:confirmEndBooking:$transaction:updatedBooking",
            params: {
              userID: user.user_id,
              bookingID: updatedBooking.booking_id,
              vehicleID: updatedVehicle.vehicle_id,
              bookingLogID: newBookingLog.booking_log_id,
            },
          });

          return {
            booking: updatedBooking,
            vehicle: updatedVehicle,
            bookingLog: newBookingLog,
          };
        }
      );
      //update old vehicle database status
      try {
        await Bike.findOneAndUpdate(
          {
            np: confirmEndBookingTransaction.vehicle.vehicle_number,
          },
          {
            status: BikeStatusEnum.Idle,
          }
        );
      } catch (error) {
        Logger.log("error", {
          message: "BookingService:confirmEndBooking:catch-2",
          params: { error },
        });
      }
      Logger.log("success", {
        message: "BookingService:confirmEndBooking:$transaction:success",
        params: {
          userID: user.user_id,
          vehicleID: ongoingBooking.vehicle_id,
          ongoingBookingID: ongoingBooking.booking_id,
        },
      });
      await BookingReceiptService.generateBookingReceipt({
        booking: confirmEndBookingTransaction.booking,
      });
      return confirmEndBookingTransaction;
    } catch (error) {
      Logger.log("error", {
        message: "BookingService:confirmEndBooking:catch-1",
        params: { error },
      });
      throw error;
    }
  };

  /**
   *
   * @param {object} param0
   * @param {import("@prisma/client").tbl_bookings} param0.ongoingBooking
   * @param {Number} param0.rideEndStationID
   */
  static revertEndBooking = async ({ ongoingBooking }) => {
    try {
      Logger.log("info", {
        message: "BookingService:revertEndBooking:params",
        params: {
          vehicleID: ongoingBooking.vehicle_id,
          ongoingBookingID: ongoingBooking.booking_id,
        },
      });
      const vehiclePrevState = await prisma.tbl_vehicles_prev_state.findUnique({
        where: { vehicle_id: parseInt(ongoingBooking.vehicle_id) },
      });
      const bookingPrevState = await prisma.tbl_bookings_prev_state.findUnique({
        where: { booking_id: parseInt(ongoingBooking.booking_id) },
      });

      Logger.log("info", {
        message: "BookingService:revertEndBooking:params",
        params: {
          vehicleID: ongoingBooking.vehicle_id,
          ongoingBookingID: ongoingBooking.booking_id,
          vehiclePrevState: vehiclePrevState,
          bookingPrevState: bookingPrevState,
        },
      });
      const revertEndBookingTransaction = await prisma.$transaction(
        async (tx) => {
          const updatedVehicle = await tx.tbl_vehicles.update({
            where: {
              vehicle_id: parseInt(ongoingBooking.vehicle_id),
            },
            data: {
              vehicle_status: vehiclePrevState.vehicle_status,
            },
          });
          Logger.log("info", {
            message:
              "BookingService:revertEndBooking:$transaction:updatedVehicle",
            params: {
              ongoingBookingID: ongoingBooking.booking_id,
              vehicleID: updatedVehicle.vehicle_id,
              vehicleStatus: updatedVehicle.vehicle_status,
            },
          });

          const updatedBooking = await tx.tbl_bookings.update({
            where: { booking_id: parseInt(ongoingBooking.booking_id) },
            data: {
              booking_status: bookingPrevState.booking_status,
            },
            include: {
              tbl_vehicles: true,
              tbl_users: true,
            },
          });
          Logger.log("info", {
            message:
              "BookingService:revertEndBooking:$transaction:updatedBooking",
            params: {
              bookingID: updatedBooking.booking_id,
              vehicleID: updatedVehicle.vehicle_id,
            },
          });

          return {
            booking: updatedBooking,
            vehicle: updatedVehicle,
          };
        }
      );
      Logger.log("success", {
        message: "BookingService:revertEndBooking:$transaction:success",
        params: {
          vehicleID: ongoingBooking.vehicle_id,
          ongoingBookingID: ongoingBooking.booking_id,
        },
      });
      return revertEndBookingTransaction;
    } catch (error) {
      Logger.log("error", {
        message: "BookingService:revertEndBooking:catch-1",
        params: { error },
      });
      throw error;
    }
  };

  /**
   *
   * @param {object} param0
   * @param {import("@prisma/client").tbl_users} param0.user
   * @param {import("@prisma/client").tbl_bookings} param0.ongoingBooking
   */
  static initiateCancelBooking = async ({ user, ongoingBooking }) => {
    try {
      Logger.log("info", {
        message: "BookingService:initiateCancelBooking:params",
        params: {
          userID: user.user_id,
          vehicleID: ongoingBooking.vehicle_id,
          ongoingBookingID: ongoingBooking.booking_id,
        },
      });

      const initiateCancelBookingTransaction = await prisma.$transaction(
        async (tx) => {
          const updatedVehicle = await tx.tbl_vehicles.update({
            where: {
              vehicle_id: parseInt(ongoingBooking.vehicle_id),
            },
            data: {
              vehicle_status: constants.VEHICLE_STATUS.READY,
            },
          });
          Logger.log("info", {
            message:
              "BookingService:initiateCancelBooking:$transaction:updatedVehicle",
            params: {
              userID: user.user_id,
              ongoingBookingID: ongoingBooking.booking_id,
              vehicleID: updatedVehicle.vehicle_id,
              vehicleStatus: updatedVehicle.vehicle_status,
            },
          });

          const newBookingLog = await tx.tbl_booking_logs.create({
            data: {
              booking_id: ongoingBooking.booking_id,
              booking_action: constants.BOOKING_ACTIONS.CANCELLED,
              user_id: parseInt(user.user_id),
              lat: Number(parseFloat(updatedVehicle.lat).toFixed(10)),
              lng: Number(parseFloat(updatedVehicle.lng).toFixed(10)),
              battery: updatedVehicle.battery,
            },
          });
          Logger.log("info", {
            message:
              "BookingService:initiateCancelBooking:$transaction:newBookingLog",
            params: {
              userID: user.user_id,
              ongoingBookingID: ongoingBooking.booking_id,
              vehicleID: updatedVehicle.vehicle_id,
              bookingLogID: newBookingLog.booking_log_id,
            },
          });

          const updatedBooking = await tx.tbl_bookings.update({
            where: { booking_id: parseInt(ongoingBooking.booking_id) },
            data: {
              booking_status: constants.BOOKING_STATUS.CANCELLED,
            },
            include: {
              tbl_vehicles: true,
            },
          });
          Logger.log("info", {
            message:
              "BookingService:initiateCancelBooking:$transaction:updatedBooking",
            params: {
              userID: user.user_id,
              bookingID: updatedBooking.booking_id,
              vehicleID: updatedVehicle.vehicle_id,
            },
          });

          return {
            booking: updatedBooking,
            vehicle: updatedVehicle,
            bookingLog: newBookingLog,
          };
        }
      );
      try {
        await Bike.findOneAndUpdate(
          {
            np: initiateCancelBookingTransaction.vehicle.vehicle_number,
          },
          {
            status: BikeStatusEnum.Idle,
          }
        );
      } catch (error) {
        Logger.log("error", {
          message: "BookingService:initiateCancelBooking:catch-2",
          params: { error },
        });
      }
      Logger.log("success", {
        message: "BookingService:initiateCancelBooking:$transaction:success",
        params: {
          userID: user.user_id,
          vehicleID: ongoingBooking.vehicle_id,
          ongoingBookingID: ongoingBooking.booking_id,
        },
      });
      return initiateCancelBookingTransaction;
    } catch (error) {
      Logger.log("error", {
        message: "BookingService:initiateCancelBooking:catch-1",
        params: { error },
      });
      throw error;
    }
  };
}

module.exports = { BookingService };
