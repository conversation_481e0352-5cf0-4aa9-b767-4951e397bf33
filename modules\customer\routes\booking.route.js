const express = require("express");
const router = express.Router();

const authMiddleware = require("../middlewares/auth.middleware");
const checkMiddleware = require("../middlewares/check.middleware");
const validationMiddleware = require("../middlewares/validation.middleware");
const bookingController = require("../controllers/booking.controller");
const {
  interceptorMiddleware,
} = require("../middlewares/interceptor.middleware");

router.get("/", authMiddleware, bookingController.getCompletedBookings);
router.get(
  "/ongoing",
  authMiddleware,
  checkMiddleware.isBooked,
  bookingController.ongoingBooking
);
router.get("/:id", authMiddleware, bookingController.getBookingByID);

router.post(
  "/start",
  authMiddleware,
  validationMiddleware.checkVehicleFieldInBody,
  checkMiddleware.isProfileCompleted,
  checkMiddleware.isWalletActivated,
  checkMiddleware.isSecurityDepositDone,
  checkMiddleware.isSufficientWalletBalance,
  checkMiddleware.isNotBooked,
  interceptorMiddleware.provideVehicle,
  checkMiddleware.isNotReserved,
  checkMiddleware.isVehicleAvailable,
  bookingController.start
);

router.post(
  "/pause",
  authMiddleware,
  checkMiddleware.isBooked,
  checkMiddleware.isBookingRiding,
  bookingController.pause
);

router.post(
  "/resume",
  authMiddleware,
  checkMiddleware.isBooked,
  checkMiddleware.isBookingPaused,
  bookingController.resume
);

router.post(
  "/check_outstation",
  authMiddleware,
  checkMiddleware.isBooked,
  interceptorMiddleware.provideIfCancelOrEndBooking,
  (req, res) => {
    if (req.isEndRide) {
      return bookingController.getOutstationStatus(req, res);
    } else {
      return res.json({ success: true, outstation: false });
    }
  }
);

router.post(
  "/check_outstation_v2",
  authMiddleware,
  checkMiddleware.isBooked,
  interceptorMiddleware.provideIfCancelOrEndBooking,
  (req, res) => {
    // if (req.isEndRide) {
    //   return bookingController.getOutstationStatus(req, res);
    // } else {
    //   return res.json({ success: true, outstation: false });
    // }
    return res.json({ success: true, outstation: false });
  }
);

router.post(
  "/end",
  authMiddleware,
  checkMiddleware.isBooked,
  interceptorMiddleware.provideIfCancelOrEndBooking,
  (req, res, next) => {
    if (req.isEndRide) {
      return interceptorMiddleware.provideVehicleOutstationStatus(
        req,
        res,
        next
      );
    } else {
      return next();
    }
  },
  (req, res) => {
    try {
      if (req.isEndRide) {
        return bookingController.end(req, res);
      } else {
        return bookingController.cancel(req, res);
      }
    } catch (error) {}
  }
);

module.exports = router;
