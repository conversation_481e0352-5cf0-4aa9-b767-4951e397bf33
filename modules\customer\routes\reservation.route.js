const express = require("express");
const router = express.Router();

const authMiddleware = require("../middlewares/auth.middleware");
const checkMiddleware = require("../middlewares/check.middleware");
const validationMiddleware = require("../middlewares/validation.middleware");

const {
  interceptorMiddleware,
} = require("../middlewares/interceptor.middleware");
const {
  reservationController,
} = require("../controllers/reservation.controller");

router.get("/", authMiddleware, reservationController.getCompletedReservations);
router.get(
  "/ongoing",
  authMiddleware,
  checkMiddleware.isReserved,
  reservationController.ongoingReservation
);
router.get("/:id", authMiddleware, reservationController.getReservationByID);

router.post(
  "/reserve",
  authMiddleware,
  validationMiddleware.checkVehicleFieldInBody,
  checkMiddleware.isProfileCompleted,
  checkMiddleware.isWalletActivated,
  checkMiddleware.isSecurityDepositDone,
  checkMiddleware.isSufficientWalletBalance,
  interceptorMiddleware.provideVehicle,
  checkMiddleware.isNotBooked,
  checkMiddleware.isNotReserved,
  checkMiddleware.isVehicleAvailable,

  reservationController.reserveVehicle
);

router.post(
  "/end",
  authMiddleware,
  checkMiddleware.isReserved,
  reservationController.endReservation
);

module.exports = router;
