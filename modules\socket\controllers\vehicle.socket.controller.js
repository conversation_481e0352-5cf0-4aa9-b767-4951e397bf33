const { prisma } = require("../../../config/prisma");
const { socketIO } = require("../../../config/socket.io");
const constants = require("../../../constants");
const Logger = require("../../../utils/logger");

const vehicleSocketController = {};

/**
 *
 * @param {object} param0
 * @param {String} param0.firebaseID
 */
vehicleSocketController.emitVehiclesOnFirstConnect = async ({ firebaseID }) => {
  try {
    Logger.log("info", {
      message: "vehicleSocketController:emitVehiclesOnFirstConnect:params",
      params: { firebaseID },
    });
    const user = await prisma.tbl_users.findFirst({
      where: {
        firebase_id: firebaseID,
      },
    });
    Logger.log("info", {
      message: "vehicleSocketController:emitVehiclesOnFirstConnect:user",
      params: { userID: user.user_id, firebaseID },
    });

    if (user && user.last_lat && user.last_lng) {
      const vehicles = await prisma.tbl_vehicles.findMany({
        where: {
          AND: [
            {
              lat: {
                gte:
                  parseFloat(user.last_lat) - constants.VEHICLE_RENDER_RADIUS,
              },
            },
            {
              lat: {
                lte:
                  parseFloat(user.last_lat) + constants.VEHICLE_RENDER_RADIUS,
              },
            },
            {
              lng: {
                gte:
                  parseFloat(user.last_lng) - constants.VEHICLE_RENDER_RADIUS,
              },
            },
            {
              lng: {
                lte:
                  parseFloat(user.last_lng) + constants.VEHICLE_RENDER_RADIUS,
              },
            },
          ],
          is_disabled: false,
          vehicle_status: constants.VEHICLE_STATUS.READY,
        },
        include: {
          tbl_vehicle_types: true,
        },
      });
      Logger.log("info", {
        message: "vehicleSocketController:emitVehiclesOnFirstConnect:vehicles",
        params: { userID: user.user_id, firebaseID, vehicles: vehicles.length },
      });
      socketIO
        .to(firebaseID)
        .emit(constants.SOCKET_EVENTS.ON_VEHICLES_UPDATE, vehicles);
      Logger.log("success", {
        message: "vehicleSocketController:emitVehiclesOnFirstConnect:success",
        params: { userID: user.user_id, firebaseID, vehicles: vehicles.length },
      });
    } else {
      Logger.log("error", {
        message: "vehicleSocketController:emitVehiclesOnFirstConnect:catch-2",
        params: { firebaseID, error: constants.ERROR_CODES.INVALID_USER },
      });
    }
  } catch (error) {
    Logger.log("error", {
      message: "vehicleSocketController:emitVehiclesOnFirstConnect:catch-1",
      params: { error },
    });
  }
};

/**
 *
 * @param {object} param0
 * @param {import("@prisma/client").tbl_users} param0.user
 * @param {Number} param0.latitude
 * @param {Number} param0.longitude
 */
vehicleSocketController.emitVehiclesOnUserGeolocationUpdate = async ({
  user,
  latitude,
  longitude,
}) => {
  try {
    Logger.log("info", {
      message:
        "vehicleSocketController:emitVehiclesOnUserGeolocationUpdate:params",
      params: { userID: user.user_id, latitude, longitude },
    });
    const vehicles = await prisma.tbl_vehicles.findMany({
      where: {
        AND: [
          { lat: { gte: parseFloat(latitude) - 0.03 } },
          { lat: { lte: parseFloat(latitude) + 0.03 } },
          { lng: { gte: parseFloat(longitude) - 0.03 } },
          { lng: { lte: parseFloat(longitude) + 0.03 } },
        ],
      },
      include: {
        tbl_vehicle_types: true,
      },
    });
    Logger.log("info", {
      message: "vehicleSocketController:emitVehiclesOnFirstConnect:vehicles",
      params: {
        userID: user.user_id,
        firebaseID: user.firebase_id,
        vehicles: vehicles.length,
      },
    });
    socketIO
      .to(user.firebase_id)
      .emit(constants.SOCKET_EVENTS.ON_VEHICLES_UPDATE, vehicles);
    Logger.log("success", {
      message:
        "vehicleSocketController:emitVehiclesOnUserGeolocationUpdate:success",
      params: { userID: user.user_id, firebaseID: user.firebase_id },
    });
  } catch (error) {
    Logger.log("error", {
      message:
        "vehicleSocketController:emitVehiclesOnUserGeolocationUpdate:catch-1",
      params: { error },
    });
  }
};

module.exports = vehicleSocketController;
