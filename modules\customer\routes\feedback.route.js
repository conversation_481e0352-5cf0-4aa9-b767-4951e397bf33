const express = require("express");
const feedbackController = require("../controllers/feedback.controller");
const router = express.Router();
const authMiddleware = require("../middlewares/auth.middleware");
const checkMiddleware = require("../middlewares/check.middleware");

//vehicle routes

// get all vehicles details
router.post(
  "/review",
  authMiddleware,
  checkMiddleware.isProfileCompleted,
  checkMiddleware.isWalletActivated,
  checkMiddleware.isBookingEnded,
  feedbackController.submitReview
);

router.get(
  "/support_ticket",
  authMiddleware,
  feedbackController.getAllSupportTickets
);

router.get(
  "/support_ticket/:support_ticket_id",
  authMiddleware,
  feedbackController.getSupportTicketMessage
);

router.post(
  "/support_ticket",
  authMiddleware,
  feedbackController.logSupportTicket
);

router.post(
  "/support_ticket/:support_ticket_id",
  authMiddleware,
  feedbackController.postSupportTicketMessage
);

// get vehicle details by id

module.exports = router;
