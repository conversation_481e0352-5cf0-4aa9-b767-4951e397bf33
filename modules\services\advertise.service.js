const { prisma } = require("../../config/prisma");
const constants = require("../../constants");
const Logger = require("../../utils/logger");

class AdvertiseService {
  constructor() {}

  /**
   *
   * @param {object} param0
   * @param {import("@prisma/client").tbl_users} param0.user
   * @returns
   */
  static getUserAdvertises = async ({ user }) => {
    try {
      Logger.log("info", {
        message: "AdvertiseService:getUserAdvertises:params",
        params: { userID: user.user_id, adUserMapID: user.ad_user_map_id },
      });

      const advertises = await prisma.tbl_advertises.findMany({
        where: {
          ad_user_map_id: user.ad_user_map_id
            ? user.ad_user_map_id
            : constants.DEFAULT_AD_USER_MAP_ID,
        },
      });

      Logger.log("success", {
        message: "AdvertiseService:getUserAdvertises:vehicle",
        params: {
          userID: user.user_id,
          adUserMapID: user.ad_user_map_id,
          advertises,
        },
      });
      return advertises;
    } catch (error) {
      Logger.log("error", {
        message: "AdvertiseService:getUserAdvertises:catch-1",
        params: { error },
      });
      return null;
    }
  };
}

module.exports = { AdvertiseService };
