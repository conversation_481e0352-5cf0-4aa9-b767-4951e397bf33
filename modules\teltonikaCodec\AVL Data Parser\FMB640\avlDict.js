"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.avlidDictionary = void 0;
/**
* Dictionary for avl name, given the AVL ID.
* For some cases, the value is a dictionary containing the avl name and the respective table name.
* When the table name is 0, it means that there is not table for that avl.
*/
exports.avlidDictionary = {
    1: "Digital Input 1",
    2: "Digital Input 2",
    3: "Digital Input 3",
    4: "Digital Input 4",
    5: "Dallas Temperature ID 5",
    9: "Analog Input 1",
    10: "Analog Input 2",
    11: "Analog Input 3",
    21: "GSM Signal",
    22: "Data Mode",
    24: "Speed",
    50: "Digital Output 3",
    51: "Digital Output 4",
    66: "External Voltage",
    67: "Battery Voltage",
    68: "Battery Current",
    70: "PCB Temperature",
    71: "GNSS Status",
    72: "Dallas Temperature 1",
    78: "iButton",
    79: "Brake Switch",
    80: "Wheel Based Speed",
    81: "Cruise Control Active",
    82: "Clutch Switch",
    83: "PTO State",
    84: "Acceleration Pedal Position",
    85: "Engine Current Load",
    86: "Engine Total Fuel Used",
    87: "Fuel Level",
    88: "Engine Speed",
    89: "Axle weight 1",
    90: "Axle weight 2",
    91: "Axle weight 3",
    92: "Axle weight 4",
    93: "Axle weight 5",
    94: "Axle weight 6",
    95: "Axle weight 7",
    96: "Axle weight 8",
    97: "Axle weight 9",
    98: "Axle weight 10",
    99: "Axle weight 11",
    100: "Axle weight 12",
    101: "Axle weight 13",
    102: "Axle weight 14",
    103: "Axle weight 15",
    104: "Engine Total Hours Of Operation",
    110: "Diagnostics Supported",
    113: "Service Distance",
    122: "Direction Indication",
    123: "Tachograph Performance",
    124: "Handling Info",
    125: "System Event",
    127: "Engine Coolant Temperature",
    128: "Ambient Air Temperature",
    135: "Fuel Rate",
    136: "Instantaneous Fuel Economy",
    137: "PTO Drive Engagement",
    138: "High Resolution Engine Total Fuel Used",
    139: "Gross Combination Vehicle Weight",
    144: "SD Status",
    178: 'Network Type',
    179: "Digital Output 1",
    180: "Digital Output 2",
    181: "GNSS PDOP",
    182: "GNSS HDOP",
    199: "Trip Odometer",
    200: "Sleep Mode",
    205: "GSM Cell ID",
    206: "GSM Area Code",
    216: "Total Odometer",
    218: 'IMSI',
    219: 'CCID Part1',
    220: 'CCID Part2',
    221: 'CCID Part3',
    236: "Axis X",
    237: "Axis Y",
    238: "Axis Z",
    239: "Ignition",
    240: "Movement",
    241: "Active GSM Operator",
    243: "Idling",
    245: "Analog Input 4",
    246: "Towing",
    247: "Crash Detection",
    249: "Jamming",
    251: "Immobilizer",
    253: "Green Driving Type",
    255: "Over Speeding",
    10348: "Fuel level 2",
    10349: "MIL indicator",
    10428: "Tell Tale 0",
    10429: "Tell Tale 1",
    10430: "Tell Tale 2",
    10431: "Tell Tale 3",
};
