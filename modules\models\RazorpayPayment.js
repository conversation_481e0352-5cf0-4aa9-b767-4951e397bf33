class RazorpayPayment {
  constructor({
    id,
    amount,
    currency,
    status,
    method,
    order_id,
    refund_status,
    amount_refunded,
    captured,
    contact,
    fee,
    tax,
    error_code,
    error_description,
    created_at,
    card_id,
    card,
    bank,
    wallet,
    email,
    vpa,
  }) {
    this.id = id;
    this.amount = amount;
    this.currency = currency;
    this.status = status;
    this.method = method;
    this.order_id = order_id;
    this.refund_status = refund_status;
    this.amount_refunded = amount_refunded;
    this.captured = captured;
    this.contact = contact;
    this.fee = fee;
    this.tax = tax;
    this.error_code = error_code;
    this.error_description = error_description;
    this.created_at = created_at;
    this.card_id = card_id;
    this.card = card;
    this.vpa = vpa;
    this.bank = bank;
    this.wallet = wallet;
    this.email = email;
  }
}

module.exports = { RazorpayPayment };
