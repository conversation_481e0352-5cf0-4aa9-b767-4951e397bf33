// @ts-nocheck
const validator = require("validator");
const constants = require("../../../constants");
const validationMiddleware = {};

const validationErrors = {
  INVALID_USERDATA: {
    code: "INVALID_USERDATA",
    message: "User data is not valid",
  },
  BAD_REQUEST: {
    code: "BAD_REQUEST",
    message: "bad request error",
  },
  INVALID_EMAIL: {
    code: "INVALID_EMAIL",
    message: "Email not valid",
  },
  INVALID_DEVICE_TOKEN: {
    code: "INVALID_DEVICE_TOKEN",
    message: "Device token not valid",
  },
  INVALID_TRANSACTION_ID: {
    code: "INVALID_TRANSACTION_ID",
    message: "Transaction id not valid",
  },
  INVALID_VEHICLE_ID: {
    code: "INVALID_VEHICLE_ID",
    message: "Vehcile id not valid",
  },
  INVALID_STATION_ID: {
    code: "INVALID_STATION_ID",
    message: "Station id not valid",
  },
  INVALID_COUPON_ID: {
    code: "INVALID_COUPON_ID",
    message: "Coupon params not valid",
  },
  INVALID_SUBSCRIPTION_ID: {
    code: "INVALID_SUBSCRIPTION_ID",
    message: "Subscription params not valid",
  },
  INVALID_TRANSACTION_ID: {
    code: "INVALID_TRANSACTION_ID",
    message: "Invalid transaction ID",
  },
  INVALID_RECHARGE_AMOUNT: {
    code: "INVALID_RECHARGE_AMOUNT",
    message: "Wallet recharge amount should be more than 0.30 gpg",
  },
};

/**
 *
 * @param {import("express").Request} req
 * @param {import("express").Response} res
 * @param {import("express").NextFunction} next
 * @returns
 */
validationMiddleware.checkUserUpdateParams = async (req, res, next) => {
  if (!req || !req.body) {
    return res.json({ error: validationErrors.BAD_REQUEST });
  }
  let validated = true;
  const { email, firstName, lastName, address1, address2 } = req.body;

  if (
    email &&
    (validator.isEmpty(String(email)) || !validator.isEmail(String(email)))
  ) {
    Logger.pageLogger(
      "validationMiddleware:checkUserUpdateParams:email:failed"
    );
    validated = false;
  }
  if (firstName && validator.isEmpty(String(firstName))) {
    Logger.pageLogger(
      "validationMiddleware:checkUserUpdateParams:firstName:failed"
    );
    validated = false;
  }
  if (lastName && validator.isEmpty(String(lastName))) {
    Logger.pageLogger(
      "validationMiddleware:checkUserUpdateParams:lastName:failed"
    );
    validated = false;
  }
  if (address1 && validator.isEmpty(String(address1))) {
    Logger.pageLogger(
      "validationMiddleware:checkUserUpdateParams:address1:failed"
    );
    validated = false;
  }

  if (validated) {
    return next();
  } else {
    return res.json({ error: validationErrors.INVALID_USERDATA });
  }
};

/**
 *
 * @param {import("express").Request} req
 * @param {import("express").Response} res
 * @param {import("express").NextFunction} next
 * @returns
 */
validationMiddleware.checkHistoryParam = async (req, res, next) => {
  if (!req || !req.params) {
    return res.json({ error: validationErrors.BAD_REQUEST });
  }
  const { master_id } = req.params;
  if (master_id && validator.isNumeric(String(master_id))) {
    return next();
  }
  return res.json({ error: validationErrors.INVALID_TRANSACTION_ID });
};

/**
 *
 * @param {import("express").Request} req
 * @param {import("express").Response} res
 * @param {import("express").NextFunction} next
 * @returns
 */
validationMiddleware.checkRechargeAmount = async (req, res, next) => {
  if (!req || !req.body) {
    return res.json({ error: validationErrors.BAD_REQUEST });
  }
  const { amount } = req.body;
  if (
    amount &&
    (validator.isInt(String(amount)) || validator.isFloat(String(amount))) &&
    parseFloat(String(amount)) >= constants.WALLET_BALANCE_THRESHOLD
  ) {
    return next();
  }
  return res.json({ error: validationErrors.INVALID_RECHARGE_AMOUNT });
};

/**
 *
 * @param {import("express").Request} req
 * @param {import("express").Response} res
 * @param {import("express").NextFunction} next
 * @returns
 */
validationMiddleware.checkEmailField = async (req, res, next) => {
  if (!req || !req.body) {
    return res.json({ error: validationErrors.BAD_REQUEST });
  }
  const { email } = req.body;
  if (email && validator.isEmail(String(email))) {
    return next();
  }
  return res.json({ error: validationErrors.INVALID_EMAIL });
};

/**
 *
 * @param {import("express").Request} req
 * @param {import("express").Response} res
 * @param {import("express").NextFunction} next
 * @returns
 */
validationMiddleware.checkDeviceTokenField = async (req, res, next) => {
  if (!req || !req.body) {
    return res.json({ error: validationErrors.BAD_REQUEST });
  }
  const { device_token } = req.body;
  if (device_token) {
    return next();
  }

  return res.json({ error: validationErrors.INVALID_DEVICE_TOKEN });
};

/**
 *
 * @param {import("express").Request} req
 * @param {import("express").Response} res
 * @param {import("express").NextFunction} next
 * @returns
 */
validationMiddleware.checkVehicleIDInParam = async (req, res, next) => {
  if (!req || !req.params) {
    return res.json({ error: validationErrors.BAD_REQUEST });
  }
  const { id } = req.params;
  if (id && validator.isNumeric(String(id))) {
    return next();
  }
  return res.json({ error: validationErrors.INVALID_VEHICLE_ID });
};

/**
 *
 * @param {import("express").Request} req
 * @param {import("express").Response} res
 * @param {import("express").NextFunction} next
 * @returns
 */
validationMiddleware.checkVehicleCodeInParam = async (req, res, next) => {
  if (!req || !req.params) {
    return res.json({ error: validationErrors.BAD_REQUEST });
  }
  const { vehicle_code } = req.params;
  if (vehicle_code && validator.isAlphanumeric(String(vehicle_code))) {
    return next();
  }
  return res.json({ error: validationErrors.INVALID_VEHICLE_ID });
};

/**
 *
 * @param {import("express").Request} req
 * @param {import("express").Response} res
 * @param {import("express").NextFunction} next
 * @returns
 */
validationMiddleware.checkStationIdParam = async (req, res, next) => {
  if (!req || !req.params) {
    return res.json({ error: validationErrors.BAD_REQUEST });
  }
  const { id } = req.params;
  if (id && validator.isNumeric(String(id))) {
    return next();
  }
  return res.json({ error: validationErrors.INVALID_STATION_ID });
};

/**
 *
 * @param {import("express").Request} req
 * @param {import("express").Response} res
 * @param {import("express").NextFunction} next
 * @returns
 */
validationMiddleware.checkCouponParam = async (req, res, next) => {
  if (!req || !req.params) {
    return res.json({ error: validationErrors.BAD_REQUEST });
  }
  const { coupon_id, coupon_type_id } = req.params;
  if (
    coupon_id &&
    coupon_type_id &&
    validator.isNumeric(String(coupon_id)) &&
    validator.isNumeric(String(coupon_type_id))
  ) {
    return next();
  }
  return res.json({ error: validationErrors.INVALID_COUPON_ID });
};

/**
 *
 * @param {import("express").Request} req
 * @param {import("express").Response} res
 * @param {import("express").NextFunction} next
 * @returns
 */
validationMiddleware.checkSubscriptionParam = async (req, res, next) => {
  if (!req || !req.params) {
    return res.json({ error: validationErrors.BAD_REQUEST });
  }
  const { subscription_id } = req.params;
  if (subscription_id && validator.isNumeric(String(subscription_id))) {
    return next();
  }
  return res.json({ error: validationErrors.INVALID_SUBSCRIPTION_ID });
};

/**
 *
 * @param {import("express").Request} req
 * @param {import("express").Response} res
 * @param {import("express").NextFunction} next
 * @returns
 */
validationMiddleware.checkSubscriptionTypeField = async (req, res, next) => {
  if (!req || !req.body) {
    return res.json({ error: validationErrors.BAD_REQUEST });
  }
  const { subscription_type_id } = req.body;
  if (
    subscription_type_id &&
    validator.isNumeric(String(subscription_type_id))
  ) {
    return next();
  }
  return res.json({ error: validationErrors.INVALID_SUBSCRIPTION_ID });
};

/**
 *
 * @param {import("express").Request} req
 * @param {import("express").Response} res
 * @param {import("express").NextFunction} next
 * @returns
 */
validationMiddleware.checkSubscriptionTypeParam = async (req, res, next) => {
  if (!req || !req.params) {
    return res.json({ error: validationErrors.BAD_REQUEST });
  }
  const { subscription_type_id } = req.params;
  if (
    subscription_type_id &&
    validator.isNumeric(String(subscription_type_id))
  ) {
    return next();
  }
  return res.json({ error: validationErrors.INVALID_SUBSCRIPTION_ID });
};

/**
 *
 * @param {import("express").Request} req
 * @param {import("express").Response} res
 * @param {import("express").NextFunction} next
 * @returns
 */
validationMiddleware.checkVehicleFieldInBody = async (req, res, next) => {
  if (!req || !req.body) {
    return res.json({ error: validationErrors.BAD_REQUEST });
  } else {
    const { vehicle_id } = req.body;
    if (vehicle_id && validator.isNumeric(String(vehicle_id))) {
      return next();
    } else {
      return res.json({ error: validationErrors.INVALID_VEHICLE_ID });
    }
  }
};

/**
 *
 * @param {import("express").Request} req
 * @param {import("express").Response} res
 * @param {import("express").NextFunction} next
 * @returns
 */
validationMiddleware.checkVehicleTypeFieldInBody = async (req, res, next) => {
  if (!req || !req.body) {
    return res.json({ error: validationErrors.BAD_REQUEST });
  } else {
    const { vehicle_type_id } = req.body;
    if (vehicle_type_id && validator.isNumeric(String(vehicle_type_id))) {
      return next();
    } else {
      return res.json({ error: validationErrors.INVALID_VEHICLE_ID });
    }
  }
};

/**
 *
 * @param {import("express").Request} req
 * @param {import("express").Response} res
 * @param {import("express").NextFunction} next
 * @returns
 */
validationMiddleware.checkCouponField = async (req, res, next) => {
  if (!req || !req.body) {
    return res.json({ error: validationErrors.BAD_REQUEST });
  }
  const { coupon_id, coupon_type_id } = req.body;
  if (!coupon_id && !coupon_type_id) {
    return next();
  } else if (
    coupon_id &&
    coupon_type_id &&
    validator.isNumeric(String(coupon_id)) &&
    validator.isNumeric(String(coupon_type_id))
  ) {
    return next();
  }
  return res.json({ error: validationErrors.INVALID_COUPON_ID });
};

/**
 *
 * @param {import("express").Request} req
 * @param {import("express").Response} res
 * @param {import("express").NextFunction} next
 * @returns
 */
validationMiddleware.checkSubscriptionField = async (req, res, next) => {
  if (!req || !req.body) {
    return res.json({ error: validationErrors.BAD_REQUEST });
  }
  const { subscription_id } = req.body;
  if (!subscription_id) {
    return next();
  } else if (subscription_id && validator.isNumeric(String(subscription_id))) {
    return next();
  }
  return res.json({ error: validationErrors.INVALID_SUBSCRIPTION_ID });
};

/**
 *
 * @param {import("express").Request} req
 * @param {import("express").Response} res
 * @param {import("express").NextFunction} next
 * @returns
 */
validationMiddleware.checkSubscriptionFieldInBody = async (req, res, next) => {
  if (!req || !req.body) {
    return res.json({ error: validationErrors.BAD_REQUEST });
  }
  const { subscription_id } = req.body;
  if (!subscription_id || !validator.isNumeric(String(subscription_id))) {
    return res.json({ error: validationErrors.INVALID_SUBSCRIPTION_ID });
  }
  return next();
};

module.exports = validationMiddleware;
