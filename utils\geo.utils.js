const convertHexToWgs84 = (hexLat, hexLng) => {
  // Convert hex values to decimal degrees
  const lat = parseInt(hexLat, 16) / 30000;
  const lng = parseInt(hexLng, 16) / 30000;

  const lat_deg = Math.floor(Math.floor(lat) / 60);
  const lng_deg = Math.floor(Math.floor(lng) / 60);

  const lat_dec = lat - lat_deg * 60;
  const lng_dec = lng - lng_deg * 60;

  const latitude = lat_deg + lat_dec / 60;
  const longitude = lng_deg + lng_dec / 60;

  return { latitude, longitude };
};

const convertHexToWgs84Latitude = (hexLat) => {
  // Convert hex values to decimal degrees
  const lat = parseInt(hexLat, 16) / 30000;

  const lat_deg = Math.floor(Math.floor(lat) / 60);

  const lat_dec = lat - lat_deg * 60;

  const latitude = lat_deg + lat_dec / 60;

  return latitude;
};

const convertHexToWgs84Longitude = (hexLng) => {
  // Convert hex values to decimal degrees
  const lng = parseInt(hexLng, 16) / 30000;

  const lng_deg = Math.floor(Math.floor(lng) / 60);

  const lng_dec = lng - lng_deg * 60;

  const longitude = lng_deg + lng_dec / 60;

  return longitude;
};

const isCoordinateInsidePolygon = ({ lat, lng }, polygon) => {
  var inside = false;
  for (var i = 0, j = polygon.length - 1; i < polygon.length; j = i++) {
    var xi = polygon[i][0],
      yi = polygon[i][1];
    var xj = polygon[j][0],
      yj = polygon[j][1];

    var intersect =
      yi > lng != yj > lng &&
      lat <
        parseFloat(
          Number(
            parseFloat(Number((xj - xi) * (lng - yi)).toFixed(8)) / (yj - yi) +
              xi
          ).toFixed(8)
        );
    if (intersect) inside = !inside;
  }

  return inside;
};

module.exports = {
  convertHexToWgs84,
  convertHexToWgs84Latitude,
  convertHexToWgs84Longitude,
  isCoordinateInsidePolygon,
};
