const Logger = require("../../../utils/logger");
const { extractError } = require("../../../utils/error.utils");
const { AdvertiseService } = require("../../services/advertise.service");

const advertiseController = {};

/**
 *
 * @param {import("express").Request} req
 * @param {import("express").Response} res
 * @returns
 */
advertiseController.getUserAdvertises = async (req, res) => {
  try {
    const { user } = req;
    const advertises = await AdvertiseService.getUserAdvertises({ user });
    return res.json({ success: true, advertises });
  } catch (error) {
    Logger.log("error", {
      message: "advertiseController:getUserAdvertises:catch-1",
      params: { error },
    });
    return res.json({ success: false, error: extractError(error) });
  }
};

module.exports = advertiseController;
