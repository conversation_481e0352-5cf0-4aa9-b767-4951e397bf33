const Logger = require("../../../utils/logger");
const { extractError } = require("../../../utils/error.utils");
const { prisma } = require("../../../config/prisma");
const userSocketController = require("../../socket/controllers/user.socket.controller");
const constants = require("../../../constants");
const { BookingService } = require("../../services/booking.service");
const bookingSocketController = require("../../socket/controllers/booking.socket.controller");
const { VehicleService } = require("../../services/vehicle.service");
const { ReservationService } = require("../../services/reservation.service");
const { IOTService } = require("../../services/iot.service");
const { default: axios } = require("axios");
const calculations = require("../../../utils/calculations");
const {
  reservationSocketController,
} = require("../../socket/controllers/reservation.socket.controller");

const bookingController = {};

/**
 *
 * @param {import("express").Request} req
 * @param {import("express").Response} res
 * @returns
 */
bookingController.getOutstationStatus = async (req, res) => {
  try {
    const { user, ongoingBooking, body } = req;
    Logger.log("info", {
      message: "bookingController:getOutstationStatus:params",
      params: { user, ongoingBooking, body },
    });
    const vehicle = await prisma.tbl_vehicles.findUnique({
      where: {
        vehicle_id: parseInt(ongoingBooking.vehicle_id),
      },
    });
    const stations = await prisma.tbl_stations.findMany();
    const { minimunDistance, nearestStationID } = calculations.nearestStation(
      stations,
      body.lat && body.lng ? { lat: body.lat, lng: body.lng } : vehicle
    );
    Logger.log("info", {
      message: "bookingController:getOutstationStatus:nearestStation",
      params: { userID: user.user_id, minimunDistance, nearestStationID },
    });
    if (minimunDistance < constants.OUTSTATION_THRESHOLD) {
      Logger.log("info", {
        message: "bookingController:getOutstationStatus:success",
        params: { userID: user.user_id, minimunDistance, nearestStationID },
      });
      return res.json({ success: true, outstation: false });
    } else {
      Logger.log("error", {
        message: "bookingController:getOutstationStatus:outstation",
        params: { userID: user.user_id },
      });
      return res.json({
        success: true,
        outstation: true,
        distance: minimunDistance,
        charge: minimunDistance * constants.DEFAULT_OUTSTATION_PER_KM_CHARGE,
      });
    }
  } catch (error) {
    Logger.log("error", {
      message: "bookingController:getOutstationStatus:catch-1",
      params: { error },
    });
    return res.json({
      success: false,
      error: constants.ERROR_CODES.SERVER_ERROR,
    });
  }
};
/**
 *
 * @param {import("express").Request} req
 * @param {import("express").Response} res
 * @returns
 */
bookingController.ongoingBooking = async (req, res) => {
  try {
    const { user, ongoingBooking } = req;
    Logger.log("info", {
      message: "bookingController:ongoingBooking:params",
      params: { userID: user.user_id },
    });

    return res.json({ success: true, ongoingBooking });
  } catch (error) {
    Logger.log("error", {
      message: "bookingController:ongoingBooking:catch-1",
      params: { error },
    });
    return res.json({ success: false, error: extractError(error) });
  }
};

/**
 *
 * @param {import("express").Request} req
 * @param {import("express").Response} res
 * @returns
 */
bookingController.getCompletedBookings = async (req, res) => {
  try {
    const { user } = req;
    const { page } = req.query;
    Logger.log("info", {
      message: "bookingController:getCompletedBookings:params",
      params: { userID: user.user_id, page },
    });
    let skip = 0;
    let take = constants.BOOKING_HISTORY_PAGE_SIZE;
    if (page) {
      skip = (parseInt(page) - 1) * constants.BOOKING_HISTORY_PAGE_SIZE;
      take = constants.BOOKING_HISTORY_PAGE_SIZE;
    }

    const bookings = await BookingService.getCompletedUserBookings({
      user,
      skip,
      take,
    });

    Logger.log("success", {
      message: "bookingController:getCompletedBookings:bookings",
      params: { userID: user.user_id, bookings: bookings.length },
    });
    return res.json({
      success: true,
      bookings,
      nextPage:
        bookings.length < constants.BOOKING_HISTORY_PAGE_SIZE
          ? undefined
          : parseInt(parseInt(page) + 1),
    });
  } catch (error) {
    Logger.log("error", {
      message: "bookingController:getCompletedBookings:catch-1",
      params: { error },
    });
    return res.json({ success: false, error: extractError(error) });
  }
};

/**
 *
 * @param {import("express").Request} req
 * @param {import("express").Response} res
 * @returns
 */
bookingController.getBookingByID = async (req, res) => {
  try {
    const { user } = req;
    const { id } = req.params;

    Logger.log("info", {
      message: "bookingController:getBookingByID:params",
      params: { userID: user.user_id },
    });

    const booking = await BookingService.getUserBookingByID({
      user,
      bookingID: parseInt(id),
    });

    Logger.log("success", {
      message: "bookingController:getBookingByID:bookings",
      params: { userID: user.user_id, bookingID: booking.booking_id },
    });
    return res.json({
      success: true,
      booking,
    });
  } catch (error) {
    Logger.log("error", {
      message: "bookingController:getBookingByID:catch-1",
      params: { error },
    });
    return res.json({ success: false, error: extractError(error) });
  }
};

/**
 *
 * @param {import("express").Request} req
 * @param {import("express").Response} res
 * @returns
 */
bookingController.start = async (req, res) => {
  try {
    const { user, vehicle, ongoingReservation } = req;
    const { plan_id } = req.body;
    Logger.log("info", {
      message: "bookingController:start:params",
      params: {
        userID: user.user_id,
        vehicle: vehicle.vehicle_id,
        plan_id,
      },
    });
    if (ongoingReservation) {
      Logger.log("info", {
        message: "bookingController:start:ongoingReservation",
        params: {
          userID: user.user_id,
          ongoingReservationID: ongoingReservation.reservation_id,
        },
      });
      const endReservationTransaction = await ReservationService.endReservation(
        {
          user,
          reservation: ongoingReservation,
        }
      );
      await reservationSocketController.emitOngoingReservationOnUpdate({
        ongoingReservation: endReservationTransaction.reservation,
        firebaseID: user.firebase_id,
      });
      Logger.log("success", {
        message: "bookingController:start:ongoingReservation:cancelled",
        params: {
          userID: user.user_id,
          ongoingReservationID: ongoingReservation.reservation_id,
        },
      });
    }
    const initiateStartBookingTransaction =
      await BookingService.initiateStartBooking({
        user,
        vehicle,
        planID: parseInt(plan_id),
      });

    Logger.log("success", {
      message: "bookingController:start:success",
      params: {
        userID: user.user_id,
        initiateStartBookingTransactionID:
          initiateStartBookingTransaction.booking.booking_id,
        isOldIot: vehicle.is_old_iot,
      },
    });
    if (false) {
      bookingSocketController.emitOngoingBookingOnUpdate({
        ongoingBooking: initiateStartBookingTransaction.booking,
        firebaseID: user.firebase_id,
      });
      IOTService.sendCommand({
        imei: initiateStartBookingTransaction.vehicle.iot_imei,
        command: constants.IOT_COMMANDS.VEHICLE_START.command,
        bookingLogID: initiateStartBookingTransaction.bookingLog.booking_log_id,
      });
      return res.json({
        success: true,
        booking: initiateStartBookingTransaction.booking,
      });
    } else {
      const confirmStartBookingTransaction =
        await BookingService.confirmStartBooking({
          ongoingBooking: initiateStartBookingTransaction.booking,
          user,
        });
      Logger.log("success", {
        message: "bookingController:start:success",
        params: {
          userID: user.user_id,
          confirmStartBookingTransactionID:
            confirmStartBookingTransaction.booking.booking_id,
        },
      });
      // bookingSocketController.emitOngoingBookingOnUpdate({
      //   ongoingBooking: confirmStartBookingTransaction.booking,
      //   firebaseID: user.firebase_id,
      // });
      return res.json({
        success: true,
        booking: confirmStartBookingTransaction.booking,
      });
    }
  } catch (error) {
    Logger.log("error", {
      message: "bookingController:start:catch-1",
      params: { error },
    });
    return res.json({ success: false, error: extractError(error) });
  }
};

/**
 *
 * @param {import("express").Request} req
 * @param {import("express").Response} res
 * @returns
 */
bookingController.pause = async (req, res) => {
  try {
    const { user, ongoingBooking } = req;
    Logger.log("info", {
      message: "bookingController:pause:params",
      params: {
        userID: user.user_id,
        vehicle: ongoingBooking.vehicle_id,
        ongoingBooking: ongoingBooking.booking_id,
      },
    });
    const initiatePauseBookingTransaction =
      await BookingService.initiatePauseBooking({
        user,
        ongoingBooking,
      });
    Logger.log("success", {
      message: "bookingController:pause:success",
      params: {
        userID: user.user_id,
        initiatePauseBookingTransactionID:
          initiatePauseBookingTransaction.booking.booking_id,
        isOldIot: ongoingBooking.tbl_vehicles.is_old_iot,
      },
    });
    if (false) {
      bookingSocketController.emitOngoingBookingOnUpdate({
        ongoingBooking: initiatePauseBookingTransaction.booking,
        firebaseID: user.firebase_id,
      });
      IOTService.sendCommand({
        imei: initiatePauseBookingTransaction.vehicle.iot_imei,
        command: constants.IOT_COMMANDS.VEHICLE_STOP.command,
        bookingLogID: initiatePauseBookingTransaction.bookingLog.booking_log_id,
      });
      return res.json({
        success: true,
        booking: initiatePauseBookingTransaction.booking,
      });
    } else {
      const confirmPauseBookingTransaction =
        await BookingService.confirmPauseBooking({
          ongoingBooking: initiatePauseBookingTransaction.booking,
          user,
        });
      Logger.log("success", {
        message: "bookingController:pause:success",
        params: {
          userID: user.user_id,
          confirmPauseBookingTransactionID:
            confirmPauseBookingTransaction.booking.booking_id,
        },
      });
      // bookingSocketController.emitOngoingBookingOnUpdate({
      //   ongoingBooking: confirmPauseBookingTransaction.booking,
      //   firebaseID: user.firebase_id,
      // });
      return res.json({
        success: true,
        booking: confirmPauseBookingTransaction.booking,
      });
    }
  } catch (error) {
    Logger.log("error", {
      message: "bookingController:pause:catch-1",
      params: { error },
    });
    return res.json({ success: false, error: extractError(error) });
  }
};

/**
 *
 * @param {import("express").Request} req
 * @param {import("express").Response} res
 * @returns
 */
bookingController.resume = async (req, res) => {
  try {
    const { user, ongoingBooking } = req;
    Logger.log("info", {
      message: "bookingController:resume:params",
      params: {
        userID: user.user_id,
        vehicle: ongoingBooking.vehicle_id,
        ongoingBooking: ongoingBooking.booking_id,
        req: req,
      },
    });
    const initiateResumeBookingTransaction =
      await BookingService.initiateResumeBooking({
        user,
        ongoingBooking,
      });
    Logger.log("success", {
      message: "bookingController:resume:success",
      params: {
        userID: user.user_id,
        initiateResumeBookingTransactionID:
          initiateResumeBookingTransaction.booking.booking_id,
        isOldIot: ongoingBooking.tbl_vehicles.is_old_iot,
      },
    });
    if (false) {
      bookingSocketController.emitOngoingBookingOnUpdate({
        ongoingBooking: initiateResumeBookingTransaction.booking,
        firebaseID: user.firebase_id,
      });
      IOTService.sendCommand({
        imei: initiateResumeBookingTransaction.vehicle.iot_imei,
        command: constants.IOT_COMMANDS.VEHICLE_START.command,
        bookingLogID:
          initiateResumeBookingTransaction.bookingLog.booking_log_id,
      });
      return res.json({
        success: true,
        booking: initiateResumeBookingTransaction.booking,
      });
    } else {
      const confirmResumeBookingTransaction =
        await BookingService.confirmResumeBooking({
          user,
          ongoingBooking: initiateResumeBookingTransaction.booking,
        });
      Logger.log("success", {
        message: "bookingController:resume:success",
        params: {
          userID: user.user_id,
          confirmResumeBookingTransactionID:
            confirmResumeBookingTransaction.booking.booking_id,
        },
      });
      // bookingSocketController.emitOngoingBookingOnUpdate({
      //   ongoingBooking: confirmResumeBookingTransaction.booking,
      //   firebaseID: user.firebase_id,
      // });
      return res.json({
        success: true,
        booking: confirmResumeBookingTransaction.booking,
      });
    }
  } catch (error) {
    Logger.log("error", {
      message: "bookingController:resume:catch-1",
      params: { error },
    });
    return res.json({ success: false, error: extractError(error) });
  }
};

/**
 *
 * @param {import("express").Request} req
 * @param {import("express").Response} res
 * @returns
 */
bookingController.end = async (req, res) => {
  try {
    const { user, ongoingBooking, rideEndStationID, minimunDistance } = req;
    Logger.log("info", {
      message: "bookingController:end:params",
      params: {
        userID: user.user_id,
        vehicle: ongoingBooking.vehicle_id,
        ongoingBooking: ongoingBooking.booking_id,
        rideEndStationID,
        minimunDistance,
      },
    });
    const initiateEndBookingTransaction =
      await BookingService.initiateEndBooking({
        user,
        ongoingBooking,
        rideEndStationID: parseInt(rideEndStationID),
        outstationDistance: parseFloat(minimunDistance),
      });

    Logger.log("success", {
      message: "bookingController:end:success",
      params: {
        userID: user.user_id,
        initiateEndBookingTransactionID:
          initiateEndBookingTransaction.booking.booking_id,
        isOldIot: ongoingBooking.tbl_vehicles.is_old_iot,
      },
    });
    if (false) {
      bookingSocketController.emitOngoingBookingOnUpdate({
        ongoingBooking: initiateEndBookingTransaction.booking,
        firebaseID: user.firebase_id,
      });
      IOTService.sendCommand({
        imei: initiateEndBookingTransaction.vehicle.iot_imei,
        command: constants.IOT_COMMANDS.VEHICLE_STOP.command,
        bookingLogID: initiateEndBookingTransaction.bookingLog.booking_log_id,
      });

      return res.json({
        success: true,
        booking: initiateEndBookingTransaction.booking,
      });
    } else {
      const confirmEndBookingTransaction =
        await BookingService.confirmEndBooking({
          user,
          ongoingBooking: initiateEndBookingTransaction.booking,
          rideEndStationID: parseInt(rideEndStationID),
        });
      Logger.log("success", {
        message: "bookingController:end:success",
        params: {
          userID: user.user_id,
          confirmEndBookingTransactionID:
            confirmEndBookingTransaction.booking.booking_id,
        },
      });
      // bookingSocketController.emitOngoingBookingOnUpdate({
      //   ongoingBooking: confirmEndBookingTransaction.booking,
      //   firebaseID: user.firebase_id,
      // });
      return res.json({
        success: true,
        booking: confirmEndBookingTransaction.booking,
      });
    }
  } catch (error) {
    Logger.log("error", {
      message: "bookingController:end:catch-1",
      params: { error },
    });
    return res.json({ success: false, error: extractError(error) });
  }
};

/**
 *
 * @param {import("express").Request} req
 * @param {import("express").Response} res
 * @returns
 */
bookingController.cancel = async (req, res) => {
  try {
    const { user, ongoingBooking } = req;
    Logger.log("info", {
      message: "bookingController:cancel:params",
      params: {
        userID: user.user_id,
        vehicle: ongoingBooking.vehicle_id,
        ongoingBooking: ongoingBooking.booking_id,
      },
    });
    const initiateCancelBookingTransaction =
      await BookingService.initiateCancelBooking({
        user,
        ongoingBooking,
      });

    Logger.log("success", {
      message: "bookingController:cancel:success",
      params: {
        userID: user.user_id,
        initiateCancelBookingTransactionID:
          initiateCancelBookingTransaction.booking.booking_id,
      },
    });
    // bookingSocketController.emitOngoingBookingOnUpdate({
    //   ongoingBooking: initiateCancelBookingTransaction.booking,
    //   firebaseID: user.firebase_id,
    // });
    // IOTService.sendCommand({
    //   imei: initiateCancelBookingTransaction.vehicle.iot_imei,
    //   command: constants.IOT_COMMANDS.VEHICLE_STOP.command,
    // }).catch((error) => {});
    return res.json({
      success: true,
      booking: initiateCancelBookingTransaction.booking,
    });
  } catch (error) {
    Logger.log("error", {
      message: "bookingController:cancel:catch-1",
      params: { error },
    });
    return res.json({ success: false, error: extractError(error) });
  }
};

module.exports = bookingController;
