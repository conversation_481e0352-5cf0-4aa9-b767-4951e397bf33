
const Logger = require("../../../utils/logger");
const { extractError } = require("../../../utils/error.utils");
const { prisma } = require("../../../config/prisma");
const userSocketController = require("../../socket/controllers/user.socket.controller");
const constants = require("../../../constants");
const { VehicleService } = require("../../services/vehicle.service");
const { IOTService } = require("../../services/iot.service");

const vehicleController = {};

/**
 *
 * @param {import("express").Request} req
 * @param {import("express").Response} res
 * @param {import("express").NextFunction} next
 */
vehicleController.sendCommand = async (req, res, next) => {
  try {
    const { vehicle_id, command } = req.body;
    const vehicle = await VehicleService.getVehicleByID({ vehicle_id });
    if (vehicle === null || vehicle === undefined || !vehicle) {
      Logger.log("error", {
        message: "vehicleController:sendCommand:catch-1",
        params: { error: constants.ERROR_CODES.INVALID_VEHICLE_ID },
      });
      return res.json({
        success: false,
        error: constants.ERROR_CODES.INVALID_VEHICLE_ID,
      });
    }
    IOTService.sendCommand({
      imei: vehicle.iot_imei,
      command: constants.IOT_COMMANDS.VEHICLE_START.command,
      bookingLogID: initiateStartBookingTransaction.bookingLog.booking_log_id,
    });
    
    Logger.log("success", {
      message: "vehicleController:sendCommand:success",
      params: { vehicle_id, command },
    });
    return res.json({ success: true });
  } catch (error) {
    Logger.log("error", {
      message: "vehicleController:sendCommand:catch-3",
      params: { error },
    });
    return res.json({ success: false, error: extractError(error) });
  }
};

module.exports = vehicleController;
