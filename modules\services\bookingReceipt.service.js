const { prisma } = require("../../config/prisma");
const constants = require("../../constants");
const Logger = require("../../utils/logger");
const moment = require("moment");
const { BookingReceipt } = require("../models/BookingReceipt");
const { OfferService } = require("./offer.service");
const lodash = require("lodash");
const { WalletService } = require("./wallet.service");
const { BookingLogService } = require("./bookingLog.service");

class BookingReceiptService {
  constructor() {}

  /**
   *
   * @param {object} param0
   * @param {import("@prisma/client").tbl_bookings & {tbl_vehicles:import("@prisma/client").tbl_vehicles & {tbl_vehicle_types:import("@prisma/client").tbl_vehicle_types}} & {tbl_booking_logs:Array<import("@prisma/client").tbl_booking_logs>} & {tbl_users:import("@prisma/client").tbl_users & {tbl_location_offers_map: import("@prisma/client").tbl_location_offers_map}}} param0.booking
   * @param {BookingReceipt} param0.bookingReceipt
   */
  static applyPlanToReceipt = async ({ booking, bookingReceipt }) => {
    let planAppliedReceipt = lodash.cloneDeep(bookingReceipt);
    try {
      Logger.log("info", {
        message: "BookingReceiptService:applyPlanToReceipt:params",
        params: {
          bookingID: booking.booking_id,
          bookingReceipt,
        },
      });
      const selectedPlan = await OfferService.getPlanToApplyOnBookingReceipt({
        booking,
      });
      Logger.log("info", {
        message: "BookingReceiptService:applyPlanToReceipt:params",
        params: {
          bookingReceipt,
          bookingID: booking.booking_id,
          selectedPlan,
        },
      });
      planAppliedReceipt.plan = selectedPlan;
      planAppliedReceipt.plan_id = selectedPlan.plan_id;
      return planAppliedReceipt;
    } catch (error) {
      Logger.log("error", {
        message: "BookingReceiptService:applyPlanToReceipt:catch-1",
        params: {
          error,
        },
      });
      return planAppliedReceipt;
    }
  };

  /**
   *
   * @param {object} param0
   * @param {import("@prisma/client").tbl_bookings & {tbl_vehicles:import("@prisma/client").tbl_vehicles & {tbl_vehicle_types:import("@prisma/client").tbl_vehicle_types}} & {tbl_booking_logs:Array<import("@prisma/client").tbl_booking_logs>} & {tbl_users:import("@prisma/client").tbl_users & {tbl_location_offers_map: import("@prisma/client").tbl_location_offers_map}}} param0.booking
   * @param {BookingReceipt} param0.bookingReceipt
   */
  static applySubscriptionToReceipt = async ({ booking, bookingReceipt }) => {
    let subscriptionAppliedReceipt = lodash.cloneDeep(bookingReceipt);
    try {
      Logger.log("info", {
        message: "BookingReceiptService:applySubscriptionToReceipt:params",
        params: {
          bookingID:booking.booking_id,
          bookingReceipt,
        },
      });
      const selectedSubscription =
        await OfferService.getSubscriptionToApplyOnBookingReceipt({
          booking,
        });
      Logger.log("info", {
        message:
          "BookingReceiptService:applySubscriptionToReceipt:selectedSubscription",
        params: {
          bookingID: booking.booking_id,
          bookingReceipt,
          selectedSubscription,
        },
      });
      subscriptionAppliedReceipt.subscription_id =
        selectedSubscription.subscription_id;
      subscriptionAppliedReceipt.subscription = selectedSubscription;
      return subscriptionAppliedReceipt;
    } catch (error) {
      Logger.log("error", {
        message: "BookingReceiptService:applySubscriptionToReceipt:catch-1",
        params: {
          error,
        },
      });
      return subscriptionAppliedReceipt;
    }
  };

  /**
   *
   * @param {object} param0
   * @param {import("@prisma/client").tbl_bookings & {tbl_vehicles:import("@prisma/client").tbl_vehicles & {tbl_vehicle_types:import("@prisma/client").tbl_vehicle_types}} & {tbl_booking_logs:Array<import("@prisma/client").tbl_booking_logs>} & {tbl_users:import("@prisma/client").tbl_users & {tbl_location_offers_map: import("@prisma/client").tbl_location_offers_map}}} param0.booking
   */
  static generateBookingReceipt = async ({ booking }) => {
    try {
      Logger.log("info", {
        message: "BookingReceiptService:generateBookingReceipt:params",
        params: {
          userID: booking.user_id,
          bookingID: booking.booking_id,
        },
      });
      // calculate pause time of booking
      const _bookingLogs = booking.tbl_booking_logs.sort((a, b) => {
        let t = new Date(b.created_at) - new Date(a.created_at);
      });

      const _selectedPlanID = booking.user_selected_plan_id;

      Logger.log("info", {
        message: "BookingReceiptService:generateBookingReceipt:params",
        params: {
          userID: booking.user_id,
          bookingID: booking.booking_id,
          _selectedPlanID,
        },
      });

      const { bookingRideTime, bookingPauseTime } =
        await BookingLogService.calculateBookingRideAndPauseTimeFromStartAndEndTime(
          {
            bookingLogs: _bookingLogs,
            consideredStartTime: booking.booking_start_at,
            consideredEndTime: booking.booking_end_at,
          }
        );
      let totalBookingTime = moment(booking.booking_end_at).diff(
        moment(booking.booking_start_at)
      );
      const newBookingReceipt = new BookingReceipt({
        initial_pause_time: bookingPauseTime,
        initial_ride_time: bookingRideTime,
        initial_booking_time: totalBookingTime,

        total_pause_time_after_plan: bookingPauseTime,
        total_ride_time_after_plan: bookingRideTime,
        total_booking_time_after_plan: totalBookingTime,

        total_pause_time_after_subscription: bookingPauseTime,
        total_ride_time_after_subscription: bookingRideTime,
        total_booking_time_after_subscription: totalBookingTime,

        ride_time_charge: booking?.tbl_vehicles?.tbl_vehicle_types
          ?.ride_time_charge
          ? booking.tbl_vehicles.tbl_vehicle_types.ride_time_charge
          : constants.DEFAULT_RIDE_TIME_CHARGE,
        pause_time_charge: booking?.tbl_vehicles?.tbl_vehicle_types
          ?.pause_time_charge
          ? booking.tbl_vehicles.tbl_vehicle_types.pause_time_charge
          : constants.DEFAULT_PAUSE_TIME_CHARGE,

        outstation_charge:
          booking.tbl_vehicles.station_id === constants.OUTSTATION_ID
            ? constants.DEFAULT_OUTSTATION_PER_KM_CHARGE *
              booking.outstation_distance
            : 0,
        unlock_charge: booking?.tbl_vehicles?.tbl_vehicle_types?.unlock_charge
          ? booking.tbl_vehicles.tbl_vehicle_types.unlock_charge
          : constants.DEFAULT_UNLOCK_CHARGE,
        booking_start_at: booking.booking_start_at,
        booking_end_at: booking.booking_end_at,
        tax_rate: constants.DEFAULT_TAX_PERCENTAGE,
        tax: 0,
        final_booking_fare: 0,
        final_pause_time_fare: 0,
        final_ride_time_fare: 0,
        final_unlock_fare: 0,
        subscription_id: null,
        subscription: null,
        plan_id: null,
        plan: null,
      });

      newBookingReceipt.initialCalculation();

      let bookingReceiptAfterPlan = null;
      let bookingReceiptAfterSubscription = null;

      if (_selectedPlanID) {
        const processedBookingReceipt1 = await this.applyPlanToReceipt({
          booking,
          bookingReceipt: newBookingReceipt,
        });

        Logger.log("info", {
          message:
            "BookingReceiptService:generateBookingReceipt:processedBookingReceipt1",
          params: {
            userID: booking.user_id,
            bookingID: booking.booking_id,
            bookingReceipt: processedBookingReceipt1,
          },
        });

        const processedBookingReceipt2 =
          await this.processAppliedPlanForBookingReceipt({
            booking,
            bookingLogs: _bookingLogs,
            bookingReceipt: processedBookingReceipt1,
          });

        Logger.log("info", {
          message:
            "BookingReceiptService:generateBookingReceipt:processedBookingReceipt2",
          params: {
            userID: booking.user_id,
            bookingID: booking.booking_id,
            bookingReceipt: processedBookingReceipt2,
          },
        });
        bookingReceiptAfterPlan = lodash.cloneDeep(processedBookingReceipt2);
      } else {
        bookingReceiptAfterPlan = lodash.cloneDeep(newBookingReceipt);
      }
      Logger.log("info", {
        message:
          "BookingReceiptService:generateBookingReceipt:bookingReceiptAfterPlan",
        params: {
          userID: booking.user_id,
          bookingID: booking.booking_id,
          bookingReceiptAfterPlan: bookingReceiptAfterPlan,
        },
      });

      const processedBookingReceipt3 = await this.applySubscriptionToReceipt({
        booking,
        bookingReceipt: bookingReceiptAfterPlan,
      });

      Logger.log("info", {
        message:
          "BookingReceiptService:generateBookingReceipt:processedBookingReceipt3",
        params: {
          userID: booking.user_id,
          bookingID: booking.booking_id,
          bookingReceipt: processedBookingReceipt3,
        },
      });

      if (processedBookingReceipt3.subscription) {
        const processedBookingReceipt4 =
          await this.processAppliedSubscriptionForBookingReceipt({
            booking,
            bookingLogs: _bookingLogs,
            bookingReceipt: processedBookingReceipt3,
          });

        Logger.log("info", {
          message:
            "BookingReceiptService:generateBookingReceipt:processedBookingReceipt4",
          params: {
            userID: booking.user_id,
            bookingID: booking.booking_id,
            bookingReceipt: processedBookingReceipt4,
          },
        });
        bookingReceiptAfterSubscription = lodash.cloneDeep(
          processedBookingReceipt4
        );
      } else {
        processedBookingReceipt3.total_booking_time_after_subscription =
          processedBookingReceipt3.total_booking_time_after_plan;
        processedBookingReceipt3.total_ride_time_after_subscription =
          processedBookingReceipt3.total_ride_time_after_plan;
        processedBookingReceipt3.total_pause_time_after_subscription =
          processedBookingReceipt3.total_pause_time_after_plan;
        bookingReceiptAfterSubscription = lodash.cloneDeep(
          processedBookingReceipt3
        );
      }

      Logger.log("info", {
        message:
          "BookingReceiptService:generateBookingReceipt:bookingReceiptAfterSubscription",
        params: {
          userID: booking.user_id,
          bookingID: booking.booking_id,
          bookingReceiptAfterSubscription: bookingReceiptAfterSubscription,
        },
      });

      const finalBookingReceipt = BookingReceipt.finalCalculate({
        bookingReceipt: bookingReceiptAfterSubscription,
      });

      Logger.log("info", {
        message:
          "BookingReceiptService:generateBookingReceipt:finalBookingReceipt",
        params: {
          userID: booking.user_id,
          bookingID: booking.booking_id,
          bookingReceiptAfterSubscription,
          finalBookingReceipt,
        },
      });

      const {
        initialCalculation,
        initialCalculationAfterPlanApplied,
        initialCalculationAfterSubscriptionApplied,
        finalCalculate,
        booking_receipt_id,

        total_booking_time_after_subscription,
        total_pause_time_after_subscription,
        total_ride_time_after_subscription,

        final_unlock_fare,
        final_ride_time_fare,
        final_pause_time_fare,
        final_booking_fare,

        total_booking_time_after_plan,
        total_pause_time_after_plan,
        total_ride_time_after_plan,
        subscription,
        plan,
        coupon,
        subscription_id,
        plan_id,
        coupon_id,

        ...finalBookingReceiptData
      } = finalBookingReceipt;
      await prisma.tbl_bookings.update({
        where: {
          booking_id: parseInt(booking.booking_id),
        },
        data: {
          tbl_booking_receipts: {
            create: {
              ...finalBookingReceiptData,
              total_booking_time: total_booking_time_after_subscription,
              total_pause_time: total_pause_time_after_subscription,
              total_ride_time: total_ride_time_after_subscription,

              final_unlock_fare,
              final_ride_time_fare,
              final_pause_time_fare,
              final_booking_fare,

              coupon_id,
              subscription_id,
              plan_id,
            },
          },
        },
      }),
        // deduct final amount from wallet

        await WalletService.payBookingPaymentFromWallet({
          bookingID: booking.booking_id,
          userID: booking.user_id,
        });
      return finalBookingReceipt;
    } catch (error) {
      Logger.log("error", {
        message: "BookingReceiptService:generateBookingReceipt:catch-1",
        params: {
          error,
        },
      });
      return null;
    }
  };

  /**
   *
   * @param {object} param0
   * @param {import("@prisma/client").tbl_bookings & {tbl_vehicles:import("@prisma/client").tbl_vehicles & {tbl_vehicle_types:import("@prisma/client").tbl_vehicle_types}} & {tbl_booking_logs:Array<import("@prisma/client").tbl_booking_logs>} & {tbl_users:import("@prisma/client").tbl_users & {tbl_location_offers_map: import("@prisma/client").tbl_location_offers_map}}} param0.booking
   * @param {Array<import("@prisma/client").tbl_booking_logs>} param0.bookingLogs
   * @param {BookingReceipt} param0.bookingReceipt
   */
  // assuming that coupons are valid and ready to be applied
  static processAppliedPlanForBookingReceipt = async ({
    booking,
    bookingLogs,
    bookingReceipt,
  }) => {
    let processedBookingReceipt = lodash.cloneDeep(bookingReceipt);

    try {
      Logger.log("info", {
        message:
          "BookingReceiptService:processAppliedPlanForBookingReceipt:params",
        params: {
          booking,
          bookingLogs,
        },
      });

      const appliedPlan = bookingReceipt.plan;

      if (!appliedPlan) {
        Logger.log("info", {
          message:
            "BookingReceiptService:processAppliedPlanForBookingReceipt:no plan to apply",
          params: {
            bookingID: booking.booking_id,
            processedBookingReceipt,
          },
        });

        return processedBookingReceipt;
      }

      const {
        bookingRideTime: bookingRideTimeWithinPlanValidityDuration,
        bookingPauseTime: bookingPauseTimeWithinPlanValidityDuration,
      } =
        await BookingLogService.calculateBookingRideAndPauseTimeFromStartAndEndTime(
          {
            bookingLogs,
            consideredStartTime: booking.booking_start_at,
            consideredEndTime: appliedPlan.validity,
          }
        );
      const totalBookingTimeWithinPlanValidityDuration =
        bookingRideTimeWithinPlanValidityDuration +
        bookingPauseTimeWithinPlanValidityDuration;

      Logger.log("info", {
        message:
          "BookingReceiptService:processAppliedPlanForBookingReceipt:totalBookingTimeWithinPlanValidityDuration",
        params: {
          bookingID: booking.booking_id,
          bookingPauseTimeWithinPlanValidityDuration,
          bookingRideTimeWithinPlanValidityDuration,
          totalBookingTimeWithinPlanValidityDuration,
          processedBookingReceipt,
        },
      });

      const totalDeductedBookingTime =
        totalBookingTimeWithinPlanValidityDuration;

      processedBookingReceipt.total_pause_time_after_plan =
        processedBookingReceipt.initial_pause_time -
        bookingPauseTimeWithinPlanValidityDuration;
      processedBookingReceipt.total_ride_time_after_plan =
        processedBookingReceipt.initial_ride_time -
        bookingRideTimeWithinPlanValidityDuration;
      processedBookingReceipt.total_booking_time_after_plan =
        processedBookingReceipt.initial_booking_time -
        totalBookingTimeWithinPlanValidityDuration;

      const updatedPlan = await prisma.tbl_plans.update({
        where: {
          plan_id: appliedPlan.plan_id,
        },
        data: {
          remaining_quantity: {
            decrement: moment
              .duration(totalDeductedBookingTime, "milliseconds")
              .asMinutes(),
          },
        },
      });

      processedBookingReceipt.initialCalculationAfterPlanApplied();

      Logger.log("info", {
        message:
          "BookingReceiptService:processAppliedPlanForBookingReceipt:updatedPlan",
        params: {
          bookingID: booking.booking_id,
          processedBookingReceipt,
          updatedPlan,
        },
      });

      return processedBookingReceipt;
    } catch (error) {
      Logger.log("error", {
        message:
          "BookingReceiptService:processAppliedPlanForBookingReceipt:catch-1",
        params: {
          error,
        },
      });
      return processedBookingReceipt;
    }
  };

  /**
   *
   * @param {object} param0
   * @param {import("@prisma/client").tbl_bookings & {tbl_vehicles:import("@prisma/client").tbl_vehicles & {tbl_vehicle_types:import("@prisma/client").tbl_vehicle_types}} & {tbl_booking_logs:Array<import("@prisma/client").tbl_booking_logs>} & {tbl_users:import("@prisma/client").tbl_users & {tbl_location_offers_map: import("@prisma/client").tbl_location_offers_map}}} param0.booking
   * @param {Array<import("@prisma/client").tbl_booking_logs>} param0.bookingLogs
   * @param {BookingReceipt} param0.bookingReceipt
   */
  // assuming that coupons are valid and ready to be applied
  static processAppliedSubscriptionForBookingReceipt = async ({
    booking,
    bookingLogs,
    bookingReceipt,
  }) => {
    let processedBookingReceipt = lodash.cloneDeep(bookingReceipt);

    try {
      Logger.log("info", {
        message:
          "BookingReceiptService:processAppliedSubscriptionForBookingReceipt:params",
        params: {
          booking,
          bookingLogs,
          bookingReceipt,
        },
      });

      const appliedSubscription = bookingReceipt.subscription;

      if (
        bookingReceipt.total_booking_time_after_plan > 0 &&
        appliedSubscription.remaining_free_booking_time
      ) {
        const maxTimeAsPerFreeRideTime = bookingReceipt.plan
          ? moment(bookingReceipt.plan.validity).add(
              appliedSubscription.remaining_free_booking_time,
              "minutes"
            )
          : moment(booking.booking_start_at).add(
              appliedSubscription.remaining_free_booking_time,
              "minutes"
            );
        Logger.log("info", {
          message:
            "BookingReceiptService:processAppliedSubscriptionForBookingReceipt:maxTimeAsPerFreeRideTime",
          params: {
            bookingID: booking.booking_id,
            plan: bookingReceipt.plan,
            remaining_free_booking_time:
              appliedSubscription.remaining_free_booking_time,
            maxTimeAsPerFreeRideTime,
          },
        });
        const {
          bookingPauseTime:
            bookingPauseTimeWithinSubscriptionFreeTimeValidityDuration,
          bookingRideTime:
            bookingRideTimeWithinSubscriptionFreeTimeValidityDuration,
        } =
          await BookingLogService.calculateBookingRideAndPauseTimeFromStartAndEndTime(
            {
              bookingLogs,
              consideredStartTime: bookingReceipt.plan
                ? bookingReceipt.plan.validity
                : booking.booking_start_at,
              consideredEndTime: maxTimeAsPerFreeRideTime,
            }
          );
        const totalBookingTimeWithinSubscriptionFreeTimeValidityDuration =
          bookingRideTimeWithinSubscriptionFreeTimeValidityDuration +
          bookingPauseTimeWithinSubscriptionFreeTimeValidityDuration;

        Logger.log("info", {
          message:
            "BookingReceiptService:processAppliedSubscriptionForBookingReceipt:totalBookingTime",
          params: {
            bookingID: booking.booking_id,
            bookingRideTimeWithinSubscriptionFreeTimeValidityDuration,
            bookingPauseTimeWithinSubscriptionFreeTimeValidityDuration,
            totalBookingTimeWithinSubscriptionFreeTimeValidityDuration,
            processedBookingReceipt,
          },
        });

        const totalDeductedBookingTime =
          totalBookingTimeWithinSubscriptionFreeTimeValidityDuration;

        processedBookingReceipt.total_pause_time_after_subscription =
          processedBookingReceipt.total_pause_time_after_plan -
          bookingPauseTimeWithinSubscriptionFreeTimeValidityDuration;
        processedBookingReceipt.total_ride_time_after_subscription =
          processedBookingReceipt.total_ride_time_after_plan -
          bookingRideTimeWithinSubscriptionFreeTimeValidityDuration;
        processedBookingReceipt.total_booking_time_after_subscription =
          processedBookingReceipt.total_booking_time_after_plan -
          totalBookingTimeWithinSubscriptionFreeTimeValidityDuration;

        const updatedSubscription = await prisma.tbl_subscriptions.update({
          where: {
            subscription_id: appliedSubscription.subscription_id,
          },
          data: {
            remaining_free_booking_time: {
              decrement: moment
                .duration(totalDeductedBookingTime, "milliseconds")
                .asMinutes(),
            },
          },
        });
        processedBookingReceipt.initialCalculationAfterSubscriptionApplied();

        processedBookingReceipt.unlock_charge =
          appliedSubscription.tbl_subscription_types.unlock_charge == null ||
          appliedSubscription.tbl_subscription_types.unlock_charge == undefined
            ? processedBookingReceipt.unlock_charge
            : appliedSubscription.tbl_subscription_types.unlock_charge;
        processedBookingReceipt.ride_time_charge =
          appliedSubscription.tbl_subscription_types.ride_time_charge == null ||
          appliedSubscription.tbl_subscription_types.ride_time_charge ==
            undefined
            ? processedBookingReceipt.ride_time_charge
            : appliedSubscription.tbl_subscription_types.ride_time_charge;
        processedBookingReceipt.pause_time_charge =
          appliedSubscription.tbl_subscription_types.pause_time_charge ==
            null ||
          appliedSubscription.tbl_subscription_types.pause_time_charge ==
            undefined
            ? processedBookingReceipt.pause_time_charge
            : appliedSubscription.tbl_subscription_types.pause_time_charge;

        Logger.log("info", {
          message:
            "BookingReceiptService:processAppliedSubscriptionForBookingReceipt:updatedPlan",
          params: {
            bookingID: booking.booking_id,
            processedBookingReceipt,
            updatedSubscription,
          },
        });
      } else {
        const { bookingPauseTime, bookingRideTime } =
          await BookingLogService.calculateBookingRideAndPauseTimeFromStartAndEndTime(
            {
              bookingLogs,
              consideredStartTime: bookingReceipt.plan
                ? bookingReceipt.plan.validity
                : booking.booking_start_at,
              consideredEndTime: booking.booking_end_at,
            }
          );
        const totalBookingTime = bookingPauseTime + bookingRideTime;

        Logger.log("info", {
          message:
            "BookingReceiptService:processAppliedSubscriptionForBookingReceipt:totalBookingTime",
          params: {
            bookingID: booking.booking_id,
            bookingRideTime,
            bookingPauseTime,
            totalBookingTime,
            processedBookingReceipt,
          },
        });

        processedBookingReceipt.total_pause_time_after_subscription =
          bookingPauseTime;
        processedBookingReceipt.total_ride_time_after_subscription =
          bookingRideTime;
        processedBookingReceipt.total_booking_time_after_subscription =
          totalBookingTime;

        processedBookingReceipt.initialCalculationAfterSubscriptionApplied();

        processedBookingReceipt.unlock_charge =
          appliedSubscription.tbl_subscription_types.unlock_charge == null ||
          appliedSubscription.tbl_subscription_types.unlock_charge == undefined
            ? processedBookingReceipt.unlock_charge
            : appliedSubscription.tbl_subscription_types.unlock_charge;
        processedBookingReceipt.ride_time_charge =
          appliedSubscription.tbl_subscription_types.ride_time_charge == null ||
          appliedSubscription.tbl_subscription_types.ride_time_charge ==
            undefined
            ? processedBookingReceipt.ride_time_charge
            : appliedSubscription.tbl_subscription_types.ride_time_charge;
        processedBookingReceipt.pause_time_charge =
          appliedSubscription.tbl_subscription_types.pause_time_charge ==
            null ||
          appliedSubscription.tbl_subscription_types.pause_time_charge ==
            undefined
            ? processedBookingReceipt.pause_time_charge
            : appliedSubscription.tbl_subscription_types.pause_time_charge;

        Logger.log("info", {
          message:
            "BookingReceiptService:processAppliedSubscriptionForBookingReceipt:updatedPlan",
          params: {
            bookingID: booking.booking_id,
            processedBookingReceipt,
          },
        });
      }

      return processedBookingReceipt;
    } catch (error) {
      Logger.log("error", {
        message:
          "BookingReceiptService:processAppliedSubscriptionForBookingReceipt:catch-1",
        params: {
          error,
        },
      });
      return processedBookingReceipt;
    }
  };
}

module.exports = { BookingReceiptService };
