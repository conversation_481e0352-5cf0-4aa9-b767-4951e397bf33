const { prisma } = require("../../config/prisma");
const Logger = require("../../utils/logger");

const constants = require("../../constants");
const { slackWebClient } = require("../../config/slack");
class TicketingService {
  constructor() {}
  /**
   *
   * @param {object} param0
   * @param {import("@prisma/client").tbl_users} param0.user
   * @param {string} param0.message
   */
  static createSupportTicket = async ({ user, message }) => {
    try {
      //   let channelID = "C05SCD7H34Y";

      Logger.log("info", {
        message: "ReportService:createSupportTicket:params",
        params: { userID: user.user_id, message },
      });
      //   if (user?.tbl_user_station_map?.[0]?.tbl_stations) {
      //     Logger.log("info", {
      //       message: "ReportService:createSupportTicket:channelID",
      //       params: { userID: user.user_id, message, channelID },
      //     });
      //     channelID = user.tbl_user_station_map[0].tbl_stations.channel_id;
      //   }
      //   const result = await slackWebClient.chat.postMessage({
      //     text: message,
      //     channel: channelID,
      //   });
      //   Logger.log("success", {
      //     message: "ReportService:createSupportTicket:added to slack",
      //     params: { userID: user.user_id, threadID: result.ts },
      //   });
      const supportTicketRecord = await prisma.tbl_support_tickets.create({
        data: {
          text: message,
          //   meta_key: `${channelID}-${result.ts}`,
          user_id: parseInt(user.user_id),
        },
      });
      Logger.log("success", {
        message: "ReportService:createSupportTicket:added to local db",
        params: {
          userID: user.user_id,
          //   threadID: result.ts,
          supportTicketID: supportTicketRecord.support_ticket_id,
        },
      });
      return supportTicketRecord;
    } catch (error) {
      Logger.log("error", {
        message: "ReportService:createSupportTicket:catch-1",
        params: { error },
      });
      throw error;
    }
  };

  /**
   *
   * @param {object} param0
   * @param {import("@prisma/client").tbl_users} param0.user
   */
  static retrieveAllSupportTickets = async ({ user }) => {
    try {
      Logger.log("info", {
        message: "ReportService:retrieveAllSupportTickets:params",
        params: { userID: user.user_id },
      });
      const tickets = await prisma.tbl_support_tickets.findMany({
        where: { user_id: user.user_id },
      });

      Logger.log("success", {
        message: "ReportService:retrieveAllSupportTickets:messages retrieved",
        params: {
          tickets: tickets.length,
        },
      });
      return tickets;
    } catch (error) {
      Logger.log("error", {
        message: "ReportService:retrieveAllSupportTickets:catch-1",
        params: { error },
      });
      throw error;
    }
  };

  /**
   *
   * @param {object} param0
   * @param {Number} param0.userID
   * @param {Number} param0.ticketID
   */
  static retrieveSupportTicketMessage = async ({ userID, ticketID }) => {
    try {
      Logger.log("info", {
        message: "ReportService:retrieveSupportTicketMessage:params",
        params: { ticketID, userID },
      });
      const ticket = await prisma.tbl_support_tickets.findFirst({
        where: { support_ticket_id: ticketID, user_id: userID },
      });
      if (!ticket) {
        Logger.log("error", {
          message: "ReportService:retrieveSupportTicketMessage:catch-1",
          params: { error: constants.ERROR_CODES.INVALID_REQUEST },
        });
        throw constants.ERROR_CODES.INVALID_REQUEST;
      }
      const metaKeyArray = String(ticket.meta_key).split("-");
      const ticketMessage = await slackWebClient.conversations.replies({
        channel: metaKeyArray[0],
        ts: metaKeyArray[1],
      });

      Logger.log("success", {
        message:
          "ReportService:retrieveSupportTicketMessage:messages retrieved",
        params: {
          ticketMessage: ticketMessage.messages.length,
        },
      });
      return ticketMessage;
    } catch (error) {
      Logger.log("error", {
        message: "ReportService:retrieveSupportTicketMessage:catch-1",
        params: { error },
      });
      throw error;
    }
  };

  /**
   *
   * @param {object} param0
   * @param {Number} param0.ticketID
   * @param {string} param0.ts
   * @param {Number} param0.userID
   * @param {string} param0.message
   */
  static postMessageToSupportTicket = async ({
    userID,
    ticketID,
    ts,
    message,
  }) => {
    try {
      Logger.log("info", {
        message: "ReportService:postMessageToSupportTicket:params",
        params: { ticketID, message, ts, userID },
      });
      const ticket = await prisma.tbl_support_tickets.findFirst({
        where: { support_ticket_id: ticketID, user_id: userID },
      });
      if (!ticket) {
        Logger.log("error", {
          message: "ReportService:retrieveSupportTicketMessage:catch-1",
          params: { error: constants.ERROR_CODES.INVALID_REQUEST },
        });
        throw constants.ERROR_CODES.INVALID_REQUEST;
      }
      const metaKeyArray = String(ticket.meta_key).split("-");
      const ticketMessage = await slackWebClient.chat.postMessage({
        channel: metaKeyArray[0],
        thread_ts: ts ? ts : metaKeyArray[1],
        text: message,
      });

      Logger.log("success", {
        message: "ReportService:postMessageToSupportTicket:messages posted",
        params: {
          ticketMessage: ticketMessage,
        },
      });
      return ticketMessage;
    } catch (error) {
      Logger.log("error", {
        message: "ReportService:postMessageToSupportTicket:catch-1",
        params: { error },
      });
      throw error;
    }
  };

  /**
   *
   * @param {object} param0
   * @param {Number} param0.ticketID
   * @param {Number} param0.userID
   * @param {string} param0.message
   */
  static closeSupportTicket = async ({ userID, ticketID, message }) => {
    try {
      Logger.log("info", {
        message: "ReportService:closeSupportTicket:params",
        params: { ticketID, message, userID },
      });
      const ticket = await prisma.tbl_support_tickets.update({
        where: { support_ticket_id: ticketID },
        data: { closed_at: new Date() },
      });

      Logger.log("success", {
        message: "ReportService:closeSupportTicket:ticket closed",
        params: {
          ticket,
        },
      });
      return ticket;
    } catch (error) {
      Logger.log("error", {
        message: "ReportService:closeSupportTicket:catch-1",
        params: { error },
      });
      throw error;
    }
  };
}

module.exports = { TicketingService };
