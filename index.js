const environment = require("./environment");

require("./config/firebase");

const constants = require("./constants");
const { expressApp } = require("./config/express.app");
const { httpServer } = require("./config/http.server");
const { socketIO } = require("./config/socket.io");
const userSocketController = require("./modules/socket/controllers/user.socket.controller");
const stationSocketController = require("./modules/socket/controllers/station.socket.controller");
const Logger = require("./utils/logger");
const vehicleSocketController = require("./modules/socket/controllers/vehicle.socket.controller");
const { CronJobScheduler } = require("./jobs/cron.jobs");
const { broker } = require("./config/broker");
const { connectMongoDB } = require("./config/mongoose");
connectMongoDB();
expressApp.use("/api/users", require("./modules/customer/routes/user.route"));
expressApp.use(
  "/api/wallets",
  require("./modules/customer/routes/wallet.route")
);
expressApp.use(
  "/api/stations",
  require("./modules/customer/routes/station.route")
);

expressApp.use(
  "/api/vehicles",
  require("./modules/customer/routes/vehicle.route")
);

expressApp.use("/api/offers", require("./modules/customer/routes/offer.route"));
expressApp.use(
  "/api/payment_orders",
  require("./modules/customer/routes/paymentOrder.route")
);

expressApp.use(
  "/api/bookings",
  require("./modules/customer/routes/booking.route")
);
expressApp.use(
  "/api/reservations",
  require("./modules/customer/routes/reservation.route")
);
expressApp.use(
  "/api/receipts",
  require("./modules/customer/routes/receipt.route")
);
expressApp.use(
  "/api/delivery_bookings",
  require("./modules/customer/routes/delivery.route")
);
expressApp.use(
  "/api/feedbacks",
  require("./modules/customer/routes/feedback.route")
);

expressApp.use(
  "/api/advertises",
  require("./modules/customer/routes/advertise.route")
);

expressApp.use("/api/test", require("./modules/customer/routes/test.route"));
expressApp.use(
  "/api/admin/vehicles",
  require("./modules/admin/routes/vehicle.route")
);

expressApp.use("/webhooks", require("./modules/webhook/routes/webhook.route"));

// error handling here
expressApp.get("/", async (req, res) => {
  Logger.log("warning", {
    message: "NO_DATA_FOUND",
    params: { ips: req.ips, headers: req.headers, url: req.url },
  });
  res.status(404).json({
    error: constants.ERROR_CODES.INVALID_REQUEST,
  });
});

socketIO.on("connection", async (socket) => {
  const { firebase_id, token } = socket.handshake.auth;
  socket.on(constants.SOCKET_EMIT_EVENTS.USER_LOCATION_DATA, (location) => {
    location &&
      userSocketController.updateUserGeolocation({
        firebaseID: firebase_id,
        latitude: location.latitude,
        longitude: location.longitude,
      });
  });
  await userSocketController.subscribeUserToRooms({
    socket: socket,
    firebaseID: firebase_id,
  });

  Logger.log("success", {
    message: "user connected to socket",
    params: { firebase_id },
  });

  stationSocketController.emitStationsOnFirstConnect({
    firebaseID: firebase_id,
  });
  vehicleSocketController.emitVehiclesOnFirstConnect({
    firebaseID: firebase_id,
  });

  socket.on("disconnect", () => {
    Logger.log("info", {
      message: "socket connection disconnected",
      params: { firebase_id },
    });
  });
});
//-----------------------------------------------------------------------------------
const HTTP_SERVER_PORT = environment.PORT;
const BROKER_PORT = environment.BROKER_PORT;
// globalVariableController.setGlobalMachineIDToMachineTypeMap();

httpServer.listen(HTTP_SERVER_PORT, () => {
  Logger.log("success", {
    message: "server started listening",
    params: { HTTP_SERVER_PORT },
  });
  // CronJobScheduler.failPendingPaymentOrdersCronjob();
  // CronJobScheduler.endOverdueReservationsCronjob();
  // CronJobScheduler.bulkUpdateOldIOTVehiclesIOTInfoCronjob();
  CronJobScheduler.revertTimedOutIOTCommandsCronjob();
});

broker.listen(BROKER_PORT, () => {
  Logger.log("success", {
    message: "broker started listening",
    params: { BROKER_PORT },
  });
});
