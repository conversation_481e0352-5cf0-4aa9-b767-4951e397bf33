const { prisma } = require("../../../config/prisma");
const { socketIO } = require("../../../config/socket.io");
const constants = require("../../../constants");
const Logger = require("../../../utils/logger");

const stationSocketController = {};

stationSocketController.emitStationsOnFirstConnect = async ({ firebaseID }) => {
  try {
    Logger.log("info", {
      message: "stationSocketController:emitStationsOnFirstConnect:params",
      params: { firebaseID },
    });
    const user = await prisma.tbl_users.findFirst({
      where: {
        firebase_id: firebaseID,
      },
    });
    Logger.log("info", {
      message: "stationSocketController:emitStationsOnFirstConnect:user",
      params: { userID: user.user_id, firebaseID },
    });

    if (user && user.last_lat && user.last_lng) {
      const stations = await prisma.tbl_stations.findMany({
        where: {
          AND: [
            {
              lat: {
                gte:
                  parseFloat(user.last_lat) - constants.STATION_RENDER_RADIUS,
              },
            },
            {
              lat: {
                lte:
                  parseFloat(user.last_lat) + constants.STATION_RENDER_RADIUS,
              },
            },
            {
              lng: {
                gte:
                  parseFloat(user.last_lng) - constants.STATION_RENDER_RADIUS,
              },
            },
            {
              lng: {
                lte:
                  parseFloat(user.last_lng) + constants.STATION_RENDER_RADIUS,
              },
            },
          ],
        },
      });
      Logger.log("info", {
        message: "stationSocketController:emitStationsOnFirstConnect:stations",
        params: { userID: user.user_id, firebaseID, stations: stations.length },
      });
      socketIO.emit(constants.SOCKET_EVENTS.ON_STATION_UPDATE, stations);
      Logger.log("success", {
        message: "stationSocketController:emitStations:success",
        params: { userID: user.user_id, firebaseID, stations: stations.length },
      });
    } else {
      Logger.log("error", {
        message: "stationSocketController:emitStations:catch-2",
        params: { firebaseID, error: constants.ERROR_CODES.INVALID_USER },
      });
    }
  } catch (error) {
    Logger.log("error", {
      message: "stationSocketController:emitStations:catch-1",
      params: { error },
    });
  }
};

/**
 *
 * @param {object} param0
 * @param {import("@prisma/client").tbl_users} param0.user
 * @param {Number} param0.latitude
 * @param {Number} param0.longitude
 */
stationSocketController.emitStationsOnUserGeolocationUpdate = async ({
  user,
  latitude,
  longitude,
}) => {
  try {
    Logger.log("info", {
      message:
        "stationSocketController:emitStationsOnUserGeolocationUpdate:params",
      params: { userID: user.user_id, latitude, longitude },
    });
    const stations = await prisma.tbl_stations.findMany({
      where: {
        AND: [
          {
            lat: {
              gte: parseFloat(latitude) - constants.STATION_RENDER_RADIUS,
            },
          },
          {
            lat: {
              lte: parseFloat(latitude) + constants.STATION_RENDER_RADIUS,
            },
          },
          {
            lng: {
              gte: parseFloat(longitude) - constants.STATION_RENDER_RADIUS,
            },
          },
          {
            lng: {
              lte: parseFloat(longitude) + constants.STATION_RENDER_RADIUS,
            },
          },
        ],
      },
    });
    Logger.log("info", {
      message:
        "stationSocketController:emitStationsOnUserGeolocationUpdate:stations",
      params: { userID: user.user_id, stations: stations.length },
    });
    socketIO
      .to(user.firebase_id)
      .emit(constants.SOCKET_EVENTS.ON_STATION_UPDATE, stations);
    Logger.log("success", {
      message:
        "stationSocketController:emitStationsOnUserGeolocationUpdate:success",
      params: { userID: user.user_id, firebaseID: user.firebase_id },
    });
  } catch (error) {
    Logger.log("error", {
      message:
        "stationSocketController:emitStationsOnUserGeolocationUpdate:catch-1",
      params: { error },
    });
  }
};

module.exports = stationSocketController;
