const { prisma } = require("../../../config/prisma");
const { socketIO } = require("../../../config/socket.io");
const constants = require("../../../constants");
const Logger = require("../../../utils/logger");

const bookingSocketController = {};

/**
 *
 * @param {object} param0
 * @param {import("@prisma/client").tbl_bookings} param0.ongoingBooking
 * @param {String} param0.firebaseID
 */
bookingSocketController.emitOngoingBookingOnUpdate = async ({
  ongoingBooking,
  firebaseID,
}) => {
  Logger.log("info", {
    message: "bookingSocketController:emitOngoingBookingOnUpdate",
    params: { firebaseID },
  });

  try {
    socketIO
      .to(firebaseID)
      .emit(constants.SOCKET_EVENTS.ON_ONGOING_BOOKING_UPDATE, ongoingBooking);
  } catch (error) {
    Logger.log("error", {
      message: "bookingSocketController:emitOngoingBookingOnUpdate:catch-1",
      params: { error },
    });
  }
};

module.exports = bookingSocketController;
