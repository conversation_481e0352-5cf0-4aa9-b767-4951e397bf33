const { prisma } = require("../../../config/prisma");
const { socketIO } = require("../../../config/socket.io");
const constants = require("../../../constants");
const Logger = require("../../../utils/logger");

const offerSocketController = {};

/**
 *
 * @param {object} param0
 * @param {import("@prisma/client").tbl_users & {tbl_location_offers_map:import("@prisma/client").tbl_location_offers_map}} param0.user
 */
offerSocketController.emitEventOnUserLocationOffersMappingUpdate = async ({
  user,
}) => {
  try {
    socketIO
      .to(user.firebase_id)
      .emit(constants.SOCKET_EVENTS.ON_OFFERS_UPDATE);
    Logger.log("success", {
      message:
        "offerSocketController:emitEventOnUserLocationOffersMappingUpdate:success",
      params: { userID: user.user_id },
    });
  } catch (error) {
    Logger.log("error", {
      message:
        "offerSocketController:emitEventOnUserLocationOffersMappingUpdate:catch-1",
      params: { error },
    });
  }
};

module.exports = offerSocketController;
