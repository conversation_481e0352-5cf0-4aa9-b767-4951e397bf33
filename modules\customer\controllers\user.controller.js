const { prisma } = require("../../../config/prisma");
const constants = require("../../../constants");
const { extractError } = require("../../../utils/error.utils");
const Logger = require("../../../utils/logger");
// const { OfferService } = require("../../services/offer.service");
const { WalletService } = require("../../services/wallet.service");
// const machineSocketController = require("../../socket/controllers/machine.socket.controller");
// const stationSocketController = require("../../socket/controllers/station.socket.controller");
const userSocketController = require("../../socket/controllers/user.socket.controller");
const { OfferService } = require("../../services/offer.service");
const {
  FirebasePushNotificationService,
} = require("../../services/firebasePushNotification.service");
const { UserService } = require("../../services/user.service");
const userController = {};

/**
 *
 * @param {import("express").Request} req
 * @param {import("express").Response} res
 * @returns
 */
userController.getUserInfo = async (req, res) => {
  try {
    const { user, firebaseUser } = req;

    if (user) {
      Logger.log("info", {
        message: "userController:getUserInfo:already_exists",
        params: { user },
      });
      FirebasePushNotificationService.sendMultiCastMessageToUserID({
        title: "User logged in!",
        description: "Thank you for logging in!",
        userID: user.user_id,
      });
      return res.json({
        success: true,
        user,
      });
    } else {
      Logger.log("info", {
        message: "userController:getUserInfo:create",
        params: { user: firebaseUser },
      });

      const newUser = await prisma.tbl_users.create({
        data: {
          phone_number: firebaseUser.phone_number,
          firebase_id: firebaseUser.uid,
          tbl_user_types: {
            connect: { user_type_id: constants.DEFAULT_USER_TYPE_ID },
          },
          tbl_location_offers_map: {
            connect: {
              location_offers_map_id: constants.DEFAULT_LOCATION_OFFER_MAP_ID,
            },
          },
        },
        include: { tbl_wallets: true, tbl_user_types: true },
      });
      // OfferService.assignDefaultOffersOnSignUp({
      //   userID: newUser.user_id,
      // });

      const walletActivatedUser = await WalletService.activateWallet(newUser);

      Logger.log("success", {
        message: "userController:getUserInfo:created",
        params: { user: walletActivatedUser },
      });

      const { couponPurchaseReceiptID } =
        await OfferService.createCouponPurchaseReceipt({
          user: newUser,
          paymentSource: constants.PAYMENT_SOURCE.SIGNUP_BONUS,
          couponTypeID: parseInt(newUser.tbl_user_types.initial_coupon_type_id),
        });
      const newCoupon = await OfferService.assignCouponTypeToUser({
        user: newUser,
        couponPurchaseReceiptID: couponPurchaseReceiptID,
      });
      Logger.log("success", {
        message:
          "userController:getUserInfo:security deposit bonus plan assigned",
        params: {
          newCoupon,
        },
      });
      // userSocketController.emitUserOnUpdate({
      //   userID: walletActivatedUser.user_id,
      // });

      return res.json({ success: true, user: newUser });
    }
  } catch (error) {
    Logger.log("error", {
      message: "userController:getUserInfo:catch-1",
      params: { error },
    });
    return res.json({
      success: false,
      error: constants.ERROR_CODES.SERVER_ERROR,
    });
  }
};

/**
 *
 * @param {import("express").Request} req
 * @param {import("express").Response} res
 * @returns
 */
userController.updateUserInfo = async (req, res) => {
  try {
    const { user } = req;
    const { email, first_name, last_name, address1 } = req.body;
    Logger.log("info", {
      message: "userController:updateUserInfo:params",
      params: {
        userID: user.user_id,
        email,
        first_name,
        last_name,
        address1,
      },
    });

    // check if razorpay customer

    const updatedUser = await prisma.tbl_users.update({
      where: { user_id: user.user_id },
      data: {
        // adding the field only if value is present
        email: email ? String(email) : null,
        first_name: first_name ? String(first_name) : null,
        last_name: last_name ? String(last_name) : null,
        address1: address1 ? String(address1) : null,
      },
      include: {
        tbl_wallets: true,
        tbl_user_types: true,
      },
    });
    Logger.log("success", {
      message: "userController:updateUserInfo:updated",
      params: { updatedUser },
    });
    // send socket data
    userSocketController.emitUserOnUpdate({
      userID: updatedUser.user_id,
    });
    return res.json({ success: true, user: updatedUser });
  } catch (error) {
    Logger.log("error", {
      message: "userController:updateUserInfo:catch-1",
      params: { error },
    });
    return res.json({
      success: false,
      error: constants.ERROR_CODES.SERVER_ERROR,
    });
  }
};

/**
 *
 * @param {import("express").Request} req
 * @param {import("express").Response} res
 * @returns
 */
userController.addPushNotificationToken = async (req, res) => {
  try {
    const { user } = req;
    const { push_notification_token } = req.body;
    Logger.log("info", {
      message: "userController:addPushNotificationToken:params",
      params: { userID: user.user_id, push_notification_token },
    });
    const exisintgTokenRecord =
      await prisma.tbl_push_notification_tokens.findFirst({
        where: {
          user_id: parseInt(user.user_id),
        },
      });
    let tokenRecord;
    if (exisintgTokenRecord) {
      tokenRecord = await prisma.tbl_push_notification_tokens.update({
        where: {
          push_notification_token: exisintgTokenRecord.push_notification_token,
        },
        data: {
          push_notification_token: String(push_notification_token),
        },
      });
    } else {
      tokenRecord = await prisma.tbl_push_notification_tokens.create({
        data: {
          push_notification_token: String(push_notification_token),
          user_id: parseInt(user.user_id),
        },
      });
    }

    Logger.log("success", {
      message: "userController:addPushNotificationToken:tokenRecord",
      params: { tokenRecord },
    });

    return res.json({ success: true, tokenRecord: tokenRecord });
  } catch (error) {
    Logger.log("error", {
      message: "userController:addPushNotificationToken:catch-1",
      params: { error },
    });
    return res.json({
      success: false,
      error: constants.ERROR_CODES.SERVER_ERROR,
    });
  }
};

/**
 *
 * @param {import("express").Request} req
 * @param {import("express").Response} res
 * @returns
 */
userController.deletePushNotificationToken = async (req, res) => {
  try {
    const { user } = req;
    const { push_notification_token } = req.params;
    Logger.log("info", {
      message: "userController:deletePushNotificationToken:params",
      params: { userID: user.user_id, push_notification_token },
    });
    await prisma.tbl_web_push_notifications_token.deleteMany({
      where: {
        push_notification_token: String(push_notification_token),
      },
    });
    Logger.log("info", {
      message: "userController:deletePushNotificationToken:tokenRecord deleted",
      params: { pushNotificationToken: push_notification_token },
    });
    return res.json({ success: true });
  } catch (error) {
    Logger.log("info", {
      message: "userController:deletePushNotificationToken:catch-1",
      params: { error },
    });
    return res.json({
      success: false,
      error: constants.ERROR_CODES.SERVER_ERROR,
    });
  }
};

/**
 *
 * @param {import("express").Request} req
 * @param {import("express").Response} res
 * @returns
 */
userController.getAllPushNotificationToken = async (req, res) => {
  try {
    const { user } = req;
    const tokenRecords = await prisma.tbl_web_push_notifications_token.findMany(
      {
        where: {
          user_id: parseInt(user.user_id),
        },
      }
    );
    return res.json({ success: true, tokenRecords: tokenRecords });
  } catch (error) {
    Logger.log("error", {
      message: "userController:getAllPushNotificationToken:catch-1",
      params: { error },
    });
    return res.json({
      success: false,
      error: constants.ERROR_CODES.SERVER_ERROR,
    });
  }
};

/**
 *
 * @param {import("express").Request} req
 * @param {import("express").Response} res
 * @returns
 */
userController.getAadhaarVerificationOTP = async (req, res) => {
  try {
    const { user, body } = req;
    Logger.log("info", {
      message: "userController:getAadhaarVerificationOTP:init",
      params: { userID: user.user_id },
    });
    const { aadhaarNumber } = body;
    const requestID = await UserService.getAadhaarVerificationOTP({
      aadhaarNumber,
    });
    Logger.log("success", {
      message: "userController:getAadhaarVerificationOTP:success",
      params: { userID: user.user_id },
    });
    return res.json({ success: true, requestID });
  } catch (error) {
    Logger.log("error", {
      message: "userController:getAadhaarVerificationOTP:catch-1",
      params: { error },
    });
    return res.json({
      success: false,
      error: constants.ERROR_CODES.KYC_FAILED,
    });
  }
};

/**
 *
 * @param {import("express").Request} req
 * @param {import("express").Response} res
 * @returns
 */
userController.verifyAadhaarOTP = async (req, res) => {
  try {
    const { user, body } = req;
    Logger.log("info", {
      message: "userController:verifyAadhaarOTP:init",
      params: { userID: user.user_id },
    });
    const { requestID, OTP } = body;
    const result = await UserService.verifyAadhaarOTP({
      user,
      requestID,
      OTP,
    });
    Logger.log("success", {
      message: "userController:verifyAadhaarOTP:success",
      params: { userID: user.user_id, result },
    });
    return res.json({ success: true, result });
  } catch (error) {
    Logger.log("error", {
      message: "userController:verifyAadhaarOTP:catch-1",
      params: { error },
    });
    return res.json({
      success: false,
      error: constants.ERROR_CODES.KYC_FAILED,
    });
  }
};

module.exports = userController;
