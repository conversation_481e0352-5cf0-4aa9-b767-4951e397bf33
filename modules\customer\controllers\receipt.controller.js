const Logger = require("../../../utils/logger");
const { extractError } = require("../../../utils/error.utils");
const { prisma } = require("../../../config/prisma");
const constants = require("../../../constants");
const {
  ReservationReceiptService,
} = require("../../services/reservationReceipt.service");
const {
  BookingReceiptService,
} = require("../../services/bookingReceipt.service");

const receiptController = {};

/**
 *
 * @param {import("express").Request} req
 * @param {import("express").Response} res
 * @returns
 */
receiptController.generateBookingReceipt = async (req, res) => {
  try {
    const { user } = req;
    const { booking_id, regenerate } = req.body;
    Logger.log("info", {
      message: "receiptController:generateBookingReceipt:params",
      params: { userID: user.user_id, booking_id, regenerate },
    });
    const booking = await prisma.tbl_bookings.findFirst({
      where: {
        booking_id: parseInt(booking_id),
        user_id: parseInt(user.user_id),
      },
      include: {
        tbl_users: {
          include: {
            tbl_location_offers_map: true,
          },
        },
        tbl_booking_logs: {
          orderBy: { created_at: "asc" },
        },
        tbl_vehicles: {
          include: {
            tbl_vehicle_types: true,
          },
        },
      },
    });
    if (booking.booking_receipt_id && !regenerate) {
      Logger.log("error", {
        message: "receiptController:generateBookingReceipt:catch-2",
        params: { error: constants.ERROR_CODES.RECEIPT_ALREADY_GENERATED },
      });
      res.json({
        success: false,
        error: constants.ERROR_CODES.RECEIPT_ALREADY_GENERATED,
      });
    } else {
      await BookingReceiptService.generateBookingReceipt({ booking });
      Logger.log("success", {
        message: "receiptController:generateBookingReceipt:success",
        params: { userID: user.user_id, booking_id },
      });
      return res.json({ success: true });
    }
  } catch (error) {
    Logger.log("error", {
      message: "receiptController:generateBookingReceipt:catch-1",
      params: { error },
    });
    res.json({ success: false, error: extractError(error) });
  }
};

/**
 *
 * @param {import("express").Request} req
 * @param {import("express").Response} res
 * @returns
 */
receiptController.generateReservationReceipt = async (req, res) => {
  try {
    const { user } = req;
    const { reservation_id, regenerate } = req.body;
    Logger.log("info", {
      message: "receiptController:generateReservationReceipt:params",
      params: { userID: user.user_id, reservation_id, regenerate },
    });
    const reservation = await prisma.tbl_reservations.findFirst({
      where: {
        reservation_id: parseInt(reservation_id),
        user_id: parseInt(user.user_id),
      },
      include: {
        tbl_users: {
          include: {
            tbl_location_offers_map: true,
          },
        },
        tbl_reservation_types: true,
        tbl_vehicles: {
          include: {
            tbl_vehicle_types: true,
          },
        },
      },
    });
    if (reservation.reservation_receipt_id && !regenerate) {
      Logger.log("error", {
        message: "receiptController:generateReservationReceipt:catch-2",
        params: { error: constants.ERROR_CODES.RECEIPT_ALREADY_GENERATED },
      });
      res.json({
        success: false,
        error: constants.ERROR_CODES.RECEIPT_ALREADY_GENERATED,
      });
    } else {
      await ReservationReceiptService.generateReservationReceipt({
        reservation,
      });
      Logger.log("success", {
        message: "receiptController:generateReservationReceipt:success",
        params: { userID: user.user_id, reservation_id },
      });
      return res.json({ success: true });
    }
  } catch (error) {
    Logger.log("error", {
      message: "receiptController:generateReservationReceipt:catch-1",
      params: { error },
    });
    res.json({ success: false, error: extractError(error) });
  }
};




module.exports = { receiptController };
