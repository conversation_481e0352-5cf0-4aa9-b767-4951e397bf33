const constants = require("../../constants");
const Logger = require("../../utils/logger");
const { prisma } = require("../../config/prisma");
const moment = require("moment");
const {
  generateCodec12,
  GPRS,
} = require("../teltonikaCodec/GPRS Parser/GPRSparser");
const { default: axios } = require("axios");

class IOTService {
  constructor() {}

  /**
   *
   * @param {object} param0
   * @param {String} param0.imei
   * @param {String} param0.command
   */
  static sendCommand = async ({ imei, command }) => {
    try {
      Logger.log("info", {
        message: "IOTService:sendCommand:params",
        params: {
          imei,
          command,
          imei_to_socket: Object.keys(global.imei_to_socket),
        },
      });
      try {
        await prisma.tbl_iot_command_logs.delete({
          where: {
            imei: imei,
          },
        });
      } catch (error) {}

      await prisma.tbl_iot_command_logs.create({
        data: {
          imei,
          command,
          estimated_timeout_at: moment()
            .add(constants.IOT_COMMAND_TIMEOUT, "seconds")
            .toDate(),
          booking_log_id: null,
        },
      });
      const socket = global.imei_to_socket?.[imei];
      if (socket) {
        socket?.write(Buffer.from(generateCodec12(command), "hex"));

        Logger.log("success", {
          message: "IOTService:sendCommand:command sent",
          params: {
            imei,
            command,
            imei_to_socket: Object.keys(global.imei_to_socket),
          },
        });
        return true;
      } else {
        Logger.log("error", {
          message: "IOTService:sendCommand:catch-2",
          params: { error: constants.ERROR_CODES.SERVER_ERROR },
        });

        throw constants.ERROR_CODES.SERVER_ERROR;
        //TODO: revert operation here
      }
    } catch (error) {
      Logger.log("error", {
        message: "IOTService:sendCommand:catch-1",
        params: { error },
      });
      return false;
    }
  };

  /**
   *
   * @param {object} param0
   * @param {String} param0.imei
   * @param {String} param0.command
   */
  static resendCommand = async ({ imei, command }) => {
    try {
      Logger.log("info", {
        message: "IOTService:resendCommand:params",
        params: {
          imei,
          command,
          imei_to_socket: Object.keys(global.imei_to_socket),
        },
      });

      await prisma.tbl_iot_command_logs.update({
        where: {
          imei: imei,
        },
        data: {
          estimated_timeout_at: moment()
            .add(constants.IOT_COMMAND_TIMEOUT, "seconds")
            .toDate(),
          retry: {
            increment: 1,
          },
        },
      });

      const socket = global.imei_to_socket?.[imei];
      if (socket) {
        socket?.write(Buffer.from(generateCodec12(command), "hex"));

        Logger.log("success", {
          message: "IOTService:resendCommand:command sent",
          params: {
            imei,
            command,
            imei_to_socket: Object.keys(global.imei_to_socket),
          },
        });
        return true;
      } else {
        Logger.log("error", {
          message: "IOTService:resendCommand:catch-2",
          params: { error: constants.ERROR_CODES.SERVER_ERROR },
        });

        throw constants.ERROR_CODES.SERVER_ERROR;
        //TODO: revert operation here
      }
    } catch (error) {
      Logger.log("error", {
        message: "IOTService:resendCommand:catch-1",
        params: { error },
      });
      return false;
    }
  };

  /**
   *
   * @param {object} param0
   * @param {String} param0.imei
   * @param {GPRS} param0.content
   */
  static confirmCommandExecution = async ({ imei, content }) => {
    try {
      Logger.log("info", {
        message: "IOTService:confirmCommandExecution:params",
        params: { imei, content: content.responseStr },
      });
      const iotCommandLogEntry = await prisma.tbl_iot_command_logs.findUnique({
        where: {
          imei: imei,
        },
        include: {
          tbl_booking_logs: true,
        },
      });
      await prisma.tbl_iot_command_logs.delete({
        where: {
          imei: imei,
        },
      });
      Logger.log("info", {
        message: "IOTService:confirmCommandExecution:iotCommandLogEntry",
        params: {
          imei,
          responseStr: content.responseStr,
          iotCommandLogEntryTime: iotCommandLogEntry.created_at,
        },
      });
      if (
        (content.responseStr.includes(
          constants.IOT_COMMANDS.VEHICLE_STOP.response[0]
        ) ||
          content.responseStr.includes(
            constants.IOT_COMMANDS.VEHICLE_STOP.response[1]
          ) ||
          content.responseStr.includes(
            constants.IOT_COMMANDS.VEHICLE_START.response[0]
          ) ||
          content.responseStr.includes(
            constants.IOT_COMMANDS.VEHICLE_START.response[1]
          )) &&
        iotCommandLogEntry?.tbl_booking_logs
      ) {
        switch (iotCommandLogEntry.tbl_booking_logs.booking_action) {
          case constants.BOOKING_ACTIONS.INITIATE_START: {
            await IOTService.notifyConfirmStartCommand({
              imei: iotCommandLogEntry.imei,
              command: iotCommandLogEntry.command,
              booking_log_id: iotCommandLogEntry.booking_log_id,
              booking_id: iotCommandLogEntry.tbl_booking_logs.booking_id,
            });
            Logger.log("success", {
              message: "IOTService:confirmCommandExecution:INITIATE_START",
              params: { imei: iotCommandLogEntry.imei },
            });
            break;
          }
          case constants.BOOKING_ACTIONS.INITIATE_PAUSE: {
            await IOTService.notifyConfirmPauseCommand({
              imei: iotCommandLogEntry.imei,
              command: iotCommandLogEntry.command,
              booking_log_id: iotCommandLogEntry.booking_log_id,
              booking_id: iotCommandLogEntry.tbl_booking_logs.booking_id,
            });
            Logger.log("success", {
              message: "IOTService:confirmCommandExecution:INITIATE_PAUSE",
              params: { imei: iotCommandLogEntry.imei },
            });
            break;
          }
          case constants.BOOKING_ACTIONS.INITIATE_RESUME: {
            await IOTService.notifyConfirmResumeCommand({
              imei: iotCommandLogEntry.imei,
              command: iotCommandLogEntry.command,
              booking_log_id: iotCommandLogEntry.booking_log_id,
              booking_id: iotCommandLogEntry.tbl_booking_logs.booking_id,
            });
            Logger.log("success", {
              message: "IOTService:confirmCommandExecution:INITIATE_RESUME",
              params: { imei: iotCommandLogEntry.imei },
            });
            break;
          }
          case constants.BOOKING_ACTIONS.INITIATE_END: {
            await IOTService.notifyConfirmEndCommand({
              imei: iotCommandLogEntry.imei,
              command: iotCommandLogEntry.command,
              booking_log_id: iotCommandLogEntry.booking_log_id,
              booking_id: iotCommandLogEntry.tbl_booking_logs.booking_id,
            });
            Logger.log("success", {
              message: "IOTService:confirmCommandExecution:INITIATE_END",
              params: { imei: iotCommandLogEntry.imei },
            });
            break;
          }
          default: {
            Logger.log("warning", {
              message: "IOTService:confirmCommandExecution:wrong command",
              params: {
                imei: iotCommandLogEntry.imei,
                command: iotCommandLogEntry.tbl_booking_logs.booking_action,
              },
            });
            break;
          }
        }
      }
    } catch (error) {
      Logger.log("error", {
        message: "IOTService:confirmCommandExecution:catch-1",
        params: { error },
      });
    }
  };

  /**
   *
   * @param {object} param0
   * @param {import("@prisma/client").tbl_iot_command_logs & {tbl_booking_logs:import("@prisma/client").tbl_booking_logs}} param0.iotCommandLogEntry
   */
  static confirmCommandExecutionForUnlock = async ({ iotCommandLogEntry }) => {
    try {
      Logger.log("info", {
        message: "IOTService:confirmCommandExecutionForUnlock:params",
        params: {
          imei: iotCommandLogEntry.imei,
          command: iotCommandLogEntry.command,
          booking_log_id: iotCommandLogEntry.booking_log_id,
          booking_id: iotCommandLogEntry.tbl_booking_logs?.booking_id,
        },
      });
      await IOTService.notifyConfirmUnlockCommand({
        imei: iotCommandLogEntry.imei,
        command: iotCommandLogEntry.command,
        booking_log_id: iotCommandLogEntry.booking_log_id,
        booking_id: iotCommandLogEntry.tbl_booking_logs?.booking_id,
      });
      Logger.log("success", {
        message: "IOTService:confirmCommandExecutionForUnlock:success",
        params: {
          imei: iotCommandLogEntry.imei,
          command: iotCommandLogEntry.command,
          booking_log_id: iotCommandLogEntry.booking_log_id,
          booking_id: iotCommandLogEntry.tbl_booking_logs?.booking_id,
        },
      });
    } catch (error) {
      Logger.log("error", {
        message: "IOTService:confirmCommandExecutionForUnlock:catch-1",
        params: { error },
      });
    }
  };

  /**
   *
   * @param {object} param0
   * @param {import("@prisma/client").tbl_iot_command_logs & {tbl_booking_logs:import("@prisma/client").tbl_booking_logs}} param0.iotCommandLogEntry
   */
  static confirmCommandExecutionForLock = async ({ iotCommandLogEntry }) => {
    try {
      Logger.log("info", {
        message: "IOTService:confirmCommandExecutionForLock:params",
        params: {
          imei: iotCommandLogEntry.imei,
          command: iotCommandLogEntry.command,
          booking_log_id: iotCommandLogEntry.booking_log_id,
          booking_id: iotCommandLogEntry.tbl_booking_logs?.booking_id,
        },
      });
      await IOTService.notifyConfirmLockCommand({
        imei: iotCommandLogEntry.imei,
        command: iotCommandLogEntry.command,
        booking_log_id: iotCommandLogEntry.booking_log_id,
        booking_id: iotCommandLogEntry.tbl_booking_logs?.booking_id,
      });
      Logger.log("success", {
        message: "IOTService:confirmCommandExecutionForLock:success",
        params: {
          imei: iotCommandLogEntry.imei,
          command: iotCommandLogEntry.command,
          booking_log_id: iotCommandLogEntry.booking_log_id,
          booking_id: iotCommandLogEntry.tbl_booking_logs?.booking_id,
        },
      });
    } catch (error) {
      Logger.log("error", {
        message: "IOTService:confirmCommandExecutionForLock:catch-1",
        params: { error },
      });
    }
  };

  /**
   *
   * @param {object} param0
   * @param {import("@prisma/client").tbl_iot_command_logs & {tbl_booking_logs:import("@prisma/client").tbl_booking_logs & {tbl_bookings:import("@prisma/client").tbl_bookings}}} param0.iotCommandLogEntry
   */
  static handleTimeoutCommand = async ({ iotCommandLogEntry }) => {
    try {
      Logger.log("info", {
        message: "IOTService:handleTimedoutCommands:params",
        params: { imei: iotCommandLogEntry.imei },
      });
      await prisma.tbl_iot_command_logs.delete({
        where: {
          imei: iotCommandLogEntry.imei,
        },
      });
      Logger.log("info", {
        message: "IOTService:handleTimedoutCommands:timeout updated to null",
        params: { imei: iotCommandLogEntry.imei },
      });
      if (iotCommandLogEntry.tbl_booking_logs) {
        Logger.log("warning", {
          message: "IOTService:handleTimedoutCommands:booking command timeout",
          params: { imei: iotCommandLogEntry.imei },
        });
        switch (iotCommandLogEntry.tbl_booking_logs.booking_action) {
          case constants.BOOKING_ACTIONS.INITIATE_START: {
            await IOTService.notifyTimeoutStartCommand({
              imei: iotCommandLogEntry.imei,
              command: iotCommandLogEntry.command,
              booking_log_id: iotCommandLogEntry.booking_log_id,
              booking_id: iotCommandLogEntry.tbl_booking_logs.booking_id,
            });
            Logger.log("warning", {
              message:
                "IOTService:handleTimedoutCommands:booking command timeout:INITIATE_START",
              params: { imei: iotCommandLogEntry.imei },
            });
            break;
          }
          case constants.BOOKING_ACTIONS.INITIATE_PAUSE: {
            await IOTService.notifyTimeoutPauseCommand({
              imei: iotCommandLogEntry.imei,
              command: iotCommandLogEntry.command,
              booking_log_id: iotCommandLogEntry.booking_log_id,
              booking_id: iotCommandLogEntry.tbl_booking_logs.booking_id,
            });
            Logger.log("warning", {
              message:
                "IOTService:handleTimedoutCommands:booking command timeout:INITIATE_PAUSE",
              params: { imei: iotCommandLogEntry.imei },
            });
            break;
          }
          case constants.BOOKING_ACTIONS.INITIATE_RESUME: {
            await IOTService.notifyTimeoutResumeCommand({
              imei: iotCommandLogEntry.imei,
              command: iotCommandLogEntry.command,
              booking_log_id: iotCommandLogEntry.booking_log_id,
              booking_id: iotCommandLogEntry.tbl_booking_logs.booking_id,
            });
            Logger.log("warning", {
              message:
                "IOTService:handleTimedoutCommands:booking command timeout:INITIATE_RESUME",
              params: { imei: iotCommandLogEntry.imei },
            });
            break;
          }
          case constants.BOOKING_ACTIONS.INITIATE_END: {
            await IOTService.notifyTimeoutEndCommand({
              imei: iotCommandLogEntry.imei,
              command: iotCommandLogEntry.command,
              booking_log_id: iotCommandLogEntry.booking_log_id,
              booking_id: iotCommandLogEntry.tbl_booking_logs.booking_id,
            });
            Logger.log("warning", {
              message:
                "IOTService:handleTimedoutCommands:booking command timeout:INITIATE_END",
              params: { imei: iotCommandLogEntry.imei },
            });
            break;
          }
          default: {
            Logger.log("warning", {
              message:
                "IOTService:handleTimedoutCommands:booking command timeout:wrong command",
              params: {
                imei: iotCommandLogEntry.imei,
                command: iotCommandLogEntry.tbl_booking_logs.booking_action,
              },
            });
            break;
          }
        }
      } else {
        Logger.log("warning", {
          message:
            "IOTService:handleTimedoutCommands:non-booking command timeout",
          params: { imei: iotCommandLogEntry.imei },
        });
      }
    } catch (error) {
      Logger.log("error", {
        message: "IOTService:handleTimedoutCommands:catch-1",
        params: { error },
      });
    }
  };

  static revertTimeoutCommands = async () => {
    try {
      Logger.log("info", {
        message: "IOTService:revertTimeoutCommands:init",
      });
      const iotCommandLogEntries = await prisma.tbl_iot_command_logs.findMany({
        where: {
          estimated_timeout_at: {
            lt: new Date(),
          },
        },
        include: {
          tbl_booking_logs: {
            include: {
              tbl_bookings: true,
            },
          },
        },
      });
      const iotCommandLogEntriesWithRetriesOver = [];
      const iotCommandLogEntriesWithRetriesNotOver = [];
      iotCommandLogEntries.forEach((iotCommandLogEntry) => {
        if (
          iotCommandLogEntry.retry >= constants.DEFAULT_MAX_IOT_COMMAND_RETRY
        ) {
          iotCommandLogEntriesWithRetriesOver.push({ ...iotCommandLogEntry });
        } else {
          iotCommandLogEntriesWithRetriesNotOver.push({
            ...iotCommandLogEntry,
          });
        }
      });
      iotCommandLogEntriesWithRetriesNotOver.forEach((iotCommandLogEntry) => {
        IOTService.resendCommand({
          imei: iotCommandLogEntry.imei,
          command: iotCommandLogEntry.command,
        });
      });
      iotCommandLogEntriesWithRetriesOver.forEach((iotCommandLogEntry) => {
        this.handleTimeoutCommand({ iotCommandLogEntry });
      });
      Logger.log("success", {
        message: "IOTService:revertTimeoutCommands:success",
        params: {
          iotCommandLogEntriesWithRetriesOverLength:
            iotCommandLogEntriesWithRetriesOver.length,
          iotCommandLogEntriesWithRetriesNotOverLength:
            iotCommandLogEntriesWithRetriesNotOver.length,
        },
      });
    } catch (error) {
      Logger.log("error", {
        message: "IOTService:revertTimeoutCommands:catch-1",
        params: { error },
      });
    }
  };

  /**
   *
   * @param {object} param0
   * @param {String} param0.imei
   * @param {String} param0.command
   * @param {Number} param0.booking_log_id
   * @param {Number} param0.booking_id
   */
  static notifyConfirmLockCommand = async ({
    imei,
    command,
    booking_log_id,
    booking_id,
  }) => {
    try {
      Logger.log("info", {
        message: "IOTService:notifyConfirmLockCommand:params",
        params: {
          imei,
          command,
          booking_log_id,
          booking_id,
        },
      });
      await axios.post(
        `${constants.WEBHOOK_SERVER}/webhooks/iot/confirm_lock`,
        {
          imei,
          command,
          booking_log_id,
          booking_id,
        }
      );
      Logger.log("success", {
        message: "IOTService:notifyConfirmLockCommand:success",
        params: {
          imei,
          command,
          booking_log_id,
          booking_id,
        },
      });
      return true;
    } catch (error) {
      Logger.log("error", {
        message: "IOTService:notifyConfirmLockCommand:catch-1",
        params: { error },
      });
      return false;
    }
  };

  /**
   *
   * @param {object} param0
   * @param {String} param0.imei
   * @param {String} param0.command
   * @param {Number} param0.booking_log_id
   * @param {Number} param0.booking_id
   */
  static notifyConfirmUnlockCommand = async ({
    imei,
    command,
    booking_log_id,
    booking_id,
  }) => {
    try {
      Logger.log("info", {
        message: "IOTService:notifyConfirmUnlockCommand:params",
        params: {
          imei,
          command,
          booking_log_id,
          booking_id,
        },
      });
      await axios.post(
        `${constants.WEBHOOK_SERVER}/webhooks/iot/confirm_unlock`,
        {
          imei,
          command,
          booking_log_id,
          booking_id,
        }
      );
      Logger.log("success", {
        message: "IOTService:notifyConfirmUnlockCommand:success",
        params: {
          imei,
          command,
          booking_log_id,
          booking_id,
        },
      });
      return true;
    } catch (error) {
      Logger.log("error", {
        message: "IOTService:notifyConfirmUnlockCommand:catch-1",
        params: { error },
      });
      return false;
    }
  };

  /**
   *
   * @param {object} param0
   * @param {String} param0.imei
   * @param {String} param0.command
   * @param {Number} param0.booking_log_id
   * @param {Number} param0.booking_id
   */
  static notifyTimeoutLockCommand = async ({
    imei,
    command,
    booking_log_id,
    booking_id,
  }) => {
    try {
      Logger.log("info", {
        message: "IOTService:notifyTimeoutLockCommand:params",
        params: {
          imei,
          command,
          booking_log_id,
          booking_id,
        },
      });
      await axios.post(
        `${constants.WEBHOOK_SERVER}/webhooks/iot/timeout_lock`,
        {
          imei,
          command,
          booking_log_id,
          booking_id,
        }
      );
      Logger.log("success", {
        message: "IOTService:notifyTimeoutLockCommand:success",
        params: {
          imei,
          command,
          booking_log_id,
          booking_id,
        },
      });
      return true;
    } catch (error) {
      Logger.log("error", {
        message: "IOTService:notifyTimeoutLockCommand:catch-1",
        params: { error },
      });
      return false;
    }
  };

  /**
   *
   * @param {object} param0
   * @param {String} param0.imei
   * @param {String} param0.command
   * @param {Number} param0.booking_log_id
   * @param {Number} param0.booking_id
   */
  static notifyTimeoutUnlockCommand = async ({
    imei,
    command,
    booking_log_id,
    booking_id,
  }) => {
    try {
      Logger.log("info", {
        message: "IOTService:notifyTimeoutUnlockCommand:params",
        params: {
          imei,
          command,
          booking_log_id,
          booking_id,
        },
      });
      await axios.post(
        `${constants.WEBHOOK_SERVER}/webhooks/iot/timeout_unlock`,
        {
          imei,
          command,
          booking_log_id,
          booking_id,
        }
      );
      Logger.log("success", {
        message: "IOTService:notifyTimeoutUnlockCommand:success",
        params: {
          imei,
          command,
          booking_log_id,
          booking_id,
        },
      });
      return true;
    } catch (error) {
      Logger.log("error", {
        message: "IOTService:notifyTimeoutUnlockCommand:catch-1",
        params: { error },
      });
      return false;
    }
  };

  /**
   *
   * @param {object} param0
   * @param {String} param0.imei
   * @param {String} param0.command
   * @param {Number} param0.booking_log_id
   * @param {Number} param0.booking_id
   */
  static notifyTimeoutStartCommand = async ({
    imei,
    command,
    booking_log_id,
    booking_id,
  }) => {
    try {
      Logger.log("info", {
        message: "IOTService:notifyTimeoutStartCommand:params",
        params: {
          imei,
          command,
          booking_log_id,
          booking_id,
        },
      });
      await axios.post(
        `${constants.WEBHOOK_SERVER}/webhooks/iot/timeout_start`,
        {
          imei,
          command,
          booking_log_id,
          booking_id,
        }
      );
      Logger.log("success", {
        message: "IOTService:notifyTimeoutStartCommand:success",
        params: {
          imei,
          command,
          booking_log_id,
          booking_id,
        },
      });
      return true;
    } catch (error) {
      Logger.log("error", {
        message: "IOTService:notifyTimeoutStartCommand:catch-1",
        params: { error },
      });
      return false;
    }
  };

  /**
   *
   * @param {object} param0
   * @param {String} param0.imei
   * @param {String} param0.command
   * @param {Number} param0.booking_log_id
   * @param {Number} param0.booking_id
   */
  static notifyTimeoutPauseCommand = async ({
    imei,
    command,
    booking_log_id,
    booking_id,
  }) => {
    try {
      Logger.log("info", {
        message: "IOTService:notifyTimeoutPauseCommand:params",
        params: {
          imei,
          command,
          booking_log_id,
          booking_id,
        },
      });
      await axios.post(
        `${constants.WEBHOOK_SERVER}/webhooks/iot/timeout_pause`,
        {
          imei,
          command,
          booking_log_id,
          booking_id,
        }
      );
      Logger.log("success", {
        message: "IOTService:notifyTimeoutPauseCommand:success",
        params: {
          imei,
          command,
          booking_log_id,
          booking_id,
        },
      });
      return true;
    } catch (error) {
      Logger.log("error", {
        message: "IOTService:notifyTimeoutPauseCommand:catch-1",
        params: { error },
      });
      return false;
    }
  };

  /**
   *
   * @param {object} param0
   * @param {String} param0.imei
   * @param {String} param0.command
   * @param {Number} param0.booking_log_id
   * @param {Number} param0.booking_id
   */
  static notifyTimeoutResumeCommand = async ({
    imei,
    command,
    booking_log_id,
    booking_id,
  }) => {
    try {
      Logger.log("info", {
        message: "IOTService:notifyTimeoutResumeCommand:params",
        params: {
          imei,
          command,
          booking_log_id,
          booking_id,
        },
      });
      await axios.post(
        `${constants.WEBHOOK_SERVER}/webhooks/iot/timeout_resume`,
        {
          imei,
          command,
          booking_log_id,
          booking_id,
        }
      );
      Logger.log("success", {
        message: "IOTService:notifyTimeoutResumeCommand:success",
        params: {
          imei,
          command,
          booking_log_id,
          booking_id,
        },
      });
      return true;
    } catch (error) {
      Logger.log("error", {
        message: "IOTService:notifyTimeoutResumeCommand:catch-1",
        params: { error },
      });
      return false;
    }
  };

  /**
   *
   * @param {object} param0
   * @param {String} param0.imei
   * @param {String} param0.command
   * @param {Number} param0.booking_log_id
   * @param {Number} param0.booking_id
   */
  static notifyTimeoutEndCommand = async ({
    imei,
    command,
    booking_log_id,
    booking_id,
  }) => {
    try {
      Logger.log("info", {
        message: "IOTService:notifyTimeoutEndCommand:params",
        params: {
          imei,
          command,
          booking_log_id,
          booking_id,
        },
      });
      await axios.post(`${constants.WEBHOOK_SERVER}/webhooks/iot/timeout_end`, {
        imei,
        command,
        booking_log_id,
        booking_id,
      });
      Logger.log("success", {
        message: "IOTService:notifyTimeoutEndCommand:success",
        params: {
          imei,
          command,
          booking_log_id,
          booking_id,
        },
      });
      return true;
    } catch (error) {
      Logger.log("error", {
        message: "IOTService:notifyTimeoutEndCommand:catch-1",
        params: { error },
      });
      return false;
    }
  };

  /**
   *
   * @param {object} param0
   * @param {String} param0.imei
   * @param {String} param0.command
   * @param {Number} param0.booking_log_id
   * @param {Number} param0.booking_id
   */
  static notifyConfirmStartCommand = async ({
    imei,
    command,
    booking_log_id,
    booking_id,
  }) => {
    try {
      Logger.log("info", {
        message: "IOTService:notifyConfirmStartCommand:params",
        params: {
          imei,
          command,
          booking_log_id,
          booking_id,
        },
      });
      await axios.post(
        `${constants.WEBHOOK_SERVER}/webhooks/iot/confirm_start`,
        {
          imei,
          command,
          booking_log_id,
          booking_id,
        }
      );
      Logger.log("success", {
        message: "IOTService:notifyConfirmStartCommand:success",
        params: {
          imei,
          command,
          booking_log_id,
          booking_id,
        },
      });
      return true;
    } catch (error) {
      Logger.log("error", {
        message: "IOTService:notifyConfirmStartCommand:catch-1",
        params: { error },
      });
      return false;
    }
  };

  /**
   *
   * @param {object} param0
   * @param {String} param0.imei
   * @param {String} param0.command
   * @param {Number} param0.booking_log_id
   * @param {Number} param0.booking_id
   */
  static notifyConfirmPauseCommand = async ({
    imei,
    command,
    booking_log_id,
    booking_id,
  }) => {
    try {
      Logger.log("info", {
        message: "IOTService:notifyConfirmPauseCommand:params",
        params: {
          imei,
          command,
          booking_log_id,
          booking_id,
        },
      });
      await axios.post(
        `${constants.WEBHOOK_SERVER}/webhooks/iot/confirm_pause`,
        {
          imei,
          command,
          booking_log_id,
          booking_id,
        }
      );
      Logger.log("success", {
        message: "IOTService:notifyConfirmPauseCommand:success",
        params: {
          imei,
          command,
          booking_log_id,
          booking_id,
        },
      });
      return true;
    } catch (error) {
      Logger.log("error", {
        message: "IOTService:notifyConfirmPauseCommand:catch-1",
        params: { error },
      });
      return false;
    }
  };

  /**
   *
   * @param {object} param0
   * @param {String} param0.imei
   * @param {String} param0.command
   * @param {Number} param0.booking_log_id
   * @param {Number} param0.booking_id
   */
  static notifyConfirmResumeCommand = async ({
    imei,
    command,
    booking_log_id,
    booking_id,
  }) => {
    try {
      Logger.log("info", {
        message: "IOTService:notifyConfirmResumeCommand:params",
        params: {
          imei,
          command,
          booking_log_id,
          booking_id,
        },
      });
      await axios.post(
        `${constants.WEBHOOK_SERVER}/webhooks/iot/confirm_resume`,
        {
          imei,
          command,
          booking_log_id,
          booking_id,
        }
      );
      Logger.log("success", {
        message: "IOTService:notifyConfirmResumeCommand:success",
        params: {
          imei,
          command,
          booking_log_id,
          booking_id,
        },
      });
      return true;
    } catch (error) {
      Logger.log("error", {
        message: "IOTService:notifyConfirmResumeCommand:catch-1",
        params: { error },
      });
      return false;
    }
  };

  /**
   *
   * @param {object} param0
   * @param {String} param0.imei
   * @param {String} param0.command
   * @param {Number} param0.booking_log_id
   * @param {Number} param0.booking_id
   */
  static notifyConfirmEndCommand = async ({
    imei,
    command,
    booking_log_id,
    booking_id,
  }) => {
    try {
      Logger.log("info", {
        message: "IOTService:notifyConfirmEndCommand:params",
        params: {
          imei,
          command,
          booking_log_id,
          booking_id,
        },
      });
      await axios.post(`${constants.WEBHOOK_SERVER}/webhooks/iot/confirm_end`, {
        imei,
        command,
        booking_log_id,
        booking_id,
      });
      Logger.log("success", {
        message: "IOTService:notifyConfirmEndCommand:success",
        params: {
          imei,
          command,
          booking_log_id,
          booking_id,
        },
      });
      return true;
    } catch (error) {
      Logger.log("error", {
        message: "IOTService:notifyConfirmEndCommand:catch-1",
        params: { error },
      });
      return false;
    }
  };
}

module.exports = { IOTService };
