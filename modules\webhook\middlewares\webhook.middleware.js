const { prisma } = require("../../../config/prisma");
const constants = require("../../../constants");
const crypto = require("crypto");
const Logger = require("../../../utils/logger");
const { RazorpayOrder } = require("../../models/RazorpayOrder");
const { RazorpayPayment } = require("../../models/RazorpayPayment");
const environment = require("../../../environment");

const webhookMiddleware = {};

webhookMiddleware.razorpayDigestCheck = async (req, res, next) => {
  try {
    const secret = environment.RAZORPAY_ORDERS_WEBHOOK_SECRET;

    Logger.log("info", {
      message: "webhookMiddleware:razorpayDigestCheck:init",
      params: req.body,
    });

    const shasum = crypto.createHmac("sha256", secret);
    shasum.update(JSON.stringify(req.body));
    const digest = shasum.digest("hex");

    Logger.log("info", {
      message: "webhookMiddleware:razorpayDigestCheck:digest",
      params: { digest, header: req.headers["x-razorpay-signature"] },
    });
    if (digest === req.headers["x-razorpay-signature"]) {
      Logger.log("success", {
        message: "webhookMiddleware:razorpayDigestCheck:success",
      });
      return next();
    } else {
      Logger.log("error", {
        message: "webhookMiddleware:razorpayDigestCheck:catch-1",
        params: { error: constants.ERROR_CODES.INVALID_WEBHOOK_PAYLOAD },
      });
      return res.status(200).json({ status: "ok" });
    }
  } catch (error) {
    Logger.log("error", {
      message: "webhookMiddleware:razorpayDigestCheck:catch-1",
      params: { error },
    });
    return res.status(200).json({ status: "ok" });
  }
};

webhookMiddleware.isRazorpayOrderValid = async (req, res, next) => {
  try {
    const { orderObject, paymentOrder } = req;

    const razorpayOrder = new RazorpayOrder(orderObject);
    Logger.log("info", {
      message: "webhookMiddleware:isRazorpayOrderValid:params",
      params: { razorpayOrder, paymentOrder },
    });
    if (
      paymentOrder &&
      paymentOrder.payment_order_status !=
        constants.RAZORPAY_ORDER_STATUS.FAILED &&
      paymentOrder.payment_order_status !=
        constants.RAZORPAY_ORDER_STATUS.PAID &&
      paymentOrder.payment_order_status !=
        constants.RAZORPAY_ORDER_STATUS.TO_REFUND &&
      razorpayOrder.status === constants.RAZORPAY_ORDER_STATUS.PAID &&
      parseInt(razorpayOrder.amount_due) === 0
    ) {
      Logger.log("success", {
        message: "webhookMiddleware:isRazorpayOrderValid:success",
      });
      return next();
    } else {
      Logger.log("error", {
        message: "webhookMiddleware:isRazorpayOrderValid:catch-2",
        params: { error: constants.ERROR_CODES.INVALID_WEBHOOK_PAYLOAD },
      });
      return res.status(200).json({ status: "ok" });
    }
  } catch (error) {
    Logger.log("error", {
      message: "webhookMiddleware:isRazorpayOrderValid:catch-1",
      params: { error },
    });
    return res.status(200).json({ status: "ok" });
  }
};

webhookMiddleware.razorpayExtractPaymentOrder = async (req, res, next) => {
  try {
    const { event, payload } = req.body;
    const { order } = payload;
    const { entity } = order;
    const orderObject = new RazorpayOrder(entity);
    Logger.log("info", {
      message: "webhookMiddleware:razorpayExtractPaymentOrder:orderObject",
      params: orderObject,
    });
    const paymentOrder = await prisma.tbl_payment_orders.findUnique({
      where: { payment_order_id: orderObject.id },
      include: {
        tbl_users: {
          include: {
            tbl_wallets: true,
            tbl_user_types: true,
          },
        },
      },
    });
    Logger.log("info", {
      message: "webhookMiddleware:razorpayExtractPaymentOrder::paymentOrder",
      params: paymentOrder,
    });
    req.orderObject = orderObject;
    req.paymentOrder = paymentOrder;
    return next();
  } catch (error) {
    Logger.log("error", {
      message: "webhookMiddleware:razorpayExtractPaymentOrder:catch-1",
      params: { error },
    });
    return res.status(200).json({ status: "ok" });
  }
};

webhookMiddleware.razorpayExtractPaymentTransaction = async (
  req,
  res,
  next
) => {
  try {
    const { event, payload } = req.body;
    const { payment } = payload;
    const { entity } = payment;
    const paymentObject = new RazorpayPayment(entity);
    Logger.log("info", {
      message:
        "webhookMiddleware.razorpayExtractPaymentTransaction:paymentObject",
      params: paymentObject,
    });
    if (
      paymentObject &&
      paymentObject.status === constants.RAZORPAY_PAYMENT_STATUS_CODE.AUTHORIZED
    ) {
      Logger.log("warning", {
        message:
          "webhookMiddleware.razorpayExtractPaymentTransaction:ignore authorized webhook",
      });
      return res.json({ status: "ok" });
    } else {
      Logger.log("success", {
        message: "webhookMiddleware.razorpayExtractPaymentTransaction:success",
      });
      req.paymentObject = paymentObject;
      return next();
    }
  } catch (error) {
    Logger.log("error", {
      message: "webhookMiddleware.razorpayExtractPaymentTransaction:catch-1",
      params: { error },
    });
    return res.json({
      success: false,
      error: constants.ERROR_CODES.SERVER_ERROR,
    });
  }
};

module.exports = { webhookMiddleware };
