const express = require("express");
const router = express.Router();
const stationController = require("../controllers/station.controller");
const authMiddleware = require("../middlewares/auth.middleware");
const validationMiddleware = require("../middlewares/validation.middleware");
const vehicleController = require("../controllers/vehicle.controller");

//vehicle routes

// get all vehicles details

router.get("/nearby", authMiddleware, vehicleController.getNearbyVehicles);

router.get(
  "/deliverables",
  authMiddleware,
  vehicleController.getVehicleTypeForDelivery
);

router.get(
  "/code/:vehicle_code",
  authMiddleware,
  validationMiddleware.checkVehicleCodeInParam,
  vehicleController.getVehicleByVehicleCode
);

router.get(
  "/:id",
  authMiddleware,
  validationMiddleware.checkVehicleIDInParam,
  vehicleController.getVehicleByID
);



module.exports = router;
