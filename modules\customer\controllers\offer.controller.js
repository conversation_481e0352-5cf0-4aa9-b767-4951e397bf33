const { prisma } = require("../../../config/prisma");
const constants = require("../../../constants");
const { extractError } = require("../../../utils/error.utils");
const Logger = require("../../../utils/logger");
const { OfferService } = require("../../services/offer.service");
const { RazorpayService } = require("../../services/razorpay.service");
const { WalletService } = require("../../services/wallet.service");
const offerController = {};

/**
 *
 * @param {import("express").Request} req
 * @param {import("express").Response} res
 * @returns
 */
offerController.getLocationOffersMap = async (req, res) => {
  try {
    const { user } = req;
    Logger.log("info", {
      message: "offerController:getLocationOffersMap:params",
      params: { userID: user.user_id },
    });
    const locationOffersMap = await prisma.tbl_location_offers_map.findUnique({
      where: {
        location_offers_map_id: parseInt(user.location_offers_map_id),
      },
    });
    Logger.log("success", {
      message: "offerController:getLocationOffersMap:locationOffersMap",
      params: { userID: user.user_id, locationOffersMap },
    });
    return res.json({
      success: true,
      locationOffersMap,
    });
  } catch (error) {
    Logger.log("error", {
      message: "offerController:getLocationOffersMap:catch-1",
      params: { error },
    });
    return res.json({ success: false, error: extractError(error) });
  }
};

/**
 *
 * @param {import("express").Request} req
 * @param {import("express").Response} res
 * @returns
 */
offerController.getReservationTypes = async (req, res) => {
  try {
    const { user } = req;

    const reservationTypes = await OfferService.getAllocatedReservationTypes({
      user,
    });
    Logger.log("success", {
      message: "offerController:getReservationTypes:reservationTypes",
      params: {
        userID: user.user_id,
        reservationTypesLength: reservationTypes.length,
      },
    });
    return res.json({
      success: true,
      reservationTypes: reservationTypes,
    });
  } catch (error) {
    Logger.log("error", {
      message: "offerController:getReservationTypes:catch-1",
      params: { error },
    });
    return res.json({ success: false, error: extractError(error) });
  }
};

/**
 *
 * @param {import("express").Request} req
 * @param {import("express").Response} res
 * @returns
 */
offerController.getSubscriptionTypes = async (req, res) => {
  try {
    const { user } = req;
    const { page, vehicle_type_id } = req.query;
    Logger.log("info", {
      message: "offerController:getSubscriptionTypes:params",
      params: { userID: user.user_id, page, vehicle_type_id },
    });
    let skip = 0;
    let take = constants.OFFERS_PAGE_SIZE;
    if (page) {
      skip = (parseInt(page) - 1) * constants.OFFERS_PAGE_SIZE;
      take = constants.OFFERS_PAGE_SIZE;
    }
    const subscriptionTypes = await OfferService.getAllocatedSubscriptionTypes({
      user,
      skip,
      take,
      vehicleTypeID: vehicle_type_id ? parseInt(vehicle_type_id) : null,
    });
    Logger.log("success", {
      message: "offerController:getSubscriptionTypes:subscriptionTypes",
      params: {
        userID: user.user_id,
        subscriptionTypesLength: subscriptionTypes.length,
      },
    });
    return res.json({
      success: true,
      subscriptionTypes: subscriptionTypes,
      nextPage:
        subscriptionTypes.length < constants.OFFERS_PAGE_SIZE
          ? undefined
          : parseInt(parseInt(page) + 1),
    });
  } catch (error) {
    Logger.log("error", {
      message: "offerController:getSubscriptionTypes:catch-1",
      params: { error },
    });
    return res.json({ success: false, error: extractError(error) });
  }
};

/**
 *
 * @param {import("express").Request} req
 * @param {import("express").Response} res
 * @returns
 */
offerController.getPlanTypes = async (req, res) => {
  try {
    const { user } = req;
    const { page, vehicle_type_id } = req.query;
    Logger.log("info", {
      message: "offerController:getPlanTypes:params",
      params: { userID: user.user_id, page, vehicle_type_id },
    });
    let skip = 0;
    let take = constants.OFFERS_PAGE_SIZE;
    if (page) {
      skip = (parseInt(page) - 1) * constants.OFFERS_PAGE_SIZE;
      take = constants.OFFERS_PAGE_SIZE;
    }

    const planTypes = await OfferService.getAllocatedPlanTypes({
      user,
      skip,
      take,
      vehicleTypeID: vehicle_type_id ? parseInt(vehicle_type_id) : null,
    });
    Logger.log("success", {
      message: "offerController:getPlanTypes:planTypes",
      params: { userID: user.user_id, planTypesLength: planTypes.length },
    });
    return res.json({
      success: true,
      planTypes: planTypes,
      nextPage:
        planTypes.length < constants.OFFERS_PAGE_SIZE
          ? undefined
          : parseInt(parseInt(page) + 1),
    });
  } catch (error) {
    Logger.log("error", {
      message: "offerController:getPlanTypes:catch-1",
      params: { error },
    });
    return res.json({ success: false, error: extractError(error) });
  }
};

/**
 *
 * @param {import("express").Request} req
 * @param {import("express").Response} res
 * @returns
 */
offerController.purchaseSubscriptionType = async (req, res) => {
  try {
    const { user, subscriptionType } = req;
    const { subscription_type_id, payment_source } = req.body;
    Logger.log("info", {
      message: "offerController:purchaseSubscriptionType:params",
      params: {
        userID: user.user_id,
        subscription_type_id,
        payment_source,
      },
    });
    const { subscriptionPurchaseReceiptID } =
      await OfferService.createSubscriptionPurchaseReceipt({
        user,
        paymentSource: payment_source,
        subscriptionTypeID: parseInt(subscription_type_id),
      });
    Logger.log("info", {
      message:
        "offerController:purchaseSubscriptionType:subscriptionPurchaseReceiptID",
      params: {
        userID: user.user_id,
        subscription_type_id,
        payment_source,
        subscriptionPurchaseReceiptID,
      },
    });
    let paymentOrder = null;
    switch (payment_source) {
      case constants.PAYMENT_SOURCE.RAZORPAY: {
        paymentOrder = await RazorpayService.createRazorpayOrder({
          amount: parseInt(subscriptionType.amount),
          userID: parseInt(user.user_id),
          paymentOrderReceiptID: subscriptionPurchaseReceiptID,
          razorpayCustomerID: user.razorpay_customer_id,
          paymentPurpose: constants.PAYMENT_PURPOSE.SUBSCRIPTION_PURCHASE,
        });
        Logger.log("success", {
          message:
            "offerController:purchaseSubscriptionType:razorpay method:success",
          params: { userID: user.user_id, paymentOrder: paymentOrder },
        });

        break;
      }
      case constants.PAYMENT_SOURCE.WALLET_RAZORPAY: {
        const amountToBeDeductedFromWallet = Math.min(
          parseInt(user.tbl_wallets.balance),
          parseInt(subscriptionType.amount)
        );
        const amountToBeDeductedFromPaymentGateway =
          parseInt(subscriptionType.amount) - amountToBeDeductedFromWallet;

        Logger.log("info", {
          message:
            "offerController:purchaseSubscriptionType:wallet + razorpay method:calculated amount",
          params: {
            userID: user.user_id,
            subscriptionPurchaseReceiptID,
            amountToBeDeductedFromWallet,
            amountToBeDeductedFromPaymentGateway,
          },
        });
        const interWalletTransaction =
          await WalletService.paySubscriptionPurchasePaymentFromWallet({
            user,
            amount: amountToBeDeductedFromWallet,
            subscriptionPurchaseReceiptID,
            partial: Boolean(amountToBeDeductedFromPaymentGateway > 0),
          });
        Logger.log("info", {
          message:
            "offerController:purchaseSubscriptionType:wallet + razorpay method:interWalletTransaction",
          params: {
            userID: user.user_id,
            subscriptionPurchaseReceiptID,
            interWalletTransaction:
              interWalletTransaction[0].inter_wallet_transaction_id,
            partial: Boolean(amountToBeDeductedFromPaymentGateway > 0),
          },
        });
        if (amountToBeDeductedFromPaymentGateway == 0) {
          const newSubscription =
            await OfferService.assignSubscriptionTypeToUser({
              user,
              subscriptionPurchaseReceiptID: parseInt(
                subscriptionPurchaseReceiptID
              ),
            });
          Logger.log("success", {
            message:
              "offerController:purchaseSubscriptionType:wallet + razorpay method:success",
            params: {
              userID: user.user_id,
              subscriptionPurchaseReceiptID,
              newSubscription,
            },
          });
        } else {
          paymentOrder = await RazorpayService.createRazorpayOrder({
            amount: amountToBeDeductedFromPaymentGateway,
            userID: parseInt(user.user_id),
            paymentOrderReceiptID: parseInt(subscriptionPurchaseReceiptID),
            razorpayCustomerID: user.razorpay_customer_id,
            paymentPurpose: constants.PAYMENT_PURPOSE.SUBSCRIPTION_PURCHASE,
            interWalletTransactionID:
              interWalletTransaction[0].inter_wallet_transaction_id,
          });
          Logger.log("success", {
            message:
              "offerController:purchaseSubscriptionType:wallet + razorpay method:success",
            params: { userID: user.user_id, paymentOrder: paymentOrder },
          });
        }
        break;
      }
      default: {
        Logger.log("error", {
          message: "offerController:purchaseSubscriptionType:catch-2",
          params: { userID: user.user_id, payment_source },
        });
        break;
      }
    }
    return res.json({ success: true, paymentOrder: paymentOrder });
  } catch (error) {
    Logger.log("error", {
      message: "offerController:purchaseSubscriptionType:catch-1",
      params: { error },
    });
    return res.json({ success: false, error: extractError(error) });
  }
};

/**
 *
 * @param {import("express").Request} req
 * @param {import("express").Response} res
 * @returns
 */
offerController.purchasePlanType = async (req, res) => {
  try {
    const { user, planType } = req;
    const { plan_type_id, payment_source } = req.body;
    Logger.log("info", {
      message: "offerController:purchasePlanType:params",
      params: {
        userID: user.user_id,
        plan_type_id,
        payment_source,
      },
    });
    const { planPurchaseReceiptID } =
      await OfferService.createPlanPurchaseReceipt({
        user,
        paymentSource: payment_source,
        planTypeID: parseInt(plan_type_id),
      });
    Logger.log("info", {
      message: "offerController:purchasePlanType:planPurchaseReceiptID",
      params: {
        userID: user.user_id,
        plan_type_id,
        payment_source,
        planPurchaseReceiptID,
      },
    });
    let paymentOrder = null;
    switch (payment_source) {
      case constants.PAYMENT_SOURCE.RAZORPAY: {
        paymentOrder = await RazorpayService.createRazorpayOrder({
          amount: parseInt(planType.amount),
          userID: parseInt(user.user_id),
          paymentOrderReceiptID: parseInt(planPurchaseReceiptID),
          razorpayCustomerID: user.razorpay_customer_id,
          paymentPurpose: constants.PAYMENT_PURPOSE.PLAN_PURCHASE,
        });
        Logger.log("success", {
          message: "offerController:purchasePlanType:razorpay method:success",
          params: { userID: user.user_id, paymentOrder: paymentOrder },
        });

        break;
      }
      case constants.PAYMENT_SOURCE.WALLET_RAZORPAY: {
        const amountToBeDeductedFromWallet = Math.min(
          parseInt(user.tbl_wallets.balance),
          parseInt(planType.amount)
        );
        const amountToBeDeductedFromPaymentGateway =
          parseInt(planType.amount) - amountToBeDeductedFromWallet;

        Logger.log("info", {
          message:
            "offerController:purchasePlanType:wallet + razorpay method:calculated amount",
          params: {
            userID: user.user_id,
            planPurchaseReceiptID,
            amountToBeDeductedFromWallet,
            amountToBeDeductedFromPaymentGateway,
          },
        });
        const interWalletTransaction =
          await WalletService.payPlanPurchasePaymentFromWallet({
            user,
            amount: amountToBeDeductedFromWallet,
            planPurchaseReceiptID,
            partial: Boolean(amountToBeDeductedFromPaymentGateway > 0),
          });
        Logger.log("info", {
          message:
            "offerController:purchasePlanType:wallet + razorpay method:interWalletTransaction",
          params: {
            userID: user.user_id,
            planPurchaseReceiptID,
            interWalletTransaction:
              interWalletTransaction[0].inter_wallet_transaction_id,
            partial: Boolean(amountToBeDeductedFromPaymentGateway > 0),
          },
        });
        if (amountToBeDeductedFromPaymentGateway == 0) {
          const newPlan = await OfferService.assignPlanTypeToUser({
            user,
            planPurchaseReceiptID: parseInt(planPurchaseReceiptID),
          });
          Logger.log("success", {
            message:
              "offerController:purchasePlanType:wallet + razorpay method:success",
            params: {
              userID: user.user_id,
              planPurchaseReceiptID,
              newPlan,
            },
          });
        } else {
          paymentOrder = await RazorpayService.createRazorpayOrder({
            amount: amountToBeDeductedFromPaymentGateway,
            userID: parseInt(user.user_id),
            paymentOrderReceiptID: parseInt(planPurchaseReceiptID),
            razorpayCustomerID: user.razorpay_customer_id,
            paymentPurpose: constants.PAYMENT_PURPOSE.PLAN_PURCHASE,
            interWalletTransactionID:
              interWalletTransaction[0].inter_wallet_transaction_id,
          });
          Logger.log("success", {
            message:
              "offerController:purchasePlanType:wallet + razorpay method:success",
            params: { userID: user.user_id, paymentOrder: paymentOrder },
          });
        }
        break;
      }
      default: {
        Logger.log("error", {
          message: "offerController:purchasePlanType:catch-2",
          params: { userID: user.user_id, payment_source },
        });
        break;
      }
    }
    return res.json({ success: true, paymentOrder: paymentOrder });
  } catch (error) {
    Logger.log("error", {
      message: "offerController:purchasePlanType:catch-1",
      params: { error },
    });
    return res.json({ success: false, error: extractError(error) });
  }
};

/**
 *
 * @param {import("express").Request} req
 * @param {import("express").Response} res
 * @returns
 */
offerController.getUserSubscriptions = async (req, res) => {
  try {
    const { user } = req;
    const { page, vehicle_type_id } = req.query;
    Logger.log("info", {
      message: "offerController:getUserSubscriptions:params",
      params: { userID: user.user_id, page, vehicleTypeID: vehicle_type_id },
    });
    let skip = 0;
    let take = constants.OFFERS_PAGE_SIZE;
    if (page) {
      skip = (parseInt(page) - 1) * constants.OFFERS_PAGE_SIZE;
      take = constants.OFFERS_PAGE_SIZE;
    }

    const subscriptions = await OfferService.getUserSubscriptions({
      user,
      skip,
      take,
      vehicleTypeID: vehicle_type_id ? parseInt(vehicle_type_id) : null,
    });
    Logger.log("success", {
      message: "offerController:getUserOffers:subscriptions",
      params: {
        userID: user.user_id,
        subscriptionsLength: subscriptions.length,
      },
    });
    return res.json({
      success: true,
      subscriptions: subscriptions,
      nextPage:
        subscriptions.length < constants.OFFERS_PAGE_SIZE
          ? undefined
          : parseInt(parseInt(page) + 1),
    });
  } catch (error) {
    Logger.log("error", {
      message: "offerController:getUserOffers:catch-1",
      params: { error },
    });
    return res.json({ success: false, error: extractError(error) });
  }
};

/**
 *
 * @param {import("express").Request} req
 * @param {import("express").Response} res
 * @returns
 */
offerController.getUserCoupons = async (req, res) => {
  try {
    const { user } = req;
    const { page, vehicle_type_id } = req.query;
    Logger.log("info", {
      message: "offerController:getUserCoupons:params",
      params: { userID: user.user_id, page, vehicleTypeID: vehicle_type_id },
    });
    let skip = 0;
    let take = constants.OFFERS_PAGE_SIZE;
    if (page) {
      skip = (parseInt(page) - 1) * constants.OFFERS_PAGE_SIZE;
      take = constants.OFFERS_PAGE_SIZE;
    }

    const coupons = await OfferService.getUserCoupons({
      user,
      skip,
      take,
      vehicleTypeID: vehicle_type_id ? parseInt(vehicle_type_id) : null,
    });
    Logger.log("success", {
      message: "offerController:getUserCoupons:coupons",
      params: { userID: user.user_id, couponsLength: coupons.length },
    });
    return res.json({
      success: true,
      coupons: coupons,
      nextPage:
        plans.length < constants.OFFERS_PAGE_SIZE
          ? undefined
          : parseInt(parseInt(page) + 1),
    });
  } catch (error) {
    Logger.log("error", {
      message: "offerController:getUserCoupons:catch-1",
      params: { error },
    });
    return res.json({ success: false, error: extractError(error) });
  }
};

/**
 *
 * @param {import("express").Request} req
 * @param {import("express").Response} res
 * @returns
 */
offerController.getUserPlans = async (req, res) => {
  try {
    const { user } = req;
    const { page, vehicle_type_id } = req.query;
    Logger.log("info", {
      message: "offerController:getUserPlans:params",
      params: { userID: user.user_id, page, vehicleTypeID: vehicle_type_id },
    });
    let skip = 0;
    let take = constants.OFFERS_PAGE_SIZE;
    if (page) {
      skip = (parseInt(page) - 1) * constants.OFFERS_PAGE_SIZE;
      take = constants.OFFERS_PAGE_SIZE;
    }

    const plans = await OfferService.getUserPlans({
      user,
      skip,
      take,
      vehicleTypeID: vehicle_type_id ? parseInt(vehicle_type_id) : null,
    });
    Logger.log("success", {
      message: "offerController:getUserPlans:plans",
      params: { userID: user.user_id, plansLength: plans.length },
    });
    return res.json({
      success: true,
      plans: plans,
      nextPage:
        plans.length < constants.OFFERS_PAGE_SIZE
          ? undefined
          : parseInt(parseInt(page) + 1),
    });
  } catch (error) {
    Logger.log("error", {
      message: "offerController:getUserPlans:catch-1",
      params: { error },
    });
    return res.json({ success: false, error: extractError(error) });
  }
};

/**
 *
 * @param {import("express").Request} req
 * @param {import("express").Response} res
 * @returns
 */
offerController.getUserSubscriptionByID = async (req, res) => {
  try {
    const { user } = req;
    const { id } = req.params;

    const subscription = await OfferService.getUserSubscriptionByID({
      user,
      id: parseInt(id),
    });
    Logger.log("success", {
      message: "offerController:getUserSubscriptionByID:subscription",
      params: { userID: user.user_id, subscription },
    });
    return res.json({
      success: true,
      subscription,
    });
  } catch (error) {
    Logger.log("error", {
      message: "offerController:getUserSubscriptionByID:catch-1",
      params: { error },
    });
    return res.json({ success: false, error: extractError(error) });
  }
};

/**
 *
 * @param {import("express").Request} req
 * @param {import("express").Response} res
 * @returns
 */
offerController.getUserPlanByID = async (req, res) => {
  try {
    const { user } = req;
    const { id } = req.params;

    const plan = await OfferService.getUserPlanByID({
      user,
      id: parseInt(id),
    });
    Logger.log("success", {
      message: "offerController:getUserPlanByID:plan",
      params: { userID: user.user_id, plan },
    });
    return res.json({
      success: true,
      plan,
    });
  } catch (error) {
    Logger.log("error", {
      message: "offerController:getUserPlanByID:catch-1",
      params: { error },
    });
    return res.json({ success: false, error: extractError(error) });
  }
};

module.exports = offerController;
