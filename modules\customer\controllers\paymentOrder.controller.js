const constants = require("../../../constants");
const { extractError } = require("../../../utils/error.utils");
const Logger = require("../../../utils/logger");
const { getInvoice } = require("../../services/invoiceGenerator.service");
const { PaymentOrderService } = require("../../services/paymentOrder.service");

const paymentOrderController = {};

/**
 *
 * @param {import("express").Request} req
 * @param {import("express").Response} res
 * @returns
 */
paymentOrderController.getPaymentOrders = async (req, res) => {
  try {
    const { user } = req;
    const { page } = req.query;
    Logger.log("success", {
      message: "paymentOrderController:getOfferTypes:params",
      params: { userID: user.user_id, page },
    });
    let skip = 0;
    let take = constants.ORDERS_PAGE_SIZE;
    if (page) {
      skip = (parseInt(page) - 1) * constants.ORDERS_PAGE_SIZE;
      take = constants.ORDERS_PAGE_SIZE;
    }
    const paymentOrders = await PaymentOrderService.getPaymentOrderOfUser({
      user,
      skip,
      take,
    });
    Logger.log("success", {
      message: "paymentOrderController:getOfferTypes:paymentOrders",
      params: { userID: user.user_id, paymentOrders: paymentOrders.length },
    });
    return res.json({
      success: true,
      paymentOrders,
      nextPage:
        paymentOrders.length < constants.ORDERS_PAGE_SIZE
          ? undefined
          : parseInt(parseInt(page) + 1),
    });
  } catch (error) {
    Logger.log("error", {
      message: "paymentOrderController:getOfferTypes:catch-1",
      params: { error },
    });
    return res.json({ success: false, error: extractError(error) });
  }
};

/**
 *
 * @param {import("express").Request} req
 * @param {import("express").Response} res
 * @returns
 */
paymentOrderController.getPaymentOrderByID = async (req, res) => {
  try {
    const { user } = req;
    const { id } = req.params;

    const paymentOrderByID = await PaymentOrderService.getPaymentOrderByID({
      user,
      id,
    });
    Logger.log("success", {
      message: "paymentOrderController:getPaymentOrderByID:paymentOrderByID",
      params: { paymentOrderByID },
    });
    return res.json({
      success: true,
      paymentOrder: paymentOrderByID,
    });
  } catch (error) {
    Logger.log("error", {
      message: "paymentOrderController:getPaymentOrderByID:catch-1",
      params: { error },
    });
    return res.json({ success: false, error: extractError(error) });
  }
};

/**
 *
 * @param {import("express").Request} req
 * @param {import("express").Response} res
 * @returns
 */
paymentOrderController.downloadPaymentOrderByID = async (req, res) => {
  try {
    const { user } = req;
    const { id } = req.params;

    const paymentOrderByID = await PaymentOrderService.getPaymentOrderByID({
      user,
      id,
    });
    Logger.log("success", {
      message: "paymentOrderController:getPaymentOrderByID:paymentOrderByID",
      params: { paymentOrderByID },
    });
    getInvoice({ paymentOrder: paymentOrderByID }).then((html) => {
      res.status(200);
      res.set("Content-Type", "text/html");
      res.send(Buffer.from(html));
    });
  } catch (error) {
    Logger.log("error", {
      message: "paymentOrderController:getPaymentOrderByID:catch-1",
      params: { error },
    });
    return res.json({ success: false, error: extractError(error) });
  }
};

module.exports = paymentOrderController;
