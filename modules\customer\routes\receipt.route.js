const express = require("express");
const router = express.Router();

const authMiddleware = require("../middlewares/auth.middleware");
const checkMiddleware = require("../middlewares/check.middleware");
const validationMiddleware = require("../middlewares/validation.middleware");
const bookingController = require("../controllers/booking.controller");
const {
  interceptorMiddleware,
} = require("../middlewares/interceptor.middleware");
const { receiptController } = require("../controllers/receipt.controller");

router.post(
  "/generate_booking_receipt",
  authMiddleware,
  receiptController.generateBookingReceipt
);

router.post(
  "/generate_reservation_receipt",
  authMiddleware,
  receiptController.generateReservationReceipt
);

module.exports = router;
