const { prisma } = require("../../config/prisma");

const Logger = require("../../utils/logger");
const crypto = require("crypto");
const { Bike } = require("../models/mongoose/bike");
const constants = require("../../constants");
const { extractLoggingDataFromDeviceDataContent } = require("../../utils/iot");
const moment = require("moment");

class VehicleService {
  constructor() {}

  /**
   *
   * @param {object} param0
   * @param {Number} param0.vehicleID
   */
  static saveVehiclePrevState = async ({ vehicleID }) => {
    try {
      Logger.log("info", {
        message: "VehicleService:saveVehiclePrevState:params",
        params: { vehicleID },
      });
      const currentVehicleData = await prisma.tbl_vehicles.findUnique({
        where: { vehicle_id: parseInt(vehicleID) },
      });
      const previousVehicleData =
        await prisma.tbl_vehicles_prev_state.findUnique({
          where: { vehicle_id: parseInt(vehicleID) },
        });
      const { vehicle_id, ...currentData } = currentVehicleData;
      if (previousVehicleData) {
        const updatedVehiclePrevState =
          await prisma.tbl_vehicles_prev_state.update({
            where: { vehicle_id: parseInt(vehicleID) },
            data: { ...currentData },
          });
        Logger.log("success", {
          message: "VehicleService:saveVehiclePrevState:updated",
          params: { updatedVehiclePrevState },
        });
        return updatedVehiclePrevState;
      } else {
        const newVehiclePrevState = await prisma.tbl_vehicles_prev_state.create(
          {
            data: { ...currentVehicleData },
          }
        );
        Logger.log("success", {
          message: "VehicleService:saveVehiclePrevState:created",
          params: { newVehiclePrevState },
        });
        return newVehiclePrevState;
      }
    } catch (error) {
      Logger.log("error", {
        message: "VehicleService:saveVehiclePrevState:catch-1",
        params: { error },
      });
    }
  };

  /**
   *
   * @param {object} param0
   * @param {Number} param0.vehicleID
   * @param {Number} param0.lat
   * @param {Number} param0.lng
   * @returns
   */
  static updateVehicleLocation = async ({ vehicleID, lat, lng }) => {
    try {
      Logger.log("info", {
        message: "VehicleService:updateVehicleLocation:params",
        params: { vehicleID, lat, lng },
      });

      const updatedVehicle = await prisma.tbl_vehicles.update({
        where: {
          vehicle_id: parseInt(vehicleID),
        },
        data: {
          lat: parseFloat(lat),
          lng: parseFloat(lng),
        },
      });

      Logger.log("success", {
        message: "VehicleService:updateVehicleLocation:updatedVehicleID",
        params: { updatedVehicleID: updatedVehicle.vehicle_id, lat, lng },
      });
      return updatedVehicle;
    } catch (error) {
      Logger.log("error", {
        message: "VehicleService:updateVehicleLocation:catch-1",
        params: { error },
      });
      return null;
    }
  };

  /**
   *
   * @param {object} param0
   * @param {Number} param0.vehicleID
   * @param {Number} param0.lat
   * @param {Number} param0.lng
   * @returns
   */
  static bulkUpdateOldIOTVehiclesIOTInfo = async () => {
    try {
      Logger.log("info", {
        message: "VehicleService:bulkUpdateOldIOTVehiclesIOTInfo:init",
      });

      const oldIOTVehicles = await prisma.tbl_vehicles.findMany({
        where: {
          is_disabled: false,
          is_old_iot: true,
        },
      });

      const oldIOTVehicleNumbers = oldIOTVehicles.map((v) => v.vehicle_number);
      const oldIOTVehiclesIOTData = await Bike.find({
        np: { $in: oldIOTVehicleNumbers },
      });

      const oldIOTVehicleUpdateTransactionEntries = oldIOTVehiclesIOTData.map(
        (_v) => {
          return prisma.tbl_vehicles.updateMany({
            where: {
              vehicle_number: _v.np,
            },
            data: {
              battery: _v.estBattery,
              total_distance_travelled: _v.estRange,
              lng: _v.location?.coordinates?.[0],
              lat: _v.location?.coordinates?.[1],
            },
          });
        }
      );
      const oldIOTVehicleUpdateTransaction = await prisma.$transaction(
        oldIOTVehicleUpdateTransactionEntries
      );

      Logger.log("success", {
        message:
          "VehicleService:bulkUpdateOldIOTVehiclesIOTInfo:updatedVehicleNumbers",
        params: {
          oldIOTVehiclesIOTDataLength: oldIOTVehiclesIOTData?.length,
          updatedVehicleNumbers: oldIOTVehicleUpdateTransaction,
          oldIOTVehicleUpdateTransactionEntries:
            oldIOTVehicleUpdateTransactionEntries.length,
        },
      });
      return true;
    } catch (error) {
      Logger.log("error", {
        message: "VehicleService:bulkUpdateOldIOTVehiclesIOTInfo:catch-1",
        params: { error },
      });
      return null;
    }
  };
  static logNewIOTVehicleData = async ({ imei, content }) => {
    try {
      Logger.log("info", {
        message: "VehicleService:logNewIOTVehicleData:params",
        params: { imei },
      });
      const loggingData = extractLoggingDataFromDeviceDataContent({
        content: content,
        imei: imei,
      });
      if (!loggingData) {
        Logger.log("error", {
          message: "VehicleService:logNewIOTVehicleData:no logging data",
          params: { imei },
        });
        return null;
      }
      await prisma.tbl_iot_telemetry_data.create({
        data: {
          iot_imei: imei,
          timestamp: new Date(),
          iot_timestamp: moment(loggingData.timestamp).toDate(),
          latitude: loggingData.latitude,
          longitude: loggingData.longitude,
          internal_battery_voltage: loggingData.internal_battery_voltage,
          internal_battery_current: loggingData.internal_battery_current,
          internal_battery_percent: loggingData.internal_battery_percent,
          external_voltage: loggingData.external_voltage,
          external_extended_voltage: loggingData.external_extended_voltage,
          analog_input_1: loggingData.analog_input_1,
          analog_input_2: loggingData.analog_input_2,
          trip_odometer: loggingData.trip_odometer,
          total_odometer: loggingData.total_odometer,
          x_axis: loggingData.x_axis,
          y_axis: loggingData.y_axis,
          z_axis: loggingData.z_axis,
          sleep_mode: loggingData.sleep_mode,
          gsm_cell_id: loggingData.gsm_cell_id,
          gsm_area_code: loggingData.gsm_area_code,
          digital_input_1: loggingData.digital_input_1,
          digital_input_2: loggingData.digital_input_2,
          digital_input_3: loggingData.digital_input_3,
          digital_input_4: loggingData.digital_input_4,
          digital_output_1: loggingData.digital_output_1,
          digital_output_2: loggingData.digital_output_2,
          dout1_overcurrent: loggingData.dout1_overcurrent,
          dout2_overcurrent: loggingData.dout2_overcurrent,
          extended_analog_input_1: loggingData.extended_analog_input_1,
          extended_analog_input_2: loggingData.extended_analog_input_2,
          instant_movement: loggingData.instant_movement,
          iso6709_coordinates: loggingData.iso6709_coordinates,
        },
      });
      Logger.log("success", {
        message: "VehicleService:logNewIOTVehicleData:logged",
        params: { imei },
      });
      return true;
    } catch (error) {
      Logger.log("error", {
        message: "VehicleService:logNewIOTVehicleData:catch-1",
        params: { error },
      });
      return null;
    }
  };

  /**
   *
   * @param {object} param0
   * @param {String} param0.imei
   * @param {object} param0.data
   * @returns
   */
  static updateVehicleDataByIMEI = async ({ imei, data }) => {
    try {
      Logger.log("info", {
        message: "VehicleService:updateVehicleDataByIMEI:params",
        params: { imei, data },
      });

      const vehicle = await prisma.tbl_vehicles.findFirst({
        where: {
          iot_imei: imei,
        },
      });
      const _externalVoltage = data.externalVoltage
        ? Math.round(parseInt(data.externalVoltage) * 0.001)
        : 0;
      const _externalExtendedVoltage = data.externalExtendedVoltage
        ? parseInt(data.externalExtendedVoltage)
        : 0;
      const analogInput2 = data.analogInput2
        ? ((Math.round(parseInt(data.analogInput2) * 0.001) -
            constants.MINIMUM_VEHICLE_VOLTAGES.OKINAWA) /
            constants.VEHICLE_VOLTAGE_RANGE.OKINAWA) *
          100
        : 0;

      const updatedVehicle = await prisma.tbl_vehicles.update({
        where: {
          vehicle_id: vehicle.vehicle_id,
        },
        data: {
          lat: parseFloat(data.latitude),
          lng: parseFloat(data.longitude),
          battery: Math.max(analogInput2, 0),
          total_distance_travelled: Math.max(
            Math.floor((analogInput2 * constants.VEHICLE_RANGE.OKINAWA) / 100),
            0
          ),
          // _externalVoltage < 65 ? _externalVoltage : _externalExtendedVoltage,
        },
      });

      Logger.log("success", {
        message: "VehicleService:updateVehicleDataByIMEI:updatedVehicleID",
        params: { updatedVehicleID: updatedVehicle.vehicle_id, imei },
      });
      return updatedVehicle;
    } catch (error) {
      Logger.log("error", {
        message: "VehicleService:updateVehicleDataByIMEI:catch-1",
        params: { error },
      });
      return null;
    }
  };

  /**
   *
   * @param {object} param0
   * @param {String} param0.imei
   * @param {Number} param0.lat
   * @param {Number} param0.lng
   * @returns
   */
  static updateVehicleLocationByIMEI = async ({ imei, lat, lng }) => {
    try {
      Logger.log("info", {
        message: "VehicleService:updateVehicleLocationByIMEI:params",
        params: { imei, lat, lng },
      });

      const vehicle = await prisma.tbl_vehicles.findFirst({
        where: {
          iot_imei: imei,
        },
      });
      const updatedVehicle = await prisma.tbl_vehicles.update({
        where: {
          vehicle_id: vehicle.vehicle_id,
        },
        data: {
          lat: parseFloat(lat),
          lng: parseFloat(lng),
        },
      });

      Logger.log("success", {
        message: "VehicleService:updateVehicleLocationByIMEI:updatedVehicleID",
        params: { updatedVehicleID: updatedVehicle.vehicle_id, imei, lat, lng },
      });
      return updatedVehicle;
    } catch (error) {
      Logger.log("error", {
        message: "VehicleService:updateVehicleLocationByIMEI:catch-1",
        params: { error },
      });
      return null;
    }
  };

  /**
   *
   * @param {object} param0
   * @param {Number} param0.vehicleID
   * @returns
   */
  static getVehicleByID = async ({ vehicleID }) => {
    try {
      Logger.log("info", {
        message: "VehicleService:getVehicleByID:params",
        params: { vehicleID },
      });

      const vehicle = await prisma.tbl_vehicles.findUnique({
        where: {
          vehicle_id: parseInt(vehicleID),
        },

        include: {
          tbl_vehicle_types: true,
        },
      });

      Logger.log("success", {
        message: "VehicleService:getVehicleByID:vehicle",
        params: { vehicle: vehicle.vehicle_id },
      });
      return vehicle;
    } catch (error) {
      Logger.log("error", {
        message: "VehicleService:getVehicleByID:catch-1",
        params: { error },
      });
      return null;
    }
  };
}

module.exports = { VehicleService };
