var admin = require("firebase-admin");
const constants = require("../../../constants");
const Logger = require("../../../utils/logger");

const { prisma } = require("../../../config/prisma");
module.exports = async function (socket, next) {
  Logger.log("info", { message: "authSocketMiddleware:start" });
  if (socket && socket.handshake && socket.handshake.auth) {
    try {
      let { token } = socket.handshake.auth;
      const decodedIdToken = await admin.auth().verifyIdToken(token);
      Logger.log("info", { message: "authSocketMiddleware:success", params: { uid: decodedIdToken.uid, phone: decodedIdToken.phone_number } });

      const dbUser = await prisma.tbl_users.findFirst({
        where: { firebase_id: decodedIdToken.uid },
      });
      if(dbUser && dbUser.is_disabled){
        next(new Error(constants.ERROR_CODES.INVALID_USER.message));
      }else{
        socket.handshake.auth.firebase_id = decodedIdToken.uid;
        next();
      }
      
    } catch (error) {
      Logger.log("error", { message: "authSocketMiddleware:catch-2", params: { error: constants.ERROR_CODES.USER_AUTH_TOKEN_EXPIRED } });
      next(new Error(constants.ERROR_CODES.USER_AUTH_TOKEN_EXPIRED.message));
    }
  } else {
    Logger.log("error", { message: "authSocketMiddleware:catch-1", params: { error: constants.ERROR_CODES.USER_AUTH_TOKEN_NOT_FOUND } });
    next(new Error(constants.ERROR_CODES.USER_AUTH_TOKEN_NOT_FOUND.message));
  }
};
