const { connectMongoDB } = require("../config/mongoose");
const { prisma } = require("../config/prisma");
const { razorpayInstance } = require("../config/razorpay");
const environmentVariables = require("../environment");
const { User } = require("../modules/models/mongoose/user");

connectMongoDB();

const deleteExistingCustomerData = async () => {
  // await prisma.tbl_push_notification_tokens.deleteMany();
  // await prisma.tbl_booking_logs.deleteMany();
  // await prisma.tbl_bookings.deleteMany();
  // await prisma.tbl_payment_transactions.deleteMany();
  // await prisma.tbl_payment_orders.deleteMany();
  // await prisma.tbl_reservations.deleteMany();
  // await prisma.tbl_coupon_purchase_receipts.deleteMany();

  await prisma.tbl_users.deleteMany({ where: { user_id: { gt: 9 } } });
  // await prisma.tbl_wallet_recharge_transactions.deleteMany();
  // await prisma.tbl_reservation_receipts.deleteMany();
  // await prisma.tbl_booking_receipts.deleteMany();
  // await prisma.tbl_inter_wallet_transactions.deleteMany();

  // await prisma.tbl_wallets.deleteMany({ where: { wallet_id: { gt: 5 } } });
};
const clampToInt32 = (value) => {
  const minInt32 = -2147483648; // Minimum value for INT4
  const maxInt32 = 2147483647; // Maximum value for INT4

  if (value < minInt32) {
    return minInt32; // Clamp to minimum if value is too small
  } else if (value > maxInt32) {
    return maxInt32; // Clamp to maximum if value is too large
  } else {
    return Math.round(value); // Ensure the value is an integer
  }
};
const syncExistingUsersWalletData = async () => {
  const mongoUsers = await User.find(
    { $or: [{ wallet: { $ne: null } }, { isSd: true }] },
    { name: 1, email: 1, phone: 1, wallet: 1, isSd: 1 }
  );
  console.log({ l: mongoUsers.length });
  const pgUsers = await prisma.tbl_users.findMany({
    where: {
      firebase_id: { in: mongoUsers.map((mu) => mu._id) },
      wallet_id: { equals: null },
    },
  });
  console.log({ l: pgUsers.length });
  const map = {};

  mongoUsers.forEach((u) => {
    map[u._id] = { wallet: u.wallet, isSd: u.isSd };
  });
  const _pgusers = pgUsers.map((pgu) => {
    return {
      firebase_id: pgu.firebase_id,
      ...map[pgu.firebase_id],
      user_id: pgu.user_id,
    };
  });
  // //   console.log(_pgusers);
  const data = _pgusers.map((user) => {
    return prisma.tbl_users.update({
      where: { user_id: user.user_id },
      data: {
        tbl_wallets: {
          create: {
            balance: user.wallet ? user.wallet * 100 : 0,
            security_deposit: user.isSd ? 20000 : 0,
          },
        },
      },
    });
  });
  const t = await prisma.$transaction(data);
  console.log("Operation ended!");
  //   await prisma.tbl_users.deleteMany({ where: { user_id: { gt: 9 } } });
};

const main = async () => {
  const mongoUsers = await User.find(
    { $or: [{ wallet: { $ne: null } }, { isSd: true }] },
    { name: 1, email: 1, phone: 1, wallet: 1, isSd: 1 }
  );
  console.log({ l: mongoUsers.length });
  const pgUsers = await prisma.tbl_users.findMany();
  const workableMongoUsers = mongoUsers.filter((mu) => {
    const i = pgUsers.findIndex((pu) => pu.firebase_id == mu._id);
    return i < 0;
  });
  console.log({ workableMongoUsers: workableMongoUsers.length });
  const map = {};
  workableMongoUsers.forEach((u) => {
    map[u._id] = { wallet: u.wallet, isSd: u.isSd };
  });

  const data = workableMongoUsers.map((user) => {
    return prisma.tbl_users.create({
      data: {
        firebase_id: user._id,
        phone_number: user.phone,
        first_name: user.name.split(" ")[0],
        last_name: user.name.split(" ")[1],
        email: user.email,
        user_type_id: 1,
        location_offers_map_id: 1,
      },
    });
  });

  const walletData = workableMongoUsers.map((user) => {
    return prisma.tbl_users.update({
      where: {
        phone_number: user.phone,
      },
      data: {
        tbl_wallets: {
          create: {
            balance: user.wallet ? clampToInt32(user.wallet * 100) : 0,
            security_deposit: user.isSd ? 20000 : 0,
          },
        },
      },
    });
  });

  const t = await prisma.$transaction(data);
  // console.log({ walletData: walletData.length });
  const w = await prisma.$transaction(walletData);
  console.log({ w: w[0] });
  console.log("Operation ended!");
};
main();
// deleteExistingCustomerData();

// getCustomers();
