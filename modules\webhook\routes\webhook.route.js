const express = require("express");
const router = express.Router();
const { webhookMiddleware } = require("../middlewares/webhook.middleware");
const razorpayWebhookController = require("../controllers/razorpay.webhook");
const iotWebhookController = require("../controllers/iot.webhook");

router.post(
  "/razorpay/orders",
  webhookMiddleware.razorpayDigestCheck,
  webhookMiddleware.razorpayExtractPaymentOrder,
  webhookMiddleware.isRazorpayOrderValid,
  razorpayWebhookController.ordersWebhook
);
router.post(
  "/razorpay/payments",
  webhookMiddleware.razorpayDigestCheck,
  webhookMiddleware.razorpayExtractPaymentTransaction,
  razorpayWebhookController.paymentsWebhook
);

router.post("/iot/confirm_start", iotWebhookController.confirmStartBooking);
router.post("/iot/confirm_start", iotWebhookController.confirmStartBooking);
router.post("/iot/confirm_pause", iotWebhookController.confirmPauseBooking);
router.post("/iot/confirm_resume", iotWebhookController.confirmResumeBooking);
router.post("/iot/confirm_end", iotWebhookController.confirmEndBooking);

router.post("/iot/timeout_start", iotWebhookController.timeoutStartBooking);
router.post("/iot/timeout_pause", iotWebhookController.timeoutPauseBooking);
router.post("/iot/timeout_resume", iotWebhookController.timeoutResumeBooking);
router.post("/iot/timeout_end", iotWebhookController.timeoutEndBooking);


module.exports = router;
