const { winstonLogger } = require("../config/winston");

class Logger {
  static pageLogger = (page, obj) => {
    obj
      ? console.log(`-----> : ${page} |=====|`, obj)
      : console.log(`-----> : ${page}`);
  };

  static log = (status = "INFO", { message, params }) => {
    try {
      const statusArray = String(status).split(":");

      statusArray.forEach((s) => {
        if (s === "error") {
          console.log(s, message, params);
        }
        switch (s.toLowerCase()) {
          case "info": {
            winstonLogger.info(message, { data: params });
            break;
          }
          case "success": {
            winstonLogger.success(message, { data: params });
            break;
          }
          case "warning": {
            winstonLogger.warning(message, { data: params });
            break;
          }
          case "error": {
            winstonLogger.error(message, { data: params });
            break;
          }
          case "wash": {
            winstonLogger.wash(message, { data: params });
            break;
          }
          default: {
            winstonLogger.error("log level error", {
              data: { s, message, ...params },
            });
            break;
          }
        }
      });
    } catch (error) {}
  };
}

module.exports = Logger;
