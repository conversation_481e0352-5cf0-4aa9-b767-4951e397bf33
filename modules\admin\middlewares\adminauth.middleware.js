const constants = require("../../../constants");
const environmentVariables = require("../../../environment");
const Logger = require("../../../utils/logger");
module.exports = async function (req, res, next) {
  if (
    req.headers.authorization &&
    req.headers.authorization.startsWith("Bearer ")
  ) {
    try {
      let idToken = req.headers.authorization.split("Bearer ")[1];
      if (idToken === environmentVariables.ADMIN_API_KEY) {
        Logger.log("success", {
          message: "adminAuthMiddleware:success",
          params: { uid: "admin" },
        });
        return next();
      }else{
        Logger.log("error", {
          message: "adminAuthMiddleware:catch-3",
          params: { error: constants.ERROR_CODES.INVALID_USER },
        });
        return res.send({
          error: constants.ERROR_CODES.INVALID_USER,
        });
      }
    } catch (error) {
      Logger.log("error", {
        message: "adminAuthMiddleware:catch-2",
        params: { error },
      });
      return res.send({ error: constants.ERROR_CODES.USER_AUTH_TOKEN_EXPIRED });
    }
  } else {
    Logger.log("error", {
      message: "adminAuthMiddleware:catch-1",
      params: { error: constants.ERROR_CODES.USER_AUTH_TOKEN_NOT_FOUND },
    });
    return res.send({ error: constants.ERROR_CODES.USER_AUTH_TOKEN_NOT_FOUND });
  }
};
