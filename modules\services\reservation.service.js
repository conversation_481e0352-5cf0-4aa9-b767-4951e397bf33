var admin = require("firebase-admin");
const Logger = require("../../utils/logger");
const { prisma } = require("../../config/prisma");
const constants = require("../../constants");
const moment = require("moment");
const {
  reservationSocketController,
} = require("../socket/controllers/reservation.socket.controller");
const { ReservationReceiptService } = require("./reservationReceipt.service");
const { Bike } = require("../models/mongoose/bike");
const { BikeStatusEnum } = require("../models/mongoose/enums");

class ReservationService {
  constructor() {}

  /**
   *
   * @param {object} param0
   * @param {import("@prisma/client").tbl_users} param0.user
   */
  static getCompletedUserReservations = async ({ user, skip, take }) => {
    try {
      Logger.log("info", {
        message: "ReservationService:getCompletedUserReservations:params",
        params: {
          userID: user.user_id,
          skip,
          take,
        },
      });
      const reservations = await prisma.tbl_reservations.findMany({
        where: {
          user_id: parseInt(user.user_id),
          OR: [
            { reservation_status: constants.RESERVATION_STATUS.ENDED },
            { reservation_status: constants.RESERVATION_STATUS.TERMINATED },
          ],
        },
        include: {
          tbl_reservation_receipts: true,

          tbl_vehicles: true,
        },
        orderBy: {
          reservation_start_at: "desc",
        },
        skip: skip,
        take: take,
      });

      Logger.log("success", {
        message: "ReservationService:getCompletedUserReservations:success",
        params: {
          userID: user.user_id,
          reservationsLength: reservations?.length,
        },
      });
      return reservations;
    } catch (error) {
      Logger.log("error", {
        message: "ReservationService:getCompletedUserReservations:catch-1",
        params: { error },
      });
      throw error;
    }
  };

  /**
   *
   * @param {object} param0
   * @param {import("@prisma/client").tbl_users} param0.user
   * @param {Number} param0.reservationID
   */
  static getUserReservationByID = async ({ user, reservationID }) => {
    try {
      Logger.log("info", {
        message: "ReservationService:getUserReservationByID:params",
        params: {
          userID: user.user_id,
          reservationID,
        },
      });
      const reservation = await prisma.tbl_reservations.findFirst({
        where: {
          user_id: parseInt(user.user_id),
          reservation_id: parseInt(reservationID),
        },
        include: {
          tbl_reservation_receipts: {
            include: {
              tbl_coupons: true,
              tbl_plans: true,
              tbl_subscriptions: true,
              tbl_inter_wallet_transactions: true,
            },
          },

          tbl_vehicles: true,
        },
      });

      Logger.log("success", {
        message: "ReservationService:getUserReservationByID:success",
        params: {
          userID: user.user_id,
          reservationID: reservation.reservation_id,
        },
      });
      return reservation;
    } catch (error) {
      Logger.log("error", {
        message: "ReservationService:getUserReservationByID:catch-1",
        params: { error },
      });
      throw error;
    }
  };

  /**
   *
   * @param {object} param0
   * @param {import("@prisma/client").tbl_users} param0.user
   * @param {import("@prisma/client").tbl_vehicle_types} param0.vehicle
   * @param {Number} param0.reservationTypeID
   */
  static reserveVehicle = async ({ user, vehicle, reservationTypeID }) => {
    try {
      Logger.log("info", {
        message: "ReservationService:reserveVehicle:params",
        params: {
          userID: user.user_id,
          vehicle: vehicle.vehicle_id,
          reservationTypeID,
        },
      });
      const reservationType = await prisma.tbl_reservation_types.findUnique({
        where: {
          reservation_type_id: parseInt(reservationTypeID),
        },
      });
      const reserveVehicleTransaction = await prisma.$transaction(
        async (tx) => {
          const updatedVehicle = await tx.tbl_vehicles.update({
            where: {
              vehicle_id: parseInt(vehicle.vehicle_id),
            },
            data: {
              vehicle_status: constants.VEHICLE_STATUS.RESERVED,
            },
          });
          Logger.log("info", {
            message:
              "ReservationService:reserveVehicle:$transaction:updatedVehicle",
            params: {
              userID: user.user_id,
              vehicleID: updatedVehicle.vehicle_id,
              vehicleStatus: updatedVehicle.vehicle_status,
            },
          });

          const newReservation = await tx.tbl_reservations.create({
            data: {
              reservation_status: constants.RESERVATION_STATUS.RESERVED,
              vehicle_id: parseInt(vehicle.vehicle_id),
              reservation_station_id: parseInt(vehicle.station_id),
              reservation_initiation_at: new Date(),
              reservation_start_at: new Date(),
              user_id: parseInt(user.user_id),
              reservation_type_id: parseInt(reservationTypeID),
              estimated_reservation_end_at: moment(new Date()).add(
                reservationType.reservation_time,
                "milliseconds"
              ),
            },
            include: {
              tbl_vehicles: true,
              tbl_reservation_types: true,
              tbl_stations: true,
            },
          });
          Logger.log("info", {
            message:
              "ReservationService:reserveVehicle:$transaction:newReservation",
            params: {
              userID: user.user_id,
              vehicleID: updatedVehicle.vehicle_id,
              reservationID: newReservation.reservation_id,
            },
          });

          return {
            reservation: newReservation,
            vehicle: updatedVehicle,
          };
        }
      );
      //update old vehicle database status
      try {
        
        await Bike.findOneAndUpdate(
          {
            np: reserveVehicleTransaction.vehicle.vehicle_number,
          },
          {
            status: BikeStatusEnum.Booked,
          }
        );
        Logger.log("success", {
          message: "ReservationService:reserveVehicle:updated old IOT",
        });
      } catch (error) {
        Logger.log("error", {
          message: "ReservationService:reserveVehicle:catch-2",
          params: { error },
        });
      }
      Logger.log("success", {
        message: "ReservationService:reserveVehicle:$transaction:success",
        params: {
          userID: user.user_id,
          vehicleID: vehicle.vehicle_id,
        },
      });
      return reserveVehicleTransaction;
    } catch (error) {
      Logger.log("error", {
        message: "ReservationService:reserveVehicle:catch-1",
        params: { error },
      });
      throw error;
    }
  };

  /**
   *
   * @param {object} param0
   * @param {import("@prisma/client").tbl_users} param0.user
   * @param {import("@prisma/client").tbl_reservations} param0.reservation
   */
  static endReservation = async ({ user, reservation }) => {
    try {
      Logger.log("info", {
        message: "ReservationService:endReservation:params",
        params: {
          userID: user.user_id,
          reservationID: reservation.reservation_id,
        },
      });
      const endReservationTransaction = await prisma.$transaction(
        async (tx) => {
          const updatedVehicle = await tx.tbl_vehicles.update({
            where: {
              vehicle_id: parseInt(reservation.vehicle_id),
            },
            data: {
              vehicle_status: constants.VEHICLE_STATUS.READY,
            },
          });
          Logger.log("info", {
            message:
              "ReservationService:endReservation:$transaction:updatedVehicle",
            params: {
              userID: user.user_id,
              vehicleID: updatedVehicle.vehicle_id,
              vehicleStatus: updatedVehicle.vehicle_status,
            },
          });

          const updatedReservation = await tx.tbl_reservations.update({
            where: {
              reservation_id: parseInt(reservation.reservation_id),
            },
            data: {
              reservation_status: constants.RESERVATION_STATUS.ENDED,
              reservation_end_at: new Date(),
            },
            include: {
              tbl_users: {
                include: {
                  tbl_location_offers_map: true,
                },
              },
              tbl_reservation_types: true,
              tbl_vehicles: {
                include: {
                  tbl_vehicle_types: true,
                },
              },
            },
          });

          Logger.log("info", {
            message:
              "ReservationService:endReservation:$transaction:newReservation",
            params: {
              userID: user.user_id,
              vehicleID: updatedVehicle.vehicle_id,
              reservationID: updatedReservation.reservation_id,
            },
          });

          return {
            reservation: updatedReservation,
            vehicle: updatedVehicle,
          };
        }
      );
      //update old vehicle database status
      try {
        await Bike.findOneAndUpdate(
          {
            np: endReservationTransaction.vehicle.vehicle_number,
          },
          {
            status: BikeStatusEnum.Idle,
          }
        );
      } catch (error) {
        Logger.log("error", {
          message: "ReservationService:endReservation:catch-2",
          params: { error },
        });
      }
      // generate receipt for reservation
      await ReservationReceiptService.generateReservationReceipt({
        reservation: endReservationTransaction.reservation,
      });
      // send update over socket to client
      reservationSocketController.emitOngoingReservationOnUpdate({
        ongoingReservation: null,
        firebaseID: endReservationTransaction.reservation.tbl_users.firebase_id,
      });
      Logger.log("success", {
        message: "ReservationService:endReservation:$transaction:success",
        params: {
          userID: user.user_id,
          vehicleID: reservation.vehicle_id,
        },
      });
      return endReservationTransaction;
    } catch (error) {
      Logger.log("error", {
        message: "ReservationService:endReservation:catch-1",
        params: { error },
      });
      throw error;
    }
  };

  static endReservationWhenDue = async () => {
    try {
      Logger.log("info", {
        message: "ReservationService:endReservationWhenDue:init",
      });
      const currentEndTime = new Date();
      const reservationsToBeEnded = await prisma.tbl_reservations.findMany({
        where: {
          estimated_reservation_end_at: { lt: currentEndTime },
          reservation_status: constants.RESERVATION_STATUS.RESERVED,
        },
        include: {
          tbl_users: true,
        },
      });
      Logger.log("info", {
        message:
          "ReservationService:endReservationWhenDue:reservationsToBeEnded",
        params: {
          currentEndTime,
          reservationIDsToBeEnded: reservationsToBeEnded.map(
            (reservationToBeEnded) => reservationToBeEnded.reservation_id
          ),
        },
      });
      const promisesForReservationEnding = [];
      reservationsToBeEnded.forEach((reservationToBeEnded) => {
        promisesForReservationEnding.push(
          this.endReservation({
            reservation: reservationToBeEnded,
            user: reservationToBeEnded.tbl_users,
          })
        );
      });
      await Promise.all(promisesForReservationEnding);
      Logger.log("info", {
        message: "ReservationService:endReservationWhenDue:success",
      });
    } catch (error) {
      Logger.log("error", {
        message: "ReservationService:endReservationWhenDue:catch-1",
        params: { error },
      });
      // throw error;
    }
  };
}

module.exports = { ReservationService };
