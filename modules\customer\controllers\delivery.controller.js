const Logger = require("../../../utils/logger");
const { extractError } = require("../../../utils/error.utils");
const constants = require("../../../constants");

const { DeliveryService } = require("../../services/delivery.service");

const deliveryController = {};

/**
 *
 * @param {import("express").Request} req
 * @param {import("express").Response} res
 * @returns
 */
deliveryController.getDeliveryBookings = async (req, res) => {
  try {
    const { user } = req;
    const { page } = req.query;
    Logger.log("info", {
      message: "deliveryController:getDeliveryBookings:params",
      params: { userID: user.user_id, page },
    });
    let skip = 0;
    let take = constants.BOOKING_HISTORY_PAGE_SIZE;
    if (page) {
      skip = (parseInt(page) - 1) * constants.BOOKING_HISTORY_PAGE_SIZE;
      take = constants.BOOKING_HISTORY_PAGE_SIZE;
    }

    const deliveryBookings = await DeliveryService.getUserDeliveryBookings({
      user,
      skip,
      take,
    });

    Logger.log("success", {
      message: "deliveryController:getDeliveryBookings:deliveryBookings",
      params: {
        userID: user.user_id,
        deliveryBookingsLength: deliveryBookings.length,
      },
    });
    return res.json({
      success: true,
      deliveryBookings,
      nextPage:
        deliveryBookings.length < constants.BOOKING_HISTORY_PAGE_SIZE
          ? undefined
          : parseInt(parseInt(page) + 1),
    });
  } catch (error) {
    Logger.log("error", {
      message: "deliveryController:getDeliveryBookings:catch-1",
      params: { error },
    });
    return res.json({ success: false, error: extractError(error) });
  }
};

/**
 *
 * @param {import("express").Request} req
 * @param {import("express").Response} res
 * @returns
 */
deliveryController.getDeliveryBookingByID = async (req, res) => {
  try {
    const { user } = req;
    const { id } = req.params;

    Logger.log("info", {
      message: "deliveryController:getDeliveryBookingByID:params",
      params: { userID: user.user_id },
    });

    const deliveryBooking = await DeliveryService.getUserDeliveryBookingByID({
      user,
      deliveryBookingID: parseInt(id),
    });

    Logger.log("success", {
      message: "deliveryController:getDeliveryBookingByID:deliveryBookings",
      params: {
        userID: user.user_id,
        deliveryBookingID: deliveryBooking.delivery_booking_id,
      },
    });
    return res.json({
      success: true,
      deliveryBooking,
    });
  } catch (error) {
    Logger.log("error", {
      message: "deliveryController:getDeliveryBookingByID:catch-1",
      params: { error },
    });
    return res.json({ success: false, error: extractError(error) });
  }
};

/**
 *
 * @param {import("express").Request} req
 * @param {import("express").Response} res
 * @returns
 */
deliveryController.requestBooking = async (req, res) => {
  try {
    const { user } = req;
    const { vehicle_type_id, lat, lng, duration } = req.body;
    Logger.log("info", {
      message: "deliveryController:start:params",
      params: {
        userID: user.user_id,
        vehicleTypeID: vehicle_type_id,
        lat,
        lng,
        duration,
      },
    });
    const requestBookingTransaction =
      await DeliveryService.requestDeliveryBooking({
        user,
        vehicleTypeID: vehicle_type_id,
        lat,
        lng,
        duration,
      });
    Logger.log("success", {
      message: "deliveryController:start:success",
      params: { userID: user.user_id, requestBookingTransaction },
    });
    return res.json({
      success: true,
      deliveryBooking: requestBookingTransaction.deliveryBooking,
    });
  } catch (error) {
    Logger.log("error", {
      message: "deliveryController:start:catch-1",
      params: { error },
    });
    return res.json({ success: false, error: extractError(error) });
  }
};

module.exports = deliveryController;
