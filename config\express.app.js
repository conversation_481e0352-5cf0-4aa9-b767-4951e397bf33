const express = require("express");
const cors = require("cors");
const { morganMiddleware } = require("./morgan");

const expressApp = express();
expressApp.enable("trust proxy");
expressApp.use(morganMiddleware);
expressApp.use(cors());
expressApp.use(
  express.json({
    limit: "5mb",
    verify: (req, res, buffer) => (req.rawBody = buffer),
  })
);
expressApp.use(express.urlencoded({ limit: "5mb", extended: false }));
expressApp.use(express.json({ extended: false }));
module.exports = { expressApp };
