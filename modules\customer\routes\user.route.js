const express = require("express");
const router = express.Router();
const userController = require("../controllers/user.controller");
const authMiddleware = require("../middlewares/auth.middleware");
const checkMiddleware = require("../middlewares/check.middleware");
const validationMiddleware = require("../middlewares/validation.middleware");

//user routes

//get user info : auth check
router.get("/me", authMiddleware, userController.getUserInfo);

router.post(
  "/update",
  authMiddleware,
  validationMiddleware.checkUserUpdateParams,
  userController.updateUserInfo
);

router.post("/kyc", authMiddleware, userController.getAadhaarVerificationOTP);

router.post("/kyc_verify", authMiddleware, userController.verifyAadhaarOTP);

router.get(
  "/push_notification_tokens",
  authMiddleware,
  userController.getAllPushNotificationToken
);

router.post(
  "/push_notification_tokens",
  authMiddleware,
  userController.addPushNotificationToken
);

router.delete(
  "/push_notification_tokens/:push_notification_token",
  authMiddleware,
  checkMiddleware.doesPushNotificationTokenBelongToUser,
  userController.deletePushNotificationToken
);

module.exports = router;
