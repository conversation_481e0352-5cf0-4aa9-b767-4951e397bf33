var admin = require("firebase-admin");
const Logger = require("../../utils/logger");
const { UserService } = require("./user.service");

class FirebasePushNotificationService {
  constructor() {}
  static sendBulkMessages = ({ messages }) => {
    try {
      const result = admin.messaging().sendAll(messages);
      Logger.log("info", {
        message: "firebasePushNotificationService:sendBulkMessages:success",
        params: { result },
      });
    } catch (error) {
      Logger.log("error", {
        message: "firebasePushNotificationService:sendBulkMessages:catch-1",
        params: { error },
      });
    }
  };
  static sendMulticastMessages = ({ message }) => {
    try {
      const result = admin.messaging().sendMulticast(message);
      Logger.log("info", {
        message:
          "firebasePushNotificationService:sendMulticastMessages:success",
        params: { result },
      });
    } catch (error) {
      Logger.log("error", {
        message:
          "firebasePushNotificationService:sendMulticastMessages:catch-1",
        params: { error },
      });
    }
  };

  static sendMultiCastMessageToUserIDs = async ({
    title,
    description,
    userIDs,
    icon,
  }) => {
    try {
      const message = await UserService.generateFirebasePushNotification({
        title,
        description,
        icon,
        userIDs,
      });
      const result = message ? admin.messaging().sendMulticast(message) : null;
      Logger.log("info", {
        message:
          "firebasePushNotificationService:sendMulticastMessages:success",
        params: { result },
      });
    } catch (error) {
      Logger.log("error", {
        message:
          "firebasePushNotificationService:sendMulticastMessages:catch-1",
        params: { error },
      });
    }
  };

  static sendMultiCastMessageToUserID = async ({
    title,
    description,
    userID,
    icon,
  }) => {
    try {
      const message = await UserService.generateFirebasePushNotification({
        title,
        description,
        icon,
        userIDs: [userID],
      });
      const result = message ? admin.messaging().sendMulticast(message) : null;
      Logger.log("info", {
        message:
          "firebasePushNotificationService:sendMulticastMessages:success",
        params: { result },
      });
    } catch (error) {
      Logger.log("error", {
        message:
          "firebasePushNotificationService:sendMulticastMessages:catch-1",
        params: { error },
      });
    }
  };
}

module.exports = { FirebasePushNotificationService };
