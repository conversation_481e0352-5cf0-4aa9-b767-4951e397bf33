const moment = require("moment");
const { prisma } = require("../../config/prisma");
const constants = require("../../constants");
const Logger = require("../../utils/logger");
const { UserService } = require("./user.service");
const { WalletService } = require("./wallet.service");

// const { RazorpayService } = require("./razorpay.service");

class PaymentOrderService {
  constructor() {}

  /**
   *
   * @param {object} param0
   * @param {import("@prisma/client").tbl_users & {tbl_user_types:import("@prisma/client").tbl_user_types} & {tbl_location_offers_map:import("@prisma/client").tbl_location_offers_map}} param0.user
   * @param {Number} param0.take
   * @param {Number} param0.skip
   * @returns
   */
  static getPaymentOrderOfUser = async ({ user, take, skip }) => {
    try {
      Logger.log("info", {
        message: "PaymentOrderService:getPaymentOrderOfUser:params",
        params: {
          userID: user.user_id,
          userType: user.user_type_id,
          skip,
          take,
        },
      });
      const paymentOrders = await prisma.tbl_payment_orders.findMany({
        where: {
          user_id: parseInt(user.user_id),
        },
        include: {
          tbl_payment_transactions: true,
        },

        skip: skip,
        take: take,
        orderBy: [
          {
            created_at: "desc",
          },
          { updated_at: "desc" },
        ],
      });
      Logger.log("info", {
        message: "PaymentOrderService:getPaymentOrderOfUser:paymentOrders",
        params: {
          userID: user.user_id,
          userType: user.user_type_id,
          paymentOrders: paymentOrders.length,
        },
      });

      return paymentOrders;
    } catch (error) {
      Logger.log("error", {
        message: "PaymentOrderService:getPaymentOrderOfUser:catch-1",
        params: { error },
      });
      return null;
    }
  };

  /**
   *
   * @param {object} param0
   * @param {import("@prisma/client").tbl_users & {tbl_user_types:import("@prisma/client").tbl_user_types}} param0.user
   * @param {Number} param0.id
   * @returns
   */
  static getPaymentOrderByID = async ({ user, id }) => {
    try {
      Logger.log("info", {
        message: "PaymentOrderService:getPaymentOrderByID:params",
        params: { userID: user.user_id, id },
      });

      const paymentOrderByID = await prisma.tbl_payment_orders.findFirst({
        where: {
          user_id: user.user_id,
          payment_order_id: id,
        },
        include: {
          tbl_payment_transactions: true,
          tbl_inter_wallet_transactions: true,
          tbl_users: true,
        },
      });

      let payment_purpose_receipt = null;

      switch (paymentOrderByID.payment_purpose) {
        case constants.PAYMENT_PURPOSE.WALLET_RECHARGE: {
          payment_purpose_receipt =
            await prisma.tbl_wallet_recharge_transactions.findUnique({
              where: {
                wallet_recharge_transaction_id: parseInt(
                  paymentOrderByID.payment_purpose_receipt_id
                ),
              },
            });
          break;
        }
        case constants.PAYMENT_PURPOSE.SECURITY_DEPOSIT: {
          payment_purpose_receipt =
            await prisma.tbl_security_deposit_transactions.findUnique({
              where: {
                security_deposit_transaction_id: parseInt(
                  paymentOrderByID.payment_purpose_receipt_id
                ),
              },
            });
          break;
        }
        case constants.PAYMENT_PURPOSE.PLAN_PURCHASE: {
          payment_purpose_receipt =
            await prisma.tbl_plan_purchase_receipts.findUnique({
              where: {
                plan_purchase_receipt_id: parseInt(
                  paymentOrderByID.payment_purpose_receipt_id
                ),
              },
              include: {
                tbl_plan_types: true,
              },
            });
          break;
        }
        case constants.PAYMENT_PURPOSE.SUBSCRIPTION_PURCHASE: {
          payment_purpose_receipt =
            await prisma.tbl_subscription_purchase_receipts.findUnique({
              where: {
                subscription_purchase_receipt_id: parseInt(
                  paymentOrderByID.payment_purpose_receipt_id
                ),
              },
              include: {
                tbl_subscription_types: true,
              },
            });
          break;
        }
      }

      Logger.log("info", {
        message: "PaymentOrderService:getPaymentOrderByID:paymentOrderByID",
        params: {
          userID: user.user_id,
          paymentOrderByID,
          payment_purpose_receipt,
        },
      });

      return { ...paymentOrderByID, payment_purpose_receipt };
    } catch (error) {
      Logger.log("error", {
        message: "PaymentOrderService:getPaymentOrderByID:catch-1",
        params: { error },
      });
      return null;
    }
  };

  static failPendingPaymentOrders = async () => {
    try {
      Logger.log("info", {
        message: "PaymentOrderService:failPendingPaymentOrders:init",
      });

      const failPendingPaymentOrderValidity = new Date(
        Date.now() - constants.PAYMENT_ORDER_FAIL_AFTER_TIME
      );

      Logger.log("info", {
        message:
          "PaymentOrderService:failPendingPaymentOrders:failPendingPaymentOrderValidity",
        params: { failPendingPaymentOrderValidity },
      });

      const pendingPaymentOrders = await prisma.tbl_payment_orders.findMany({
        where: {
          updated_at: { lt: failPendingPaymentOrderValidity },
          OR: [
            { payment_order_status: constants.RAZORPAY_ORDER_STATUS.ATTEMPTED },
            { payment_order_status: constants.RAZORPAY_ORDER_STATUS.CREATED },
          ],
        },
        include: {
          tbl_users: true,
        },
      });

      const failedPendingPaymentOrders =
        await prisma.tbl_payment_orders.updateMany({
          where: {
            updated_at: { lt: failPendingPaymentOrderValidity },
            OR: [
              {
                payment_order_status: constants.RAZORPAY_ORDER_STATUS.ATTEMPTED,
              },
              { payment_order_status: constants.RAZORPAY_ORDER_STATUS.CREATED },
            ],
          },
          data: {
            payment_order_status: constants.RAZORPAY_ORDER_STATUS.FAILED,
          },
        });
      const interWalletTransactionsToBeRevertedForOfferPurchase =
        pendingPaymentOrders.map((pendingPaymentOrder) => {
          if (pendingPaymentOrder.inter_wallet_transaction_id) {
            Logger.log("info", {
              message:
                "PaymentOrderService:failPendingPaymentOrders:interWalletTransactionInvolved",
            });
            if (
              pendingPaymentOrder.payment_purpose ==
              constants.PAYMENT_PURPOSE.SUBSCRIPTION_PURCHASE
            ) {
              Logger.log("info", {
                message:
                  "PaymentOrderService:failPendingPaymentOrders:paymentPurpose",
                params: {
                  pendingPaymentOrder: pendingPaymentOrder.payment_order_id,
                  paymentPurpose:
                    constants.PAYMENT_PURPOSE.SUBSCRIPTION_PURCHASE,
                },
              });
              return WalletService.revertOfferPurchasePaymentFromWallet({
                userWalletID: pendingPaymentOrder.tbl_users.user_id,
                interWalletTransactionID:
                  pendingPaymentOrder.inter_wallet_transaction_id,
                subscriptionPurchaseReceiptID: parseInt(
                  pendingPaymentOrder.payment_purpose_receipt_id
                ),
              });
            } else if (
              pendingPaymentOrder.payment_purpose ==
              constants.PAYMENT_PURPOSE.PLAN_PURCHASE
            ) {
              Logger.log("info", {
                message:
                  "PaymentOrderService:failPendingPaymentOrders:paymentPurpose",
                params: {
                  pendingPaymentOrder: pendingPaymentOrder.payment_order_id,
                  paymentPurpose:
                    constants.PAYMENT_PURPOSE.SUBSCRIPTION_PURCHASE,
                },
              });
              return WalletService.revertOfferPurchasePaymentFromWallet({
                userWalletID: pendingPaymentOrder.tbl_users.user_id,
                interWalletTransactionID:
                  pendingPaymentOrder.inter_wallet_transaction_id,

                planPurchaseReceiptID: parseInt(
                  pendingPaymentOrder.payment_purpose_receipt_id
                ),
              });
            }
          }
        });

      Logger.log("info", {
        message: "PaymentOrderService:failPendingPaymentOrders:paymentOrderID",
        params: {
          pendingPaymentOrders: failedPendingPaymentOrders.count,
          interWalletTransactionsToBeRevertedForOfferPurchase:
            interWalletTransactionsToBeRevertedForOfferPurchase.length,
        },
      });
      return true;
    } catch (error) {
      Logger.log("error", {
        message: "PaymentOrderService:failPendingPaymentOrders:catch-1",
        params: { error },
      });
      return null;
    }
  };
}
module.exports = { PaymentOrderService };
