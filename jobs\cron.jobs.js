const cron = require("node-cron");

const { UserService } = require("../modules/services/user.service");
const {
  PaymentOrderService,
} = require("../modules/services/paymentOrder.service");
const {
  ReservationService,
} = require("../modules/services/reservation.service");
const constants = require("../constants");
const { VehicleService } = require("../modules/services/vehicle.service");
const { IOTService } = require("../modules/services/iot.service");

class CronJobScheduler {
  constructor() {}

  static failPendingPaymentOrdersCronjob = () => {
    cron.schedule("*/1 * * * *", PaymentOrderService.failPendingPaymentOrders);
  };
  static endOverdueReservationsCronjob = () => {
    cron.schedule("*/1 * * * *", ReservationService.endReservationWhenDue);
  };
  static bulkUpdateOldIOTVehiclesIOTInfoCronjob = () => {
    cron.schedule(
      "*/1 * * * *",
      VehicleService.bulkUpdateOldIOTVehiclesIOTInfo
    );
  };
  static revertTimedOutIOTCommandsCronjob = () => {
    cron.schedule(
      `*/${constants.IOT_COMMAND_TIMEOUT} * * * * *`,
      IOTService.revertTimeoutCommands
    );
  };
}

module.exports = { CronJobScheduler };
