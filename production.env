NODE_ENV=production 

PORT= 8090
BROKER_PORT = 1111
MONGODB_URL="*************************************************************************************"
DATABASE_URL="postgresql://prisma_client_user:Yesthisisnewpassword@127.0.0.1/customer_db"
DATABASE_URI="postgresql://raghav227996399:<EMAIL>/customer_db"
SLACK_ERROR_NOTIFICATION_HOOK="*********************************************************************************"
SLACK_WASH_BOOKING_NOTIFICATION_HOOK="*********************************************************************************"
SLACK_AUTH_TOKEN='*********************************************************'
RAZORPAY_SECRET_='eXVkNkPOXGFdfuyfjwbzCu41'
RAZORPAY_KEY_ID_='rzp_test_zZFmQtf6z0C4M8'
RAZORPAY_WEBHOOK_SECRET='VvxEFV5CnOVJ3DHNZH4Te9j8'
RAZORPAY_ORDERS_WEBHOOK_SECRET = 'VvxEFV5CnOVJ3DHNZH4Te9j8'
RAZORPAY_PAYMENTS_WEBHOOK_SECRET = 'VvxEFV5CnOVJ3DHNZH4Te9j8'
KYC_URL = 'https://api-preproduction.signzy.app/api/v3/getOkycOtp'
KYC_OTP_URL = 'https://api-preproduction.signzy.app/api/v3/fetchOkycData'
KYC_API_TOKEN = '3p26WWGgv55bJ9lR3XRjTMFqoOqz4GZU'

ADMIN_API_KEY = 'wejrpjscfodwsfhwjwojfpmsdsldjnflshfp'

RAZORPAY_SECRET='fFUkcFWReWMM1zqwaYK29ZAZ'
RAZORPAY_KEY_ID='***********************'

