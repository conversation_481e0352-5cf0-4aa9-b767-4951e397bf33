const { prisma } = require("../../../config/prisma");
const { socketIO } = require("../../../config/socket.io");
const constants = require("../../../constants");
const Logger = require("../../../utils/logger");
const { UserService } = require("../../services/user.service");

const walletSocketController = {};

/**
 *
 * @param {object} param0
 * @param {String} param0.firebaseID
 */
walletSocketController.emitWalletOnFirstConnect = async ({
  // prisma,
  firebaseID,
}) => {
  Logger.log("info", {
    message: "walletSocketController:emitWalletOnFirstConnect",
    params: { firebaseID },
  });
  try {
    const user = await prisma.tbl_users.findFirst({
      where: { firebase_id: firebaseID },
      include: {
        tbl_wallets: true,
      },
    });

    // socketIO.to(firebaseID).emit(constants.SOCKET_EVENTS.ON_WALLET_UPDATE, user.tbl_wallets);

    if (UserService.isWalletActivated(user)) {
      Logger.log("success", {
        message: "walletSocketController:emitWalletOnFirstConnect:success",
        params: { user },
      });
      socketIO
        .to(firebaseID)
        .emit(constants.SOCKET_EVENTS.ON_WALLET_UPDATE, user.tbl_wallets);
    } else {
      Logger.log("info", {
        message:
          "walletSocketController:emitWalletOnFirstConnect:wallet_not_attached",
        params: { error: constants.ERROR_CODES.WALLET_NOT_ACTIVATED },
      });
    }
  } catch (error) {
    Logger.log("error", {
      message: "walletSocketController:emitWalletOnFirstConnect:catch-1",
      params: { error },
    });
  }
};

walletSocketController.emitWalletOnUpdate = async ({ wallet, firebaseID }) => {
  Logger.log("info", {
    message: "walletSocketController:emitWalletOnUpdate",
    params: { firebaseID },
  });
  try {
    socketIO
      .to(firebaseID)
      .emit(constants.SOCKET_EVENTS.ON_WALLET_UPDATE, wallet);
  } catch (error) {
    Logger.log("error", {
      message: "walletSocketController:emitWalletOnUpdate:catch-1",
      params: { error },
    });
  }
};

/**
 *
 * @param {object} param0
 * @param {Number} param0.userID
 */
walletSocketController.emitWalletByUserIDOnUpdate = async ({ userID }) => {
  Logger.log("info", {
    message: "walletSocketController:emitWalletByUserIDOnUpdate",
    params: { userID },
  });
  try {
    const user = await prisma.tbl_users.findUnique({
      where: { user_id: userID },
      include: {
        tbl_wallets: true,
      },
    });
    socketIO
      .to(user.firebase_id)
      .emit(constants.SOCKET_EVENTS.ON_WALLET_UPDATE, user.tbl_wallets);
  } catch (error) {
    Logger.log("error", {
      message: "walletSocketController:emitWalletByUserIDOnUpdate:catch-1",
      params: { error },
    });
  }
};

module.exports = walletSocketController;
