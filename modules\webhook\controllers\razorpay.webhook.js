const Logger = require("../../../utils/logger");
const { prisma } = require("../../../config/prisma");
const { RazorpayOrder } = require("../../models/RazorpayOrder");
const constants = require("../../../constants");
const { OfferService } = require("../../services/offer.service");
const razorpayWebhookController = {};

/**
 *
 * @param {import("express").Request} req
 * @param {import("express").Response} res
 * @returns
 */
razorpayWebhookController.ordersWebhook = async (req, res) => {
  try {
    const { orderObject, paymentOrder } = req;

    const razorpayOrder = new RazorpayOrder(orderObject);
    Logger.log("info", {
      message: "razorpayWebhookController:ordersWebhook:params",
      params: { razorpayOrder, paymentOrder },
    });

    // db order is still in progress and not failed
    // razorpay order is paid
    // there is no due amount
    switch (razorpayOrder.notes.payment_purpose) {
      case constants.PAYMENT_PURPOSE.SECURITY_DEPOSIT: {
        const paidPaymentOrderTransaction = await prisma.$transaction([
          prisma.tbl_payment_orders.update({
            where: {
              payment_order_id: paymentOrder.payment_order_id,
            },
            data: {
              payment_order_status: razorpayOrder.status,
            },
            include: {
              tbl_payment_transactions: {
                orderBy: {
                  created_at: "desc",
                },
              },
            },
          }),
          prisma.tbl_security_deposit_transactions.update({
            where: {
              security_deposit_transaction_id: parseInt(razorpayOrder.receipt),
            },
            data: {
              security_deposit_transaction_status:
                constants.SECURITY_DEPOSIT_STATUS.SUCCESS,
            },
          }),
          prisma.tbl_wallets.update({
            where: {
              wallet_id: paymentOrder.tbl_users.tbl_wallets.wallet_id,
            },
            data: {
              security_deposit: {
                increment: parseInt(razorpayOrder.amount_paid),
              },
            },
          }),
        ]);
        Logger.log("success", {
          message:
            "razorpayWebhookController:ordersWebhook:transaction updated",
          params: {
            paidPaymentOrderTransaction,
            paymentPurpose: constants.PAYMENT_PURPOSE.SECURITY_DEPOSIT,
          },
        });

        break;
      }

      case constants.PAYMENT_PURPOSE.WALLET_RECHARGE: {
        const paidPaymentOrderTransaction = await prisma.$transaction([
          prisma.tbl_payment_orders.update({
            where: {
              payment_order_id: paymentOrder.payment_order_id,
            },
            data: {
              payment_order_status: razorpayOrder.status,
            },
            include: {
              tbl_payment_transactions: {
                orderBy: {
                  created_at: "desc",
                },
              },
            },
          }),
          prisma.tbl_wallet_recharge_transactions.update({
            where: {
              wallet_recharge_transaction_id: parseInt(razorpayOrder.receipt),
            },
            data: {
              wallet_recharge_transaction_status:
                constants.WALLET_RECHARGE_TRANSACTION_STATUS.SUCCESS,
            },
          }),
          prisma.tbl_wallets.update({
            where: {
              wallet_id: paymentOrder.tbl_users.tbl_wallets.wallet_id,
            },
            data: {
              balance: {
                increment: parseInt(razorpayOrder.amount_paid),
              },
            },
          }),
        ]);
        Logger.log("success", {
          message:
            "razorpayWebhookController:ordersWebhook:transaction updated",
          params: {
            paidPaymentOrderTransaction,
            paymentPurpose: constants.PAYMENT_PURPOSE.WALLET_RECHARGE,
          },
        });
        break;
      }

      case constants.PAYMENT_PURPOSE.SUBSCRIPTION_PURCHASE: {
        const canAssignSubscriptionTypeToUser =
          await OfferService.canAssignSubscriptionTypeToUser({
            user: paymentOrder.tbl_users,
          });
        if (!canAssignSubscriptionTypeToUser) {
          Logger.log("error", {
            message: "razorpayWebhookController:ordersWebhook:catch-2",
            params: { error: constants.ERROR_CODES.MAX_SUBSCRIPTION_LIMIT },
          });
          break;
        } else {
          const subscriptionAllotmentTransaction = await prisma.$transaction(
            async (tx) => {
              const updatedPaymentOrder = await tx.tbl_payment_orders.update({
                where: {
                  payment_order_id: razorpayOrder.id,
                },
                data: {
                  payment_order_status: razorpayOrder.status,
                },
                include: {
                  tbl_payment_transactions: {
                    orderBy: {
                      created_at: "desc",
                    },
                  },
                },
              });

              if (updatedPaymentOrder.inter_wallet_transaction_id) {
                await tx.tbl_inter_wallet_transactions.update({
                  where: {
                    inter_wallet_transaction_id:
                      updatedPaymentOrder.inter_wallet_transaction_id,
                  },
                  data: {
                    inter_wallet_transaction_status:
                      constants.INTER_WALLET_TRANSACTION_STATUS.SUCCESS,
                  },
                });
              }
              return { updatedPaymentOrder };
            }
          );

          const newSubscription =
            await OfferService.assignSubscriptionTypeToUser({
              user: paymentOrder.tbl_users,
              subscriptionPurchaseReceiptID: parseInt(razorpayOrder.receipt),
            });
          Logger.log("success", {
            message:
              "razorpayWebhookController:ordersWebhook:transaction updated",
            params: {
              subscriptionAllotmentTransaction,
              newSubscription,
              paymentPurpose: constants.PAYMENT_PURPOSE.SUBSCRIPTION_PURCHASE,
            },
          });
          break;
        }
      }

      case constants.PAYMENT_PURPOSE.PLAN_PURCHASE: {
        const canAssignPlanTypeToUser =
          await OfferService.canAssignPlanTypeToUser({
            user: paymentOrder.tbl_users,
          });
        if (!canAssignPlanTypeToUser) {
          Logger.log("error", {
            message: "razorpayWebhookController:ordersWebhook:catch-2",
            params: { error: constants.ERROR_CODES.MAX_PLAN_LIMIT },
          });

          break;
        } else {
          const planAllotmentTransaction = await prisma.$transaction(
            async (tx) => {
              const updatedPaymentOrder = await tx.tbl_payment_orders.update({
                where: {
                  payment_order_id: razorpayOrder.id,
                },
                data: {
                  payment_order_status: razorpayOrder.status,
                },
                include: {
                  tbl_payment_transactions: {
                    orderBy: {
                      created_at: "desc",
                    },
                  },
                },
              });

              if (updatedPaymentOrder.inter_wallet_transaction_id) {
                await tx.tbl_inter_wallet_transactions.update({
                  where: {
                    inter_wallet_transaction_id: parseInt(
                      razorpayOrder.notes.inter_wallet_transaction_id
                    ),
                  },
                  data: {
                    inter_wallet_transaction_status:
                      constants.INTER_WALLET_TRANSACTION_STATUS.SUCCESS,
                  },
                });
              }

              return { updatedPaymentOrder };
            }
          );

          const newPlan = await OfferService.assignPlanTypeToUser({
            user: paymentOrder.tbl_users,
            planPurchaseReceiptID: parseInt(razorpayOrder.receipt),
          });
          Logger.log("success", {
            message:
              "razorpayWebhookController:ordersWebhook:transaction updated",
            params: {
              planAllotmentTransaction,
              newPlan,
              paymentPurpose: constants.PAYMENT_PURPOSE.PLAN_PURCHASE,
            },
          });

          break;
        }
      }

      default: {
        Logger.log("error", {
          message: "razorpayWebhookController:ordersWebhook:default:catch-2",
          params: { error: constants.ERROR_CODES.INVALID_WEBHOOK_PAYLOAD },
        });
        break;
      }
    }
    return res.status(200).json({ status: "ok" });
  } catch (error) {
    Logger.log("error", {
      message: "razorpayWebhookController:ordersWebhook:catch-1",
      params: { error },
    });
    return res.status(200).json({ status: "ok" });
  }
};

/**
 *
 * @param {import("express").Request} req
 * @param {import("express").Response} res
 * @returns
 */
razorpayWebhookController.paymentsWebhook = async (req, res) => {
  try {
    const { paymentObject, paymentOrder } = req;

    const paymentTransaction = await prisma.tbl_payment_transactions.findUnique(
      {
        where: { payment_transaction_id: paymentObject.id },
      }
    );
    if (!paymentTransaction) {
      const newPaymentTransaction =
        await prisma.tbl_payment_transactions.create({
          data: {
            payment_transaction_id: paymentObject.id,
            payment_order_id: paymentObject.order_id,
            amount: paymentObject.amount,
            payment_transaction_status: paymentObject.status,
            payment_transaction_method: paymentObject.method,
            payment_transaction_captured: paymentObject.captured,
            amount_refunded: paymentObject.amount_refunded,
            refund_status: paymentObject.refund_status,
            payment_transaction_fee: paymentObject.fee,
            payment_transaction_tax: paymentObject.tax,
            payment_transaction_error_code: paymentObject.error_code,
            payment_transaction_error_description:
              paymentObject.error_description,
            payment_transaction_card_id: paymentObject.card_id,
            payment_transaction_card: JSON.stringify(paymentObject.card),
            payment_transaction_bank: paymentObject.bank,
            payment_transaction_vpa: paymentObject.vpa,
            payment_transaction_email: paymentObject.email,
            payment_transaction_wallet: paymentObject.wallet,
            created_at: new Date(),
          },
        });
      Logger.log("success", {
        message:
          "razorpayWebhookController:paymentsWebhook:payment transaction created",
        params: { newPaymentTransaction },
      });
    } else {
      const updatedPaymentTransaction =
        await prisma.tbl_payment_transactions.update({
          where: { payment_transaction_id: paymentObject.id },
          data: {
            amount: paymentObject.amount,
            payment_transaction_status: paymentObject.status,
            payment_transaction_method: paymentObject.method,
            payment_transaction_captured: paymentObject.captured,
            amount_refunded: paymentObject.amount_refunded,
            refund_status: paymentObject.refund_status,
            payment_transaction_fee: paymentObject.fee,
            payment_transaction_tax: paymentObject.tax,
            payment_transaction_error_code: paymentObject.error_code,
            payment_transaction_error_description:
              paymentObject.error_description,
            payment_transaction_card_id: paymentObject.card_id,
            payment_transaction_card: JSON.stringify(paymentObject.card),
            payment_transaction_bank: paymentObject.bank,
            payment_transaction_vpa: paymentObject.vpa,
            payment_transaction_email: paymentObject.email,
            payment_transaction_wallet: paymentObject.wallet,
          },
        });
      Logger.log("success", {
        message:
          "razorpayWebhookController:paymentsWebhook:payment transaction updated",
        params: { updatedPaymentTransaction },
      });
    }

    return res.status(200).json({ status: "ok" });
  } catch (error) {
    Logger.log("error", {
      message: "razorpayWebhookController:paymentsWebhook:catch-1",
      params: { error },
    });
    return res.status(200).json({ status: "ok" });
  }
};

module.exports = razorpayWebhookController;
