const express = require("express");
const router = express.Router();

const authMiddleware = require("../middlewares/auth.middleware");
const checkMiddleware = require("../middlewares/check.middleware");
const offerController = require("../controllers/offer.controller");

router.get(
  "/location_based_offer_map",
  authMiddleware,
  offerController.getLocationOffersMap
);
router.get(
  "/reservationTypes",
  authMiddleware,
  offerController.getReservationTypes
);
router.get(
  "/subscriptionTypes",
  authMiddleware,
  offerController.getSubscriptionTypes
);
router.get("/planTypes", authMiddleware, offerController.getPlanTypes);

router.get(
  "/subscriptions",
  authMiddleware,
  offerController.getUserSubscriptions
);
router.get("/plans", authMiddleware, offerController.getUserPlans);
router.get("/coupons", authMiddleware, offerController.getUserPlans);
router.get(
  "/subscriptions/:id",
  authMiddleware,
  offerController.getUserSubscriptionByID
);
router.get("/plans/:id", authMiddleware, offerController.getUserPlanByID);

router.post(
  "/purchase_subscription_type",
  authMiddleware,
  checkMiddleware.isWalletActivated,
  checkMiddleware.isSufficientWalletBalance,
  checkMiddleware.isSecurityDepositDone,
  checkMiddleware.isSubscriptionTypeValid,
  checkMiddleware.checkMaxSubscriptionsAllowed,
  checkMiddleware.isSufficientWalletBalanceForSubscriptionTypePurchase,
  offerController.purchaseSubscriptionType
);

router.post(
  "/purchase_plan_type",
  authMiddleware,
  checkMiddleware.isWalletActivated,
  checkMiddleware.isSecurityDepositDone,
  checkMiddleware.isPlanTypeValid,
  checkMiddleware.checkMaxPlansAllowed,
  checkMiddleware.isSufficientWalletBalanceForPlanTypePurchase,
  offerController.purchasePlanType
);
module.exports = router;
