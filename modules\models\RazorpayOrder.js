class RazorpayOrder {
  constructor({
    id,
    amount,
    partial_payment,
    amount_paid,
    amount_due,
    currency,
    receipt,
    status,
    attempts,
    notes,
    created_at,
  }) {
    this.id = id;
    this.amount = amount;
    this.partial_payment = partial_payment;
    this.amount_paid = amount_paid;
    this.amount_due = amount_due;
    this.currency = currency;
    this.receipt = receipt;
    this.status = status;
    this.attempts = attempts;
    this.notes = notes;
    this.created_at = created_at;
  }
}

module.exports = { RazorpayOrder };
