const constants = {
  IOT_SERVER: "http://127.0.0.1:8091",
  BACKEND_NODE_ID:
    process.env.NODE_ENV === "development"
      ? "hover_mobility_dev_node_1"
      : "hover_mobility_prod_node_1",

  // thresholds
  WALLET_BALANCE_THRESHOLD: 0,
  OUTSTATION_THRESHOLD: 0.05,
  IOT_COMMAND_TIMEOUT: 8,
  MINIMUM_BASE_BOOKING_PRICE: 1000,

  // defaults
  MINIMUM_VEHICLE_VOLTAGES: {
    OKINAWA: 42,
  },
  VEHICLE_VOLTAGE_RANGE: {
    OKINAWA: 12,
  },
  VEHICLE_VOLTAGE_DISPLAY_BUFFER_PERCENT: {
    OKINAWA: 10,
  },
  VEHICLE_RANGE: {
    OKINAWA: 50,
  },
  GENERIC_PAGE_SIZE: 10,
  ROW_PAGE_SIZE: 10,
  BOOKING_HISTORY_PAGE_SIZE: 10,
  RESERVATION_HISTORY_PAGE_SIZE: 10,
  OFFERS_PAGE_SIZE: 100,
  ORDERS_PAGE_SIZE: 20,

  DEFAULT_RIDE_TIME_CHARGE: 179,
  DEFAULT_PAUSE_TIME_CHARGE: 120,
  DEFAULT_UNLOCK_CHARGE: 0,
  DEFAULT_TAX_PERCENTAGE: 5,
  DEFAULT_OUTSTATION_CHARGE: 2000,
  DEFAULT_OUTSTATION_PER_KM_CHARGE: 500,
  DEFAULT_VEHICLE_MAINTENANCE_CHARGE_PERCENTAGE: 5,
  DEFAULT_PAYMENT_GATEWAY_CHARGE_PERCENTAGE: 2.36,

  DEFAULT_MIN_UNLOCK_FARE: 0,
  DEFAULT_MIN_PAUSE_TIME_FARE: 0,
  DEFAULT_MIN_RIDE_TIME_FARE: 0,

  DEFAULT_MIN_RESERVATION_TIME_FARE: 0,

  DEFAULT_MAX_DELIVERY_BOOKINGS: 1,

  DEFAULT_AD_USER_MAP_ID: 1,

  DEFAULT_MAX_IOT_COMMAND_RETRY: 3,

  DUMMY_PERMISSION: {
    tables: {
      tbl_stations: true,
      tbl_machines: true,
      tbl_users: true,
      tbl_washes: true,
    },
  },

  PAYMENT_ORDER_FAIL_AFTER_TIME: 86400000,
  // PAYMENT_ORDER_FAIL_AFTER_TIME: 8640,
  WASH_STATE_CHANGE_THRESHOLD: 50000,

  DEFAULT_LOCATION_OFFER_MAP_ID: 1,
  DEFAULT_USER_TYPE_ID: 1,

  STATION_RENDER_RADIUS: 0.03,
  VEHICLE_RENDER_RADIUS: 0.02,

  // IDs
  OUTSTATION_ID: 0,
  HOVER_WALLET_ID: 0,
  DEFAULT_OFFER_TYPES: [1, 2, 3],
  // error codes
  ADMIN_ERROR_CODES: {
    PERMISSION_DENIED: {
      code: "PERMISSION_DENIED",
      message: "Permission denied",
    },
    SERVER_ERROR: {
      code: "SERVER_ERROR",
      message: "Server error",
    },
  },
  ERROR_CODES: {
    WALLET_DISABLED_BY_ADMIN: {
      code: "WALLET_DISABLED_BY_ADMIN",
      message: "You wallet is disabled. Please contact to support",
    },
    KYC_FAILED: {
      code: "KYC_FAILED",
      message: "KYC failed",
    },
    CANNOT_CANCEL_RIDE: {
      code: "CANNOT_CANCEL_RIDE",
      message:
        "Ride can be cancelled within 1 min. Please end the ride from application",
    },
    VEHICLE_DID_NOT_RESPOND: {
      code: "VEHICLE_DID_NOT_RESPOND",
      message: "Vehicle did not respond. Please try again",
    },
    SECURITY_DEPOSIT_ALREADY_PAID: {
      code: "SECURITY_DEPOSIT_ALREADY_PAID",
      message: "Security deposit already paid",
    },
    SECURITY_DEPOSIT_NOT_PAID: {
      code: "SECURITY_DEPOSIT_NOT_PAID",
      message: "Security deposit not paid",
    },
    FETCH_VEHICLE_STATUS_ERROR: {
      code: "FETCH_VEHICLE_STATUS_ERROR",
      message: "Machine status fetch error",
    },
    INVALID_WEBHOOK_PAYLOAD: {
      code: "INVALID_WEBHOOK_PAYLOAD",
      message: "Webhook payload is not valid",
    },
    OFFER_INVALID: {
      code: "OFFER_INVALID",
      message: "Offer invalid",
    },
    SUBSCRIPTION_TYPE_INVALID: {
      code: "SUBSCRIPTION_TYPE_INVALID",
      message: "Subscription type invalid",
    },
    MAX_SUBSCRIPTION_LIMIT: {
      code: "MAX_SUBSCRIPTION_LIMIT",
      message: "Cannot purchase more subscriptions",
    },
    PLAN_TYPE_INVALID: {
      code: "PLAN_TYPE_INVALID",
      message: "Subscription type invalid",
    },
    MAX_PLAN_LIMIT: {
      code: "MAX_PLAN_LIMIT",
      message: "Cannot purchase more plans",
    },
    ACCESS_DENIED: {
      code: "ACCESS_DENIED",
      message: "Access denied",
    },
    OPEN_BOOKINGS: {
      code: "OPEN_BOOKINGS",
      message:
        "There are open bookings. Please end the previous bookings first",
    },
    OPEN_RESERVATION: {
      code: "OPEN_RESERVATION",
      message:
        "There are open reservations. Please end the previous reservations first",
    },
    NO_OPEN_BOOKINGS: {
      code: "NO_OPEN_BOOKINGS",
      message: "There are no open bookings.",
    },
    BOOKING_NOT_RIDING: {
      code: "BOOKING_NOT_RIDING",
      message: "Not riding scooter",
    },
    BOOKING_NOT_PAUSED: {
      code: "BOOKING_NOT_PAUSED",
      message: "Vehicle not locked",
    },
    END_TIME_THRESHOLD: {
      code: "WASH_TIME_THRESHOLD",
      message: "Please wait for 1 mins before ending wash",
    },
    INSUFFICIENT_BALANCE: {
      code: "INSUFFICIENT_BALANCE",
      message: "Insufficient balance in wallet",
    },
    INVALID_PAYMENT_ORDER_ID: {
      code: "INVALID_PAYMENT_ORDER_ID",
      message: "Payment transaction ID does not exist",
    },
    INVALID_RECEIPT_ID: {
      code: "INVALID_RECEIPT_ID",
      message: "Receipt ID does not exist",
    },
    INVALID_STATION_ID: {
      code: "INVALID_STATION_ID",
      message: "Station ID does not exist",
    },
    INVALID_VEHICLE_ID: {
      code: "INVALID_VEHICLE_ID",
      message: "Invalid vehicle code",
    },
    INVALID_REQUEST: {
      code: "INVALID_REQUEST",
      message: "Invalid request",
    },
    INVALID_BOOKING_ID: {
      code: "INVALID_BOOKING_ID",
      message: "Wash ID does not exist",
    },
    INVALID_BOOKING_ID: {
      code: "INVALID_BOOKING_ID",
      message: "Booking ID does not exist",
    },
    INVALID_RESERVATION_ID: {
      code: "INVALID_RESERVATION_ID",
      message: "Reservation ID does not exist",
    },
    INVALID_TRANSACTION_ID: {
      code: "INVALID_TRANSACTION_ID",
      message: "Transaction ID does not exist",
    },
    INVALID_USER: {
      code: "INVALID_USER",
      message: "User not found",
    },
    NOT_WASHING: {
      code: "NOT_WASHING",
      message: "Not riding",
    },
    NO_RESERVATION: {
      code: "NO_RESERVATION",
      message: "No reservation found",
    },
    NO_VALID_BOOKING: {
      code: "NO_VALID_BOOKING",
      message: "No booking found",
    },
    PAUSE_TIME_THRESHOLD: {
      code: "PAUSE_TIME_THRESHOLD",
      message: "Please wait for 1 mins before pausing wash",
    },
    PAYMENT_ALREADY_DONE: {
      code: "PAYMENT_ALREADY_DONE",
      message: "Payment already done",
    },
    PAYMENT_FAILED: {
      code: "PAYMENT_FAILED",
      message: "Payment failed",
    },
    PENDING_PAYMENT: {
      code: "PENDING_PAYMENT",
      message: "Payment is pending",
    },
    RECEIPT_ALREADY_GENERATED: {
      code: "RECEIPT_ALREADY_GENERATED",
      message: "Wash receipt already generated",
    },
    RECEIPT_GENERATION_ERROR: {
      code: "RECEIPT_GENERATION_ERROR",
      message: "Error occured while generating your receipt",
    },
    REMOTE_CONTROL_REQUEST_NOT_FOUND: {
      code: "REMOTE_CONTROL_REQUEST_NOT_FOUND",
      message: "Request not found",
    },
    REMOTE_CONTROL_REQUEST_NOT_SUCCESS: {
      code: "REMOTE_CONTROL_REQUEST_NOT_SUCESS",
      message: "Request not success",
    },
    REMOTE_CONTROL_REQUEST_TIMEDOUT: {
      code: "REMOTE_CONTROL_REQUEST_TIMEDOUT",
      message: "Request timed out",
    },
    REMOTE_CONTROL_REQUEST_TIMEDOUT_ALREADY: {
      code: "REMOTE_CONTROL_REQUEST_TIMEDOUT_ALREADY",
      message: "Request timed out already",
    },
    RESUME_TIME_THRESHOLD: {
      code: "RESUME_TIME_THRESHOLD",
      message: "Please wait for 1 mins before resuming wash",
    },
    WASH_ALREADY_CANCELLED: {
      code: "WASH_ALREADY_CANCELLED",
      message: "Wash already cancelled",
    },
    WASH_BOOKING_ERROR: {
      code: "WASH_BOOKING_ERROR",
      message: "Request not success",
    },
    WASH_ENDING_ERROR: {
      code: "WASH_ENDING_ERROR",
      message: "Request not success",
    },
    WASH_IS_ALREADY_PAUSED: {
      code: "WASH_IS_ALREADY_PAUSED",
      message: "Wash is already paused.",
    },
    WASH_IS_ALREADY_RESUMED: {
      code: "WASH_IS_ALREADY_RESUMED",
      message: "Wash is already resumed.",
    },
    WASH_NOT_PAUSED: {
      code: "WASH_NOT_PAUSED",
      message: "Wash not paused",
    },
    WASH_NOT_STUCK: {
      code: "WASH_NOT_STUCK",
      message: "Wash can be terminated only if stuck. Please end the wash",
    },
    WASH_NOT_STUCK_TIME: {
      code: "WASH_NOT_STUCK_TIME",
      message: "We recommend waiting for some time to connect the bike",
    },
    WASH_PAUSING_ERROR: {
      code: "WASH_PAUSING_ERROR",
      message: "Request not success",
    },
    WASH_RESUMING_ERROR: {
      code: "WASH_RESUMING_ERROR",
      message: "Request not success",
    },
    WASH_STATE_STILL_LOADING: {
      code: "WASH_STATE_STILL_LOADING",
      message: "Previous request still processing",
    },
    WASH_TERMINATED: {
      code: "WASH_TERMINATED",
      message: "Wash already terminated",
    },
    SERVER_ERROR: {
      code: "SERVER_ERROR",
      message: "Server error",
    },
    STATION_ID_NOT_FOUND: {
      code: "STATION_ID_NOT_FOUND",
      message: "Station ID found",
    },
    USER_AUTH_TOKEN_EXPIRED: {
      code: "USER_AUTH_TOKEN_EXPIRED",
      message: "User auth token expired",
    },
    USER_AUTH_TOKEN_NOT_FOUND: {
      code: "USER_AUTH_TOKEN_NOT_FOUND",
      message: "User auth token not found",
    },
    USER_PROFILE_NOT_COMPLETE: {
      code: "USER_PROFILE_NOT_COMPLETE",
      message: "User profile not complete",
    },
    USER_LOCATION_NOT_PROVIDED: {
      code: "USER_LOCATION_NOT_PROVIDED",
      message: "There was a problem getting your location. Please enable GPS",
    },
    ALREADY_REMINDER_SET: {
      code: "ALREADY_REMINDER_SET",
      message:
        "Already a reminder for other machine is set. Please remove the reminder to set a new reminder",
    },
    VEHICLE_BATTERY_LOW: {
      code: "VEHICLE_BATTERY_LOW",
      message: "Vehicle battery is very low",
    },
    VEHICLE_ID_NOT_FOUND: {
      code: "VEHICLE_ID_NOT_FOUND",
      message: "Vehicle ID found",
    },
    VEHICLE_NOT_AVAILABLE: {
      code: "VEHICLE_NOT_AVAILABLE",
      message: "Vehicle is not available",
    },
    VEHICLE_NOT_BUSY: {
      code: "VEHICLE_NOT_BUSY",
      message: "Machine is not busy. You can book machine",
    },
    VEHICLE_NOT_PARKED_NEAR_STATION: {
      code: "VEHICLE_NOT_PARKED_NEAR_STATION",
      message: "Vehicle not parked near station",
    },
    WALLET_ALREADY_ACTIVATED: {
      code: "WALLET_ALREADY_ACTIVATED",
      message: "User wallet is already activated",
    },
    WALLET_DISABLED: {
      code: "WALLET_DISABLED",
      message: "User wallet is disabled",
    },
    WALLET_NOT_ACTIVATED: {
      code: "WALLET_NOT_ACTIVATED",
      message: "User wallet is not activated",
    },
  },

  SERVER_SIDE_ERROR_TYPES: {
    WASH_BOOKING_ERROR: "WASH_BOOKING_ERROR",
    WASH_PAUSING_ERROR: "WASH_PAUSING_ERROR",
    WASH_RESUMING_ERROR: "WASH_RESUMING_ERROR",
    WASH_ENDING_ERROR: "WASH_ENDING_ERROR",
  },

  OFFER_TYPES: {
    SUBSCRIPTION: "SUBSCRIPTION",
    INDIVIDUAL_PLAN: "INDIVIDUAL_PLAN",
  },

  COUPON_CATEGORIES: {
    FREE_BOOKING_MINUTES: "FREE_BOOKING_MINUTES",
    FREE_RIDE_MINUTES: "FREE_RIDE_MINUTES",
    RIDE_TIME_FARE_PERCENTAGE_REDUCTION: "RIDE_TIME_FARE_PERCENTAGE_REDUCTION",
    RIDE_TIME_FARE_DIRECT_REDUCTION: "RIDE_TIME_FARE_DIRECT_REDUCTION",
    RIDE_TIME_CHARGE_REPLACEMENT: "RIDE_TIME_CHARGE_REPLACEMENT",

    FREE_PAUSE_MINUTES: "FREE_PAUSE_MINUTES",
    PAUSE_TIME_FARE_PERCENTAGE_REDUCTION:
      "PAUSE_TIME_FARE_PERCENTAGE_REDUCTION",
    PAUSE_TIME_FARE_DIRECT_REDUCTION: "PAUSE_TIME_FARE_DIRECT_REDUCTION",
    PAUSE_TIME_CHARGE_REPLACEMENT: "PAUSE_TIME_CHARGE_REPLACEMENT",

    FREE_RESERVATION_MINUTES: "FREE_RESERVATION_MINUTES",
    RESERVATION_TIME_FARE_PERCENTAGE_REDUCTION:
      "RESERVATION_TIME_FARE_PERCENTAGE_REDUCTION",
    RESERVATION_TIME_FARE_DIRECT_REDUCTION:
      "RESERVATION_TIME_FARE_DIRECT_REDUCTION",
    RESERVATION_TIME_CHARGE_REPLACEMENT: "RESERVATION_TIME_CHARGE_REPLACEMENT",

    FREE_UNLOCKS: "FREE_UNLOCKS",
    UNLOCK_FARE_PERCENTAGE_REDUCTION: "UNLOCK_FARE_PERCENTAGE_REDUCTION",
    UNLOCK_FARE_DIRECT_REDUCTION: "UNLOCK_FARE_DIRECT_REDUCTION",
    UNLOCK_CHARGE_REPLACEMENT: "UNLOCK_CHARGE_REPLACEMENT",
  },

  PAYMENT_SOURCE: {
    WALLET: "WALLET",
    WALLET_RAZORPAY: "WALLET_RAZORPAY",
    RAZORPAY: "RAZORPAY",
    SECURITY_DEPOSIT_BONUS: "SECURITY_DEPOSIT_BONUS",
    SIGNUP_BONUS: "SIGNUP_BONUS",
  },

  PAYMENT_PURPOSE: {
    PLAN_PURCHASE: "PLAN_PURCHASE",
    SECURITY_DEPOSIT: "SECURITY_DEPOSIT",
    WALLET_RECHARGE: "WALLET_RECHARGE",
    SUBSCRIPTION_PURCHASE: "SUBSCRIPTION_PURCHASE",
    BOOKING_PAYMENT: "BOOKING_PAYMENT",
    RESERVATION_PAYMENT: "RESERVATION_PAYMENT",
  },

  PAYMENT_PURPOSE_DETAILS: {
    PLAN_PURCHASE: { name: "PLAN_PURCHASE", title: "Plan purchase" },
    SECURITY_DEPOSIT: {
      name: "SECURITY_DEPOSIT",
      title: "Security deposit payment",
    },
    WALLET_RECHARGE: {
      name: "WALLET_RECHARGE",
      title: "Credit purchase",
    },
    SUBSCRIPTION_PURCHASE: {
      name: "SUBSCRIPTION_PURCHASE",
      title: "Subscription purchase",
    },
    BOOKING_PAYMENT: { name: "BOOKING_PAYMENT", title: "Booking payment" },
    RESERVATION_PAYMENT: {
      name: "RESERVATION_PAYMENT",
      title: "Reservation payment",
    },
  },

  COUPON_APPLICABILITY_ORDER: {
    START: "START",
    END: "END",
  },

  COUPON_RULE_ACTIONS: {
    ADD: "ADD",
    SUBSTRACT: "SUBSTRACT",
    DIVIDE: "DIVIDE",
    MULTIPLY: "MULTIPLY",
    REPLACE: "REPLACE",
  },

  COUPON_RULE_ACTION_LIMITING_VALUE_STRATEGY: {
    REMINDER: "REMINDER",
    ACTUAL_VALUE: "ACTUAL_VALUE",
  },

  COUPON_RULE_ACTION_LIMITING_VALUE_ACTIONS: {
    ADD: "ADD",
    SUBSTRACT: "SUBSTRACT",
    DIVIDE: "DIVIDE",
    MULTIPLY: "MULTIPLY",
    REPLACE: "REPLACE",
  },

  BOOKING_ACTIONS: {
    START: "START",
    PAUSE: "PAUSE",
    RESUME: "RESUME",
    END: "END",
    INITIATE_START: "INITIATE_START",
    INITIATE_PAUSE: "INITIATE_PAUSE",
    INITIATE_RESUME: "INITIATE_RESUME",
    INITIATE_END: "INITIATE_END",
    TERMINATED: "TERMINATED",
    CANCELLED: "CANCELLED",
  },

  // push notifications
  PUSH_NOTIFICATIONS: {
    ICON_URLS: {
      DEFAULT:
        "https://firebasestorage.googleapis.com/v0/b/grin--mobility.appspot.com/o/logo512.png?alt=media&token=da6da851-0886-4708-a55c-92e4c7da90f0",
      WASH: "https://firebasestorage.googleapis.com/v0/b/grin--mobility.appspot.com/o/logo512.png?alt=media&token=da6da851-0886-4708-a55c-92e4c7da90f0",
    },
    BOOKING_CONFIRMED: (name, washID) => {
      return {
        title: "Wash confirmation",
        description: `${name}, your wash id : ${washID} is confirmed`,
      };
    },
    BOOKING_PAUSED: (name, washID) => {
      return {
        title: "Wash paused",
        description: `${name}, your wash id : ${washID} is paused`,
      };
    },
    BOOKING_RESUMED: (name, washID) => {
      return {
        title: "Wash resumed",
        description: `${name}, your wash id : ${washID} is resumed`,
      };
    },
    BOOKING_ENDED: (name, washID) => {
      return {
        title: "Wash finished",
        description: `${name}, your wash id : ${washID} is finished`,
      };
    },
  },

  // status

  RAZORPAY_ORDER_STATUS: {
    CREATED: "created",
    ATTEMPTED: "attempted",
    PAID: "paid",
    FAILED: "failed",
    TO_REFUND: "to_refund",
  },

  RAZORPAY_WEBHOOK_ORDER_STATUS: {
    PAID: "order.paid",
  },

  RAZORPAY_PAYMENT_WEBHOOK_STATUS_CODE: {
    AUTHORIZED: "payment.authorized",
    CAPTURED: "payment.captured",
    FAILED: "payment.failed",
  },

  RAZORPAY_PAYMENT_STATUS_CODE: {
    CREATED: "created",
    AUTHORIZED: "authorized",
    CAPTURED: "captured",
    FAILED: "failed",
    REFUNDED: "refunded",
    TO_REFUND: "to_refunded",
  },

  RAZORPAY_PAYMENT_METHOD_CODE: {
    NETBANKING: "netbanking",
    CARD: "card",
    WALLET: "wallet",
    UPI: "upi",
  },

  BOOKING_STATUS: {
    RIDING: "RIDING",
    PAUSED: "PAUSED",
    ENDED: "ENDED",
    INITIATED_RIDE: "INITIATED_RIDE",
    INITIATED_PAUSE: "INITIATED_PAUSE",
    INITIATED_RESUME: "INITIATED_RESUME",
    INITIATED_END: "INITIATED_END",
    TERMINATED: "TERMINATED",
    CANCELLED: "CANCELLED",
  },

  VEHICLE_STATUS: {
    READY: "READY",
    RIDING: "RIDING",
    PAUSED: "PAUSED",
    MAINTAINANCE: "MAINTAINANCE",
    INITIATED_UNLOCK: "INITIATED_UNLOCK",
    INITIATED_LOCK_TEMP: "INITIATED_LOCK_TEMP",
    INITIATED_UNLOCK_TEMP: "INITIATED_UNLOCK_TEMP",
    INITIATED_LOCK: "INITIATED_LOCK",
    RESERVED: "RESERVED",
    ASSIGNED_ON_DELIVERY: "ASSIGNED_ON_DELIVERY",
    ON_DELIVERY: "ON_DELIVERY",
  },

  RESERVATION_STATUS: {
    RESERVED: "RESERVED",
    ENDED: "ENDED",
    TERMINATED: "TERMINATED",
  },

  DELIVERY_BOOKING_STATUS: {
    REQUESTED: "REQUESTED",
    VEHICLE_ASSIGNED: "VEHICLE_ASSIGNED",
    ON_DELIVERY: "ON_DELIVERY",
    RIDING: "RIDING",
    ENDED: "ENDED",
    CANCELLED: "CANCELLED",
    TERMINATED: "TERMINATED",
  },

  DELIVERY_BOOKING_ACTIONS: {
    REQUESTED: "REQUESTED",
    VEHICLE_ASSIGNED: "VEHICLE_ASSIGNED",
    VEHICLE_DELIVERED: "VEHICLE_DELIVERED",
    ENDED: "ENDED",
    CANCELLED: "CANCELLED",
    TERMINATED: "TERMINATED",
  },

  SECURITY_DEPOSIT_STATUS: {
    DEFAULT: "DEFAULT",
    PENDING: "PENDING",
    SUCCESS: "SUCCESS",
    FAILED: "FAILED",
    CANCELLED: "CANCELLED",
    REFUND: "REFUND",
    EXTRA: "EXTRA",
    NO_CHANGE: "NO_CHANGE",
  },

  WALLET_RECHARGE_TRANSACTION_STATUS: {
    DEFAULT: "DEFAULT",
    PENDING: "PENDING",
    SUCCESS: "SUCCESS",
    FAILED: "FAILED",
    CANCELLED: "CANCELLED",
    REFUND: "REFUND",
    EXTRA: "EXTRA",
    NO_CHANGE: "NO_CHANGE",
  },

  SUBSCRIPTION_PURCHASE_RECEIPT_STATUS: {
    DEFAULT: "DEFAULT",
    PENDING: "PENDING",
    SUCCESS: "SUCCESS",
    FAILED: "FAILED",
    CANCELLED: "CANCELLED",
    REFUND: "REFUND",
    EXTRA: "EXTRA",
    NO_CHANGE: "NO_CHANGE",
  },

  PLAN_PURCHASE_RECEIPT_STATUS: {
    DEFAULT: "DEFAULT",
    PENDING: "PENDING",
    SUCCESS: "SUCCESS",
    FAILED: "FAILED",
    CANCELLED: "CANCELLED",
    REFUND: "REFUND",
    EXTRA: "EXTRA",
    NO_CHANGE: "NO_CHANGE",
  },

  COUPON_PURCHASE_RECEIPT_STATUS: {
    DEFAULT: "DEFAULT",
    PENDING: "PENDING",
    SUCCESS: "SUCCESS",
    FAILED: "FAILED",
    CANCELLED: "CANCELLED",
    REFUND: "REFUND",
    EXTRA: "EXTRA",
    NO_CHANGE: "NO_CHANGE",
  },

  TRANSACTION_STATUS: {
    DEFAULT: "DEFAULT",
    PENDING: "PENDING",
    SUCCESS: "SUCCESS",
    FAILED: "FAILED",
    CANCELLED: "CANCELLED",
    REFUND: "REFUND",
    EXTRA: "EXTRA",
    NO_CHANGE: "NO_CHANGE",
  },

  INTER_WALLET_TRANSACTION_STATUS: {
    DEFAULT: "DEFAULT",
    PENDING: "PENDING",
    SUCCESS: "SUCCESS",
    FAILED: "FAILED",
    CANCELLED: "CANCELLED",
    REFUND: "REFUND",
    EXTRA: "EXTRA",
    NO_CHANGE: "NO_CHANGE",
  },

  // types
  PAYMENT_TYPE: {
    WASH_PAYMENT: "WASH_PAYMENT",
    SUBSCRIPTION_PAYMENT: "SUBSCRIPTION_PAYMENT",
  },

  // events
  SOCKET_EVENTS: {
    ON_VEHICLES_UPDATE: "ON_VEHICLES_UPDATE",
    ON_STATION_UPDATE: "ON_STATION_UPDATE",
    ON_OFFERS_UPDATE: "ON_OFFERS_UPDATE",
    ON_ONGOING_BOOKING_UPDATE: "ON_ONGOING_BOOKING_UPDATE",
    ON_ONGOING_RESERVATION_UPDATE: "ON_ONGOING_RESERVATION_UPDATE",
    ON_USER_UPDATE: "ON_USER_UPDATE",
    ON_WALLET_UPDATE: "ON_WALLET_UPDATE",
    ON_CURRENT_PAYMENT_ORDER_UPDATE: "ON_CURRENT_PAYMENT_ORDER_UPDATE",
    ON_PAYMENT_TRANSACTION_UPDATE: "ON_PAYMENT_TRANSACTION_UPDATE",
    ON_SERVER_SIDE_ERROR: "ON_SERVER_SIDE_ERROR",
  },

  SOCKET_EMIT_EVENTS: {
    USER_LOCATION_DATA: "USER_LOCATION_DATA",
  },

  IOT_COMMANDS: {
    VEHICLE_START: {
      command: "setdigout 1?",
      response: ["DOUT1:1", "DOUT1:Already set to 1"],
    },
    VEHICLE_STOP: {
      command: "setdigout 0?",
      response: ["DOUT1:0", "DOUT1:Already set to 0"],
    },
  },

  USER_TYPE_IDS: {
    CUSTOMER: "CUSTOMER",
    MAINTAINER: "MAINTAINER",
    ADMIN: "ADMIN",
  },
};

module.exports = constants;
