const e = require("cors");
const {
  AVL_Data,
} = require("../modules/teltonikaCodec/AVL Data Parser/AVL_Data");
const { Data } = require("../modules/teltonikaCodec/AVL Data Parser/Data");
const { GPRS } = require("../modules/teltonikaCodec/GPRS Parser/GPRSparser");
const {
  ProtocolParser,
  parseIMEI,
} = require("../modules/teltonikaCodec/ProtocolParser");
const Logger = require("./logger");

const getParsedPacket = (response) => {
  var imei = undefined;
  const packet = response.toString("hex");
  var processedPacket = processPacket(packet);
  return { content: processedPacket.dataPacket, imei: processedPacket.imei };
};

const processPacket = (packet) => {
  if (packet.length == 34) {
    return {
      imei: parseIMEI(packet),
    };
  } else {
    return {
      dataPacket: new ProtocolParser(packet),
    };
  }
};

/**
 *
 * @param {object} param0
 * @param {Data} param0.content
 * @param {String} param0.imei
 */
const extractDataFromDeviceDataContent = ({ content, imei }) => {
  try {
    Logger.log("info", {
      message: "iot_util:extractDataFromDeviceDataContent:content",
      params: { imei },
    });
    const data = [];
    content.AVL_Datas.sort((a, b) => {
      return b.Timestamp.getTime() - a.Timestamp.getTime();
    }).forEach((element) => {
      data.push({
        timestamp: element.Timestamp,
        latitude: element.GPSelement.Latitude,
        longitude: element.GPSelement.Longitude,
        internalBatteryVoltage: element.IOelement?.Elements?.["67"],
        internalBatteryCurrent: element.IOelement?.Elements?.["68"],
        internalBatteryPercent: element.IOelement?.Elements?.["113"],
        speed: element.IOelement?.Elements?.["24"],
        gsmSignal: element.IOelement?.Elements?.["21"],
        ICCID1: element.IOelement?.Elements?.["11"],
        ICCID2: element.IOelement?.Elements?.["14"],
        movement: element.IOelement?.Elements?.["240"],
        externalVoltage: element.IOelement?.Elements?.["66"],
        externalExtendedVoltage: element.IOelement?.Elements?.["800"],
        analogInput2: element.IOelement?.Elements?.["6"],
        tripOdometer: element.IOelement?.Elements?.["199"],
        totalOdometer: element.IOelement?.Elements?.["16"],
        xAxis: element.IOelement?.Elements?.["17"],
        yAxis: element.IOelement?.Elements?.["18"],
        zAxis: element.IOelement?.Elements?.["19"],
      });
    });
    Logger.log("info", {
      message: "iot_util:extractDataFromDeviceDataContent:data",
      params: {
        imei,
        data: {
          timestamp: data.timestamp,
          latitude: data.latitude,
          longitude: data.longitude,
          internalBatteryPercent: data.internalBatteryPercent,
          externalVoltage: data.externalVoltage,
        },
      },
    });
    return data.length > 0 ? data[0] : null;
  } catch (error) {
    Logger.log("error", {
      message: "iot_util:extractDataFromDeviceDataContent:catch-1",
      params: { imei, error },
    });
    return null;
  }
};
/**
 *
 * @param {object} param0
 * @param {Data} param0.content
 * @param {String} param0.imei
 */
const extractLoggingDataFromDeviceDataContent = ({ content, imei }) => {
  try {
    Logger.log("info", {
      message: "iot_util:extractLoggingDataFromDeviceDataContent:content",
      params: { imei },
    });
    const data = [];
    content.AVL_Datas.sort((a, b) => {
      return b.Timestamp.getTime() - a.Timestamp.getTime();
    }).forEach((element) => {
      data.push({
        timestamp: element.Timestamp,
        latitude: element.GPSelement.Latitude,
        longitude: element.GPSelement.Longitude,
        internal_battery_voltage: element.IOelement?.Elements?.["67"],
        internal_battery_current: element.IOelement?.Elements?.["68"],
        internal_battery_percent: element.IOelement?.Elements?.["113"],
        speed: element.IOelement?.Elements?.["24"],
        gsm_signal: element.IOelement?.Elements?.["21"],
        iccid1: element.IOelement?.Elements?.["11"],
        iccid2: element.IOelement?.Elements?.["14"],
        movement: element.IOelement?.Elements?.["240"],
        external_voltage: element.IOelement?.Elements?.["66"],
        external_extended_voltage: element.IOelement?.Elements?.["800"],
        analog_input_1: element.IOelement?.Elements?.["9"],
        analog_input_2: element.IOelement?.Elements?.["6"],
        trip_odometer: element.IOelement?.Elements?.["199"],
        total_odometer: element.IOelement?.Elements?.["16"],
        x_axis: element.IOelement?.Elements?.["17"],
        y_axis: element.IOelement?.Elements?.["18"],
        z_axis: element.IOelement?.Elements?.["19"],
        sleep_mode: element.IOelement?.Elements?.["200"],
        gsm_cell_id: element.IOelement?.Elements?.["205"],
        gsm_area_code: element.IOelement?.Elements?.["206"],
        digital_input_1: element.IOelement?.Elements?.["1"],
        digital_input_2: element.IOelement?.Elements?.["2"],
        digital_input_3: element.IOelement?.Elements?.["3"],
        digital_input_4: element.IOelement?.Elements?.["262"],
        digital_output_1: element.IOelement?.Elements?.["179"],
        digital_output_2: element.IOelement?.Elements?.["180"],
        dout1_overcurrent: element.IOelement?.Elements?.["841"],
        dout2_overcurrent: element.IOelement?.Elements?.["842"],
        extended_analog_input_1: element.IOelement?.Elements?.["839"],
        extended_analog_input_2: element.IOelement?.Elements?.["840"],
        instant_movement: element.IOelement?.Elements?.["303"],
        iso6709_coordinates: element.IOelement?.Elements?.["387"],
      });
    });
    Logger.log("info", {
      message: "iot_util:extractLoggingDataFromDeviceDataContent:data",
      params: {
        imei,
        data: {
          timestamp: data.timestamp,
          latitude: data.latitude,
          longitude: data.longitude,
          internalBatteryPercent: data.internalBatteryPercent,
          externalVoltage: data.externalVoltage,
        },
      },
    });
    return data.length > 0 ? data[0] : null;
  } catch (error) {
    Logger.log("error", {
      message: "iot_util:extractLoggingDataFromDeviceDataContent:catch-1",
      params: { imei, error },
    });
    return null;
  }
};
module.exports = {
  getParsedPacket,
  processPacket,
  extractDataFromDeviceDataContent,
  extractLoggingDataFromDeviceDataContent,
};
