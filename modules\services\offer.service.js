const moment = require("moment");
const { prisma } = require("../../config/prisma");
const constants = require("../../constants");
const Logger = require("../../utils/logger");
const { UserService } = require("./user.service");
const { BookingReceipt } = require("../models/BookingReceipt");
const { ReservationReceipt } = require("../models/ReservationReceipt");
const { isNull, isUndefined, cloneDeep } = require("lodash");

// const { RazorpayService } = require("./razorpay.service");

class OfferService {
  constructor() {}

  /**
   *
   * @param {object} param0
   * @param {import("@prisma/client").tbl_users & {tbl_user_types:import("@prisma/client").tbl_user_types} & {tbl_location_offers_map:import("@prisma/client").tbl_location_offers_map}} param0.user

   * @returns
   */
  static getAllocatedReservationTypes = async ({ user }) => {
    try {
      Logger.log("info", {
        message: "OfferService:getAllocatedReservationTypes:params",
        params: {
          userID: user.user_id,
          userType: user.user_type_id,
        },
      });
      const allocatedReservationTypeIDs =
        UserService.getAllocatedReservationTypes(user);
      Logger.log("info", {
        message:
          "OfferService:getAllocatedReservationTypes:allocatedReservationTypeIDs",
        params: {
          userID: user.user_id,
          userType: user.user_type_id,
          allocatedReservationTypeIDs,
        },
      });
      const allocatedReservationTypes =
        await prisma.tbl_reservation_types.findMany({
          where: {
            reservation_type_id: { in: allocatedReservationTypeIDs },
            is_disabled: false,
          },
        });
      Logger.log("info", {
        message:
          "OfferService:getAllocatedReservationTypes:allocatedReservationTypes",
        params: {
          userID: user.user_id,
          userType: user.user_type_id,
          allocatedReservationTypes,
        },
      });

      return allocatedReservationTypes;
    } catch (error) {
      Logger.log("error", {
        message: "OfferService:getAllocatedReservationTypes:catch-1",
        params: { error },
      });
      return null;
    }
  };

  /**
   *
   * @param {object} param0
   * @param {import("@prisma/client").tbl_users & {tbl_user_types:import("@prisma/client").tbl_user_types} & {tbl_location_offers_map:import("@prisma/client").tbl_location_offers_map}} param0.user
   * @param {Number} param0.take
   * @param {Number} param0.skip
   * @param {Number|null} param0.vehicleTypeID
   * @returns
   */
  static getAllocatedSubscriptionTypes = async ({
    user,
    take,
    skip,
    vehicleTypeID,
  }) => {
    try {
      Logger.log("info", {
        message: "OfferService:getAllocatedSubscriptionTypes:params",
        params: {
          userID: user.user_id,
          userType: user.user_type_id,
          skip,
          take,
          vehicleTypeID,
        },
      });

      const allocatedSubscriptionTypeIDs =
        UserService.getAllocatedSubscriptionTypes(user);
      Logger.log("info", {
        message:
          "OfferService:getAllocatedSubscriptionTypes:allocatedSubscriptionTypeIDs",
        params: {
          userID: user.user_id,
          userType: user.user_type_id,
          allocatedSubscriptionTypeIDs,
        },
      });
      const allocatedSubscriptionTypes =
        await prisma.tbl_subscription_types.findMany({
          where:
            !isNull(vehicleTypeID) && !isUndefined(vehicleTypeID)
              ? {
                  subscription_type_id: { in: allocatedSubscriptionTypeIDs },
                  is_disabled: false,
                  OR: [
                    {
                      vehicle_type_ids: {
                        has: vehicleTypeID,
                      },
                    },
                    {
                      vehicle_type_ids: {
                        isEmpty: true,
                      },
                    },
                  ],
                }
              : {
                  subscription_type_id: { in: allocatedSubscriptionTypeIDs },
                  is_disabled: false,
                },
          skip: skip,
          take: take,
        });
      Logger.log("info", {
        message:
          "OfferService:getAllocatedSubscriptionTypes:allocatedSubscriptionTypes",
        params: {
          userID: user.user_id,
          userType: user.user_type_id,
          allocatedSubscriptionTypes,
        },
      });
      const relatedVehicleTypeIDs = allocatedSubscriptionTypes
        .map(
          (allocatedSubscriptionType) =>
            allocatedSubscriptionType.vehicle_type_ids
        )
        .flat();

      const relatedVehicleTypes = await prisma.tbl_vehicle_types.findMany({
        where: {
          vehicle_type_id: {
            in: relatedVehicleTypeIDs,
          },
        },
      });
      Logger.log("info", {
        message:
          "OfferService:getAllocatedSubscriptionTypes:relatedVehicleTypes",
        params: {
          userID: user.user_id,
          userType: user.user_type_id,
          relatedVehicleTypes,
        },
      });
      const aggregatedSubscriptionTypes = [];
      allocatedSubscriptionTypes.forEach((allocatedSubscriptionType) => {
        const _relatedVehicleTypeIDs =
          allocatedSubscriptionType.vehicle_type_ids.map((a) => parseInt(a));
        const _relatedVehicleTypes = relatedVehicleTypes.filter(
          (relatedVehicleType) => {
            return _relatedVehicleTypeIDs.includes(
              relatedVehicleType.vehicle_type_id
            );
          }
        );
        Logger.log("info", {
          message:
            "OfferService:getAllocatedSubscriptionTypes:allocatedSubscriptionTypes.forEach:_relatedVehicleTypeIDs",
          params: {
            userID: user.user_id,
            userType: user.user_type_id,
            allocatedSubscriptionType:
              allocatedSubscriptionType.subscription_type_id,
            _relatedVehicleTypeIDs,
          },
        });
        aggregatedSubscriptionTypes.push({
          ...allocatedSubscriptionType,
          tbl_vehicle_types: _relatedVehicleTypes,
        });
      });
      return aggregatedSubscriptionTypes;
    } catch (error) {
      Logger.log("error", {
        message: "OfferService:getAllocatedSubscriptionTypes:catch-1",
        params: { error },
      });
      return null;
    }
  };

  /**
   *
   * @param {object} param0
   * @param {import("@prisma/client").tbl_users & {tbl_user_types:import("@prisma/client").tbl_user_types}& {tbl_location_offers_map:import("@prisma/client").tbl_location_offers_map}} param0.user
   * @param {Number} param0.take
   * @param {Number} param0.skip
   * @param {Number|null} param0.vehicleTypeID
   * @returns
   */
  static getAllocatedPlanTypes = async ({
    user,
    take,
    skip,
    vehicleTypeID,
  }) => {
    try {
      Logger.log("info", {
        message: "OfferService:getAllocatedPlanTypes:params",
        params: {
          userID: user.user_id,
          userType: user.user_type_id,
          skip,
          take,
          vehicleTypeID,
        },
      });
      const allocatedPlanTypeIDs = UserService.getAllocatedPlanTypes(user);
      Logger.log("info", {
        message: "OfferService:getAllocatedPlanTypes:allocatedPlanTypeIDs",
        params: {
          userID: user.user_id,
          userType: user.user_type_id,
          allocatedPlanTypeIDs,
        },
      });
      const allocatedPlanTypes = await prisma.tbl_plan_types.findMany({
        where:
          !isNull(vehicleTypeID) && !isUndefined(vehicleTypeID)
            ? {
                plan_type_id: { in: allocatedPlanTypeIDs },
                is_disabled: false,
                OR: [
                  {
                    vehicle_type_ids: {
                      has: vehicleTypeID,
                    },
                  },
                  {
                    vehicle_type_ids: {
                      isEmpty: true,
                    },
                  },
                ],
              }
            : {
                plan_type_id: { in: allocatedPlanTypeIDs },
                is_disabled: false,
              },
        skip: skip,
        take: take,
      });
      Logger.log("info", {
        message: "OfferService:getAllocatedPlanTypes:allocatedPlanTypes",
        params: {
          userID: user.user_id,
          userType: user.user_type_id,
          allocatedPlanTypes,
        },
      });
      const relatedVehicleTypeIDs = allocatedPlanTypes
        .map(
          (allocatedSubscriptionType) =>
            allocatedSubscriptionType.vehicle_type_ids
        )
        .flat();

      Logger.log("info", {
        message: "OfferService:getAllocatedPlanTypes:relatedVehicleTypeIDs",
        params: {
          userID: user.user_id,
          userType: user.user_type_id,
          relatedVehicleTypeIDs,
        },
      });
      const relatedVehicleTypes = await prisma.tbl_vehicle_types.findMany({
        where: {
          vehicle_type_id: {
            in: relatedVehicleTypeIDs,
          },
        },
      });

      Logger.log("info", {
        message: "OfferService:getAllocatedPlanTypes:relatedVehicleTypes",
        params: {
          userID: user.user_id,
          userType: user.user_type_id,
          relatedVehicleTypes,
        },
      });
      const aggregatedPlanTypes = [];
      allocatedPlanTypes.forEach((allocatedPlanType) => {
        const _relatedVehicleTypeIDs = allocatedPlanType.vehicle_type_ids.map(
          (a) => parseInt(a)
        );
        const _relatedVehicleTypes = relatedVehicleTypes.filter(
          (relatedVehicleType) => {
            return _relatedVehicleTypeIDs.includes(
              relatedVehicleType.vehicle_type_id
            );
          }
        );
        Logger.log("info", {
          message:
            "OfferService:getAllocatedPlanTypes:allocatedPlanTypes.forEach:_relatedVehicleTypes",
          params: {
            userID: user.user_id,
            userType: user.user_type_id,
            allocatedPlanType: allocatedPlanType.plan_type_id,
            _relatedVehicleTypes,
          },
        });
        aggregatedPlanTypes.push({
          ...allocatedPlanType,
          tbl_vehicle_types: _relatedVehicleTypes,
        });
      });
      return aggregatedPlanTypes;
    } catch (error) {
      Logger.log("error", {
        message: "OfferService:getAllocatedPlanTypes:catch-1",
        params: { error },
      });
      return null;
    }
  };

  /**
   *
   * @param {object} param0
   * @param {import("@prisma/client").tbl_users & {tbl_user_types:import("@prisma/client").tbl_user_types}} param0.user
   * @param {Number} param0.take
   * @param {Number} param0.skip
   * @param {Number|null} param0.vehicleTypeID
   * @returns
   */
  static getUserSubscriptions = async ({ user, take, skip, vehicleTypeID }) => {
    try {
      Logger.log("info", {
        message: "OfferService:getUserSubscriptions:params",
        params: {
          userID: user.user_id,
          userType: user.user_type_id,
          skip,
          take,
          vehicleTypeID,
        },
      });

      const validity = new Date();

      const userSubscriptions = await prisma.tbl_subscriptions.findMany({
        where: {
          user_id: user.user_id,
          is_disabled: false,
          OR: [
            { validity: { gt: validity } },
            { validity: undefined },
            { validity: null },
          ],
          tbl_subscription_types: {
            is:
              !isNull(vehicleTypeID) && !isUndefined(vehicleTypeID)
                ? {
                    is_disabled: false,
                    OR: [
                      {
                        vehicle_type_ids: {
                          has: vehicleTypeID,
                        },
                      },
                      {
                        vehicle_type_ids: {
                          isEmpty: true,
                        },
                      },
                    ],
                  }
                : {
                    is_disabled: false,
                  },
          },
        },
        include: {
          tbl_subscription_types: true,
        },
        skip: skip,
        take: take,
      });
      Logger.log("info", {
        message: "OfferService:getUserSubscriptions:userSubscriptions",
        params: {
          userID: user.user_id,
          userType: user.user_type_id,
          userSubscriptions: userSubscriptions.length,
        },
      });
      return userSubscriptions;
    } catch (error) {
      Logger.log("error", {
        message: "OfferService:getUserSubscriptions:catch-1",
        params: { error },
      });
      return null;
    }
  };

  /**
   *
   * @param {object} param0
   * @param {import("@prisma/client").tbl_users & {tbl_user_types:import("@prisma/client").tbl_user_types}} param0.user
   * @param {Number} param0.take
   * @param {Number} param0.skip
   * @param {Number|null} param0.vehicleTypeID
   * @returns
   */
  static getUserCoupons = async ({ user, take, skip, vehicleTypeID }) => {
    try {
      Logger.log("info", {
        message: "OfferService:getUserCoupons:params",
        params: {
          userID: user.user_id,
          userType: user.user_type_id,
          skip,
          take,
          vehicleTypeID,
        },
      });

      const validity = new Date();

      const userCoupons = await prisma.tbl_coupons.findMany({
        where: {
          user_id: user.user_id,
          is_disabled: false,
          OR: [
            { validity: { gt: validity } },
            { validity: undefined },
            { validity: null },
          ],
          tbl_coupon_types: {
            is:
              !isNull(vehicleTypeID) && !isUndefined(vehicleTypeID)
                ? {
                    is_disabled: false,
                    OR: [
                      {
                        vehicle_type_ids: {
                          has: vehicleTypeID,
                        },
                      },
                      {
                        vehicle_type_ids: {
                          isEmpty: true,
                        },
                      },
                    ],
                  }
                : {
                    is_disabled: false,
                  },
          },
        },
        include: {
          tbl_coupon_types: true,
        },
        skip: skip,
        take: take,
      });
      Logger.log("info", {
        message: "OfferService:getUserCoupons:userPlans",
        params: {
          userID: user.user_id,
          userType: user.user_type_id,
          userCouponsLength: userCoupons?.length,
        },
      });
      return userCoupons;
    } catch (error) {
      Logger.log("error", {
        message: "OfferService:getUserCoupons:catch-1",
        params: { error },
      });
      return null;
    }
  };

  /**
   *
   * @param {object} param0
   * @param {import("@prisma/client").tbl_users & {tbl_user_types:import("@prisma/client").tbl_user_types}} param0.user
   * @param {Number} param0.take
   * @param {Number} param0.skip
   *  @param {Number|null} param0.vehicleTypeID
   * @returns
   */
  static getUserPlans = async ({ user, take, skip, vehicleTypeID }) => {
    try {
      Logger.log("info", {
        message: "OfferService:getUserPlans:params",
        params: {
          userID: user.user_id,
          userType: user.user_type_id,
          skip,
          take,
          vehicleTypeID,
        },
      });

      const validity = new Date();

      const userPlans = await prisma.tbl_plans.findMany({
        where: {
          user_id: user.user_id,
          is_disabled: false,
          OR: [
            { validity: { gt: validity } },
            { validity: undefined },
            { validity: null },
          ],
          tbl_plan_types: {
            is:
              !isNull(vehicleTypeID) && !isUndefined(vehicleTypeID)
                ? {
                    is_disabled: false,
                    OR: [
                      {
                        vehicle_type_ids: {
                          has: vehicleTypeID,
                        },
                      },
                      {
                        vehicle_type_ids: {
                          isEmpty: true,
                        },
                      },
                    ],
                  }
                : {
                    is_disabled: false,
                  },
          },
        },
        include: {
          tbl_plan_types: true,
        },
        skip: skip,
        take: take,
      });
      Logger.log("info", {
        message: "OfferService:getUserPlans:userPlans",
        params: {
          userID: user.user_id,
          userType: user.user_type_id,
          userPlans: userPlans.length,
        },
      });

      return userPlans;
    } catch (error) {
      Logger.log("error", {
        message: "OfferService:getUserPlans:catch-1",
        params: { error },
      });
      return null;
    }
  };
  /**
   *
   * @param {object} param0
   * @param {import("@prisma/client").tbl_users & {tbl_user_types:import("@prisma/client").tbl_user_types}} param0.user
   * @param {Number} param0.id
   * @returns
   */
  static getUserSubscriptionByID = async ({ user, id }) => {
    try {
      Logger.log("info", {
        message: "OfferService:getUserSubscriptionByID:params",
        params: { userID: user.user_id, id },
      });

      const validity = new Date();

      const subscriptionByID = await prisma.tbl_subscriptions.findFirst({
        where: {
          user_id: user.user_id,
          is_disabled: false,
          subscription_id: id,
          OR: [
            { validity: { gt: validity } },
            { validity: undefined },
            { validity: null },
          ],
        },
        include: {
          tbl_subscription_types: true,
        },
      });
      Logger.log("info", {
        message: "OfferService:getUserSubscriptionByID:userSubscriptions",
        params: {
          userID: user.user_id,
          subscriptionByID,
        },
      });

      return subscriptionByID;
    } catch (error) {
      Logger.log("error", {
        message: "OfferService:getUserSubscriptionByID:catch-1",
        params: { error },
      });
      return null;
    }
  };

  /**
   *
   * @param {object} param0
   * @param {import("@prisma/client").tbl_users & {tbl_user_types:import("@prisma/client").tbl_user_types}} param0.user
   * @param {Number} param0.id
   * @returns
   */
  static getUserPlanByID = async ({ user, id }) => {
    try {
      Logger.log("info", {
        message: "OfferService:getUserPlanByID:params",
        params: { userID: user.user_id, id },
      });

      const validity = new Date();

      const planByID = await prisma.tbl_plans.findFirst({
        where: {
          user_id: user.user_id,
          is_disabled: false,
          plan_id: id,
          OR: [
            { validity: { gt: validity } },
            { validity: undefined },
            { validity: null },
          ],
        },
        include: {
          tbl_plan_types: true,
        },
      });
      Logger.log("info", {
        message: "OfferService:getUserPlanByID:userPlans",
        params: {
          userID: user.user_id,
          planByID,
        },
      });

      return planByID;
    } catch (error) {
      Logger.log("error", {
        message: "OfferService:getUserPlanByID:catch-1",
        params: { error },
      });
      return null;
    }
  };

  /**
   *
   * @param {object} param0
   * @param {import("@prisma/client").tbl_users & {tbl_user_types:import("@prisma/client").tbl_user_types}} param0.user
   * @param {Number} param0.id
   * @returns
   */
  static activateUserPlanByID = async ({ user, id }) => {
    try {
      Logger.log("info", {
        message: "OfferService:activateUserPlanByID:params",
        params: { userID: user.user_id, id },
      });

      const validity = new Date();

      const planByID = await prisma.tbl_plans.findFirst({
        where: {
          user_id: user.user_id,
          is_disabled: false,
          plan_id: id,
          OR: [
            { validity: { gt: validity } },
            { validity: undefined },
            { validity: null },
          ],
        },
        include: {
          tbl_plan_types: true,
        },
      });

      if (planByID.validity == undefined || planByID.validity == null) {
        const _validity = moment(Date.now()).add(
          planByID.tbl_plan_types.validity_period,
          "minutes"
        );
        const activatedPlanByID = await prisma.tbl_plans.update({
          where: {
            plan_id: id,
          },
          data: {
            validity: _validity,
          },
        });
      }

      Logger.log("info", {
        message: "OfferService:activateUserPlanByID:success",
        params: {
          userID: user.user_id,
          id,
        },
      });

      return true;
    } catch (error) {
      Logger.log("error", {
        message: "OfferService:activateUserPlanByID:catch-1",
        params: { error },
      });
      return false;
    }
  };

  /**
   *
   * @param {object} param0
   * @param {import("@prisma/client").tbl_users} param0.user
   * @param {String} param0.paymentSource
   * @param {Number} param0.subscriptionTypeID
   */
  static createSubscriptionPurchaseReceipt = async ({
    user,
    subscriptionTypeID,
    paymentSource,
  }) => {
    try {
      Logger.log("info", {
        message: "OfferService:createSubscriptionPurchaseReceipt:params",
        params: {
          userID: user.user_id,
          paymentSource,
          subscriptionTypeID,
        },
      });

      const subscriptionPurchaseReceipt =
        await prisma.tbl_subscription_purchase_receipts.create({
          data: {
            user_id: user.user_id,
            payment_source: paymentSource,
            subscription_type_id: subscriptionTypeID,
            subscription_purchase_receipt_status:
              constants.SUBSCRIPTION_PURCHASE_RECEIPT_STATUS.PENDING,
          },
        });
      Logger.log("success", {
        message:
          "OfferService:createSubscriptionPurchaseReceipt:subscriptionPurchaseReceipt",
        params: { subscriptionPurchaseReceipt },
      });
      return {
        subscriptionPurchaseReceiptID:
          subscriptionPurchaseReceipt.subscription_purchase_receipt_id,
      };
    } catch (error) {
      Logger.log("error", {
        message: "OfferService:createSubscriptionPurchaseReceipt:catch-1",
        params: { error },
      });
      throw error;
    }
  };

  /**
   *
   * @param {object} param0
   * @param {import("@prisma/client").tbl_users} param0.user
   * @param {String} param0.paymentSource
   * @param {Number} param0.planTypeID
   */
  static createPlanPurchaseReceipt = async ({
    user,
    planTypeID,
    paymentSource,
  }) => {
    try {
      Logger.log("info", {
        message: "OfferService:createPlanPurchaseReceipt:params",
        params: {
          userID: user.user_id,
          paymentSource,
          planTypeID,
        },
      });

      const planPurchaseReceipt =
        await prisma.tbl_plan_purchase_receipts.create({
          data: {
            user_id: user.user_id,
            payment_source: paymentSource,
            plan_type_id: planTypeID,
            plan_purchase_receipt_status:
              constants.PLAN_PURCHASE_RECEIPT_STATUS.PENDING,
          },
        });
      Logger.log("success", {
        message: "OfferService:createPlanPurchaseReceipt:planPurchaseReceipt",
        params: { planPurchaseReceipt },
      });
      return {
        planPurchaseReceiptID: planPurchaseReceipt.plan_purchase_receipt_id,
      };
    } catch (error) {
      Logger.log("error", {
        message: "OfferService:createPlanPurchaseReceipt:catch-1",
        params: { error },
      });
      throw error;
    }
  };

  /**
   *
   * @param {object} param0
   * @param {import("@prisma/client").tbl_users} param0.user
   * @param {String} param0.paymentSource
   * @param {Number} param0.couponTypeID
   */
  static createCouponPurchaseReceipt = async ({
    user,
    couponTypeID,
    paymentSource,
  }) => {
    try {
      Logger.log("info", {
        message: "OfferService:createCouponPurchaseReceipt:params",
        params: {
          userID: user.user_id,
          paymentSource,
          couponTypeID,
        },
      });

      const couponPurchaseReceipt =
        await prisma.tbl_coupon_purchase_receipts.create({
          data: {
            user_id: user.user_id,
            payment_source: paymentSource,
            coupon_type_id: couponTypeID,
            coupon_purchase_receipt_status:
              constants.COUPON_PURCHASE_RECEIPT_STATUS.PENDING,
          },
        });
      Logger.log("success", {
        message:
          "OfferService:createCouponPurchaseReceipt:couponPurchaseReceipt",
        params: { couponPurchaseReceipt },
      });
      return {
        couponPurchaseReceiptID:
          couponPurchaseReceipt.coupon_purchase_receipt_id,
      };
    } catch (error) {
      Logger.log("error", {
        message: "OfferService:createCouponPurchaseReceipt:catch-1",
        params: { error },
      });
      throw error;
    }
  };

  /**
   *
   * @param {object} param0
   * @param {import("@prisma/client").tbl_users} param0.user
   * @param {Number} param0.subscriptionPurchaseReceiptID
   */
  static assignSubscriptionTypeToUser = async ({
    user,
    subscriptionPurchaseReceiptID,
  }) => {
    try {
      Logger.log("info", {
        message: "OfferService:assignSubscriptionTypeToUser:params",
        params: {
          userID: user.user_id,
          subscriptionPurchaseReceiptID,
        },
      });

      const subscriptionPurchaseReceipt =
        await prisma.tbl_subscription_purchase_receipts.findUnique({
          where: {
            subscription_purchase_receipt_id: subscriptionPurchaseReceiptID,
          },
          include: {
            tbl_subscription_types: true,
          },
        });

      const subscriptionType =
        subscriptionPurchaseReceipt.tbl_subscription_types;
      
      

      const subscriptionTypeValidity = subscriptionType.validity_period
        ? moment(new Date()).add(subscriptionType.validity_period, 'minutes').toDate()
        : null;
      

      Logger.log("info", {
        message:
          "OfferService:assignSubscriptionTypeToUser:subscriptionPurchaseReceiptID",
        params: {
          userID: user.user_id,
          subscriptionPurchaseReceiptID,
          subscriptionTypeValidity,
        },
      });

      return await prisma.$transaction(async (tx) => {
        

        const newSubscription = await tx.tbl_subscriptions.create({
          data: {
            user_id: user.user_id,
            validity: subscriptionTypeValidity,
            subscription_type_id: subscriptionType.subscription_type_id,
            remaining_free_booking_time: subscriptionType.free_booking_time,
          },
        });
        Logger.log("info", {
          message:
            "OfferService:assignSubscriptionTypeToUser:prisma.$transaction:newSubscription",
          params: {
            userID: user.user_id,
            newSubscription,
            subscriptionPurchaseReceiptID,
          },
        });

        const updatedSubscriptionPurchaseReceipt =
          await tx.tbl_subscription_purchase_receipts.update({
            where: {
              subscription_purchase_receipt_id: subscriptionPurchaseReceiptID,
            },
            data: {
              
              subscription_purchase_receipt_status:
                constants.SUBSCRIPTION_PURCHASE_RECEIPT_STATUS.SUCCESS,
              alloted_subscription_id: newSubscription.subscription_id,
            },
          });
        Logger.log("info", {
          message:
            "OfferService:assignSubscriptionTypeToUser:prisma.$transaction:updatedSubscriptionPurchaseReceipt",
          params: {
            userID: user.user_id,

            newSubscription: newSubscription.subscription_id,
            updatedSubscriptionPurchaseReceipt,
          },
        });

        return newSubscription;
      });
    } catch (error) {
      Logger.log("error", {
        message: "OfferService:assignSubscriptionTypeToUser:catch-1",
        params: { error },
      });
      throw error;
    }
  };

  /**
   *
   * @param {object} param0
   * @param {import("@prisma/client").tbl_users} param0.user
   * @param {Number} param0.planPurchaseReceiptID
   */
  static assignPlanTypeToUser = async ({
    user,

    planPurchaseReceiptID,
  }) => {
    try {
      Logger.log("info", {
        message: "OfferService:assignPlanTypeToUser:params",
        params: {
          userID: user.user_id,
          planPurchaseReceiptID,
        },
      });
      const planPurchaseReceipt =
        await prisma.tbl_plan_purchase_receipts.findUnique({
          where: {
            plan_purchase_receipt_id: planPurchaseReceiptID,
          },
          include: {
            tbl_plan_types: true,
          },
        });
      const planType = planPurchaseReceipt.tbl_plan_types;
      

      Logger.log("info", {
        message: "OfferService:assignPlanTypeToUser:planPurchaseReceiptID",
        params: {
          userID: user.user_id,
          planPurchaseReceiptID,
        },
      });

      return await prisma.$transaction(async (tx) => {
        
        const newPlan = await tx.tbl_plans.create({
          data: {
            user_id: user.user_id,
            validity: null,
            plan_type_id: planType.plan_type_id,
          },
        });
        Logger.log("info", {
          message:
            "OfferService:assignPlanTypeToUser:prisma.$transaction:newPlan",
          params: {
            userID: user.user_id,
            newPlan,
            planPurchaseReceiptID,
          },
        });

        const updatedPlanPurchaseReceipt =
          await tx.tbl_plan_purchase_receipts.update({
            where: {
              plan_purchase_receipt_id: planPurchaseReceiptID,
            },
            data: {
              
              plan_purchase_receipt_status:
                constants.PLAN_PURCHASE_RECEIPT_STATUS.SUCCESS,
              alloted_plan_id: newPlan.plan_id,
            },
          });
        Logger.log("info", {
          message:
            "OfferService:assignPlanTypeToUser:prisma.$transaction:updatedPlanPurchaseReceipt",
          params: {
            userID: user.user_id,
            newPlan: newPlan.plan_id,
            updatedPlanPurchaseReceipt,
          },
        });

        return newPlan;
      });
    } catch (error) {
      Logger.log("error", {
        message: "OfferService:assignPlanTypeToUser:catch-1",
        params: { error },
      });
      throw error;
    }
  };

  /**
   *
   * @param {object} param0
   * @param {import("@prisma/client").tbl_users} param0.user
   * @param {Number} param0.couponPurchaseReceiptID
   */
  static assignCouponTypeToUser = async ({ user, couponPurchaseReceiptID }) => {
    try {
      Logger.log("info", {
        message: "OfferService:assignCouponTypeToUser:params",
        params: {
          userID: user.user_id,
          couponPurchaseReceiptID,
        },
      });
      const couponPurchaseReceipt =
        await prisma.tbl_coupon_purchase_receipts.findUnique({
          where: {
            coupon_purchase_receipt_id: couponPurchaseReceiptID,
          },
          include: {
            tbl_coupon_types: true,
          },
        });
      const couponType = couponPurchaseReceipt.tbl_coupon_types;

      Logger.log("info", {
        message: "OfferService:assignCouponTypeToUser:couponPurchaseReceiptID",
        params: {
          userID: user.user_id,
          couponPurchaseReceiptID,
        },
      });

      return await prisma.$transaction(async (tx) => {
        const newCoupon = await tx.tbl_coupons.create({
          data: {
            coupon_type_id: couponPurchaseReceipt.coupon_type_id,
            rule_action_limiting_value_balance:
              couponType.rule_action_limiting_value_action ==
              constants.COUPON_RULE_ACTION_LIMITING_VALUE_ACTIONS.SUBSTRACT
                ? couponType.rule_action_limiting_value
                : 0,
            validity: couponType.validity_period
              ? moment().add(couponType.validity_period, "minutes").toDate()
              : null,
            usage_balance: couponType.usage_limit
              ? couponType.usage_limit
              : null,
            user_id: user.user_id,
          },
        });

        Logger.log("info", {
          message:
            "OfferService:assignCouponTypeToUser:prisma.$transaction:newCoupon",
          params: {
            userID: user.user_id,
            newCoupon,
            couponPurchaseReceiptID,
          },
        });

        const updatedCouponPurchaseReceipt =
          await tx.tbl_coupon_purchase_receipts.update({
            where: {
              coupon_purchase_receipt_id: couponPurchaseReceiptID,
            },
            data: {
              coupon_purchase_receipt_status:
                constants.COUPON_PURCHASE_RECEIPT_STATUS.SUCCESS,
              alloted_coupon_id: newCoupon.coupon_id,
            },
          });
        Logger.log("info", {
          message:
            "OfferService:assignCouponTypeToUser:prisma.$transaction:updatedCouponPurchaseReceipt",
          params: {
            userID: user.user_id,
            newCoupon: newCoupon.coupon_id,
            updatedCouponPurchaseReceipt,
          },
        });

        return newCoupon;
      });
    } catch (error) {
      Logger.log("error", {
        message: "OfferService:assignCouponTypeToUser:catch-1",
        params: { error },
      });
      throw error;
    }
  };

  /**
   *
   * @param {object} param0
   * @param {import("@prisma/client").tbl_users & {tbl_user_types:import("@prisma/client").tbl_user_types}} param0.user
   */
  static canAssignSubscriptionTypeToUser = async ({ user }) => {
    try {
      const validity = new Date();
      const userSubscriptionsCount = await prisma.tbl_subscriptions.count({
        where: {
          user_id: parseInt(user.user_id),
          is_disabled: false,
          OR: [
            { validity: { gt: validity } },
            { validity: undefined },
            { validity: null },
          ],
        },
      });

      Logger.log("info", {
        message:
          "OfferService:canAssignSubscriptionTypeToUser:max allowed subscriptions",
        params: {
          userID: user.user_id,
          userSubscriptionsCount,
          allowedSubscriptions: parseInt(user.tbl_user_types.max_subscriptions),
        },
      });

      if (
        userSubscriptionsCount < parseInt(user.tbl_user_types.max_subscriptions)
      ) {
        return true;
      } else {
        return false;
      }
    } catch (error) {
      Logger.log("error", {
        message: "OfferService:canAssignSubscriptionTypeToUser:catch-1",
        params: { error },
      });
      return false;
    }
  };

  /**
   *
   * @param {object} param0
   * @param {import("@prisma/client").tbl_users & {tbl_user_types:import("@prisma/client").tbl_user_types}} param0.user
   */
  static canAssignPlanTypeToUser = async ({ user }) => {
    try {
      const validity = new Date();
      const userPlansCount = await prisma.tbl_plans.count({
        where: {
          user_id: parseInt(user.user_id),
          is_disabled: false,
          OR: [
            { validity: { gt: validity } },
            { validity: undefined },
            { validity: null },
          ],
        },
      });

      Logger.log("info", {
        message: "OfferService:canAssignPlanTypeToUser:max allowed plans",
        params: {
          userID: user.user_id,
          userPlansCount,
          allowedPlans: parseInt(user.tbl_user_types.max_plans),
        },
      });

      if (userPlansCount < parseInt(user.tbl_user_types.max_plans)) {
        return true;
      } else {
        return false;
      }
    } catch (error) {
      Logger.log("error", {
        message: "OfferService:canAssignPlanTypeToUser:catch-1",
        params: { error },
      });
      return false;
    }
  };

  /**
   *
   * @param {object} param0
   * @param {import("@prisma/client").tbl_users & {tbl_user_types:import("@prisma/client").tbl_user_types}} param0.user
   */
  static canAssignCouponTypeToUser = async ({ user }) => {
    try {
      const validity = new Date();
      const userCouponsCount = await prisma.tbl_coupons.count({
        where: {
          user_id: parseInt(user.user_id),
          is_disabled: false,
          OR: [
            { validity: { gt: validity } },
            { validity: undefined },
            { validity: null },
          ],
        },
      });

      Logger.log("info", {
        message: "OfferService:canAssignCouponTypeToUser:max allowed coupons",
        params: {
          userID: user.user_id,
          userCouponsCount,
          allowedCoupons: parseInt(user.tbl_user_types.max_coupons),
        },
      });

      if (userCouponsCount < parseInt(user.tbl_user_types.max_coupons)) {
        return true;
      } else {
        return false;
      }
    } catch (error) {
      Logger.log("error", {
        message: "OfferService:canAssignCouponTypeToUser:catch-1",
        params: { error },
      });
      return false;
    }
  };

  /**
   *
   * @param {object} param0
   * @param {import("@prisma/client").tbl_bookings & {tbl_vehicles:import("@prisma/client").tbl_vehicles & {tbl_vehicle_types:import("@prisma/client").tbl_vehicle_types}} & {tbl_booking_logs:Array<import("@prisma/client").tbl_booking_logs>} & {tbl_users:import("@prisma/client").tbl_users & {tbl_location_offers_map: import("@prisma/client").tbl_location_offers_map}}} param0.booking
   */
  static getPlanToApplyOnBookingReceipt = async ({ booking }) => {
    try {
      Logger.log("info", {
        message: "OfferService:getPlanToApplyOnBookingReceipt:init",
        params: {
          booking: booking.booking_id,
        },
      });
      // get the assigned plan to user
      const plan = await prisma.tbl_plans.findFirst({
        where: {
          user_id: booking.user_id,
          plan_id: booking.user_selected_plan_id,
          is_disabled: false,
        },
        include: {
          tbl_plan_types: true,
        },
      });

      Logger.log("info", {
        message: "OfferService:getPlanToApplyOnBookingReceipt:plan",
        params: {
          booking: booking.booking_id,
          planID: plan?.plan_id,
        },
      });

      return plan;
    } catch (error) {
      Logger.log("error", {
        message: "OfferService:getPlanToApplyOnBookingReceipt:catch-1",
        params: {
          error,
        },
      });
      return null;
    }
  };

  /**
   *
   * @param {object} param0
   * @param {import("@prisma/client").tbl_bookings & {tbl_vehicles:import("@prisma/client").tbl_vehicles & {tbl_vehicle_types:import("@prisma/client").tbl_vehicle_types}} & {tbl_booking_logs:Array<import("@prisma/client").tbl_booking_logs>} & {tbl_users:import("@prisma/client").tbl_users & {tbl_location_offers_map: import("@prisma/client").tbl_location_offers_map}}} param0.booking
   */
  static getSubscriptionToApplyOnBookingReceipt = async ({ booking }) => {
    try {
      Logger.log("info", {
        message: "OfferService:getSubscriptionToApplyOnBookingReceipt:init",
        params: {
          booking: booking.booking_id,
        },
      });

      const validity = new Date(booking.booking_start_at);
      // get the assigned subscription to user
      const subscription = await prisma.tbl_subscriptions.findFirst({
        where: booking.user_selected_subscription_id
          ? {
              user_id: booking.user_id,
              subscription_id: booking.user_selected_subscription_id,
              is_disabled: false,
              tbl_subscription_types: {
                is:
                  !isNull(booking.tbl_vehicles.vehicle_type_id) &&
                  !isUndefined(booking.tbl_vehicles.vehicle_type_id)
                    ? {
                        is_disabled: false,
                        OR: [
                          {
                            vehicle_type_ids: {
                              has: booking.tbl_vehicles.vehicle_type_id,
                            },
                          },
                          {
                            vehicle_type_ids: {
                              isEmpty: true,
                            },
                          },
                        ],
                      }
                    : {
                        is_disabled: false,
                      },
              },
              OR: [
                { validity: { gt: validity } },
                { validity: undefined },
                { validity: null },
              ],
            }
          : {
              user_id: booking.user_id,
              is_disabled: false,
              tbl_subscription_types: {
                is:
                  !isNull(booking.tbl_vehicles.vehicle_type_id) &&
                  !isUndefined(booking.tbl_vehicles.vehicle_type_id)
                    ? {
                        is_disabled: false,
                        OR: [
                          {
                            vehicle_type_ids: {
                              has: booking.tbl_vehicles.vehicle_type_id,
                            },
                          },
                          {
                            vehicle_type_ids: {
                              isEmpty: true,
                            },
                          },
                        ],
                      }
                    : {
                        is_disabled: false,
                      },
              },
              OR: [
                { validity: { gt: validity } },
                { validity: undefined },
                { validity: null },
              ],
            },
        include: {
          tbl_subscription_types: true,
        },
        orderBy: {
          created_at: "desc",
        },
      });

      Logger.log("info", {
        message:
          "OfferService:getSubscriptionToApplyOnBookingReceipt:subscription",
        params: {
          booking: booking.booking_id,
          subscriptionID: subscription?.subscription_id,
        },
      });

      return subscription;
    } catch (error) {
      Logger.log("error", {
        message: "OfferService:getSubscriptionToApplyOnBookingReceipt:catch-1",
        params: {
          error,
        },
      });
      return null;
    }
  };
}
module.exports = { OfferService };
