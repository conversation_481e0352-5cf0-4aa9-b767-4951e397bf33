const express = require("express");
const router = express.Router();

const authMiddleware = require("../middlewares/auth.middleware");
const paymentOrderController = require("../controllers/paymentOrder.controller");
const {
  interceptorMiddleware,
} = require("../middlewares/interceptor.middleware");

//user routes

//get user info : auth check
router.get("/", authMiddleware, paymentOrderController.getPaymentOrders);
router.get(
  "/invoice/:id",
  interceptorMiddleware.provideUserFromURLToken,
  paymentOrderController.downloadPaymentOrderByID
);
router.get("/:id", authMiddleware, paymentOrderController.getPaymentOrderByID);

module.exports = router;
