const net = require("net");

const {
  getParsedPacket,
  extractDataFromDeviceDataContent,
} = require("../utils/iot");
const Logger = require("../utils/logger");
const { randomUUID } = require("crypto");
const { VehicleService } = require("../modules/services/vehicle.service");
const { IOTService } = require("../modules/services/iot.service");

// const { VehicleService } = require("../modules/services/vehicle.service");
global.uuid_to_socket = {};
global.imei_to_socket = {};

const broker = net.createServer((socket) => {
  const uuid = randomUUID();
  socket.on("connect", (data) => {
    Logger.log("success", {
      message: "broker:connect",
    });
  });
  socket.on("data", (data) => {
    Logger.log("info", {
      message: "broker:data",
      params: { data: data.toString("hex") },
    });

    try {
      let deviceData = getParsedPacket(data);
      Logger.log("info", {
        message: "broker:data:parsedData",
        params: {
          data: {
            imei: deviceData?.imei,
            CodecType: deviceData?.content?.CodecType,
          },
        },
      });

      if (deviceData.content == undefined && deviceData.imei != undefined) {
        Logger.log("info", {
          message: "broker:data:login",
          params: { uuid, imei: deviceData.imei },
        });
        global.uuid_to_socket[uuid] = { imei: deviceData.imei, data: [] };
        global.imei_to_socket[deviceData.imei] = socket;
        Logger.log("info", {
          message: "broker:data:login:socket saved",
          params: { imei: deviceData.imei },
        });

        // send response answer for login request
        var imei_answer;
        imei_answer = new Uint8Array(1);
        imei_answer[0] = 1;
        socket.write(imei_answer);
        Logger.log("info", {
          message: "broker:data:login:ack",
          params: { uuid, imei: deviceData.imei, ack: imei_answer },
        });
      }
      if (deviceData.content != undefined && deviceData.imei == undefined) {
        deviceData.imei = global.uuid_to_socket[uuid].imei;

        // for data sent from vehicle without request
        if (deviceData.content.CodecType === "data sending") {
          const data = extractDataFromDeviceDataContent({
            content: deviceData.content.Content,
            imei: deviceData.imei,
          });
          Logger.log("info", {
            message: "broker:data:data",
            params: {
              uuid,
              imei: deviceData.imei,
              content: data,
            },
          });
          VehicleService.logNewIOTVehicleData({
            content: deviceData.content.Content,
            imei: deviceData.imei,
          });
          // update vehicle location
          VehicleService.updateVehicleDataByIMEI({
            imei: deviceData.imei,
            data,
          });
        }
        // for data sent from vehicle as response to request
        else if (deviceData.content.CodecType === "GPRS messages") {
          Logger.log("info", {
            message: "broker:data:data",
            params: {
              uuid,
              imei: deviceData.imei,
              content: deviceData.content.Content,
            },
          });
          IOTService.confirmCommandExecution({
            imei: deviceData.imei,
            content: deviceData.content.Content,
          });
        }
        const dataReceivedPacket = Buffer.alloc(4);
        dataReceivedPacket.writeUInt32BE(deviceData.content.Quantity1);
        socket.write(dataReceivedPacket);
        Logger.log("info", {
          message: "broker:data:logged in:ack",
          params: { ack: dataReceivedPacket },
        });
      }
    } catch (error) {
      Logger.log("error", {
        message: "broker:data:catch-1",
        params: { error },
      });
    }
  });
  socket.on("error", (error) => {
    Logger.log("error", {
      message: "broker:error:catch-1",
      params: { error },
    });
  });
});

module.exports = { broker };
