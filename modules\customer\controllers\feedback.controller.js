const { prisma } = require("../../../config/prisma");
const constants = require("../../../constants");
const { extractError } = require("../../../utils/error.utils");
const Logger = require("../../../utils/logger");
const { TicketingService } = require("../../services/ticket.service");

const feedbackController = {};

/**
 *
 * @param {import("express").Request} req
 * @param {import("express").Response} res
 * @returns
 */
feedbackController.submitReview = async (req, res) => {
  Logger.log("info", { message: "feedbackController:submitReview:init" });
  try {
    const { user } = req;
    const { booking_id, text, stars } = req.body;
    const booking = await prisma.tbl_bookings.findUnique({
      where: {
        booking_id: parseInt(booking_id),
      },
    });
    if (!booking) {
      Logger.log("error", {
        message: "feedbackController:submitReview:catch-2",
        params: { error: constants.ERROR_CODES.INVALID_BOOKING_ID },
      });
      return res.json({
        success: false,
        error: constants.ERROR_CODES.INVALID_BOOKING_ID,
      });
    } else {
      Logger.log("info", {
        message: "feedbackController:submitReview:booking",
        params: {
          user_id: user.user_id,
          bookingID: booking.booking_id,
        },
      });
      const updatedBookingRecord = await prisma.tbl_bookings.update({
        where: {
          booking_id: parseInt(booking_id),
        },
        data: {
          tbl_booking_reviews: {
            create: {
              text,
              stars: parseInt(stars) || 3,
            },
          },
        },
        include: {
          tbl_booking_reviews: true,
        },
      });
      Logger.log("success", {
        message: "feedbackController:submitReview:created new review",
        params: {
          user_id: user.user_id,
          bookingID: booking.booking_id,
        },
      });

      // delete the previous review
      if (booking?.booking_review_id) {
        await prisma.tbl_booking_reviews.delete({
          where: {
            booking_review_id: booking.booking_review_id,
          },
        });
        Logger.log("success", {
          message: "feedbackController:submitReview:deleted previous review",
          params: {
            user_id: user.user_id,
            bookingID: booking.booking_id,
          },
        });
      }
      return res.json({ success: true, booking: updatedBookingRecord });
    }
  } catch (error) {
    return res.json({ success: false, error: extractError(error) });
  }
};

/**
 *
 * @param {import("express").Request} req
 * @param {import("express").Response} res
 * @returns
 */
feedbackController.logSupportTicket = async (req, res) => {
  Logger.log("info", { message: "feedbackController:logSupportTicket:init" });
  try {
    const { user } = req;
    const { text } = req.body;
    // const SupportTicketcketRecord = await prisma.tbl_support_tickets.create({
    //   data: {
    //     text: String(text),
    //     user_id: parseInt(user.user_id),
    //   },
    // });
    const supportTicketRecord = await TicketingService.createSupportTicket({
      user,
      message: text,
    });
    Logger.log("success", {
      message: "feedbackController:logSupportTicket:success",
      params: { supportTicketRecord },
    });
    return res.json({ success: true, supportTicketRecord });
  } catch (error) {
    return res.json({ success: false, error: extractError(error) });
  }
};

/**
 *
 * @param {import("express").Request} req
 * @param {import("express").Response} res
 * @returns
 */
feedbackController.getAllSupportTickets = async (req, res) => {
  Logger.log("info", {
    message: "feedbackController:getAllSupportTickets:init",
  });
  try {
    const { user } = req;
    const supportTicketRecords =
      await TicketingService.retrieveAllSupportTickets({
        user,
      });
    Logger.log("success", {
      message: "feedbackController:getAllSupportTickets:success",
    });
    return res.json({ success: true, supportTicketRecords });
  } catch (error) {
    Logger.log("error", {
      message: "feedbackController:getAllSupportTickets:catch-1",
      params: { error },
    });
    return res.json({ success: false, error: extractError(error) });
  }
};

/**
 *
 * @param {import("express").Request} req
 * @param {import("express").Response} res
 * @returns
 */
feedbackController.getSupportTicketMessage = async (req, res) => {
  Logger.log("info", {
    message: "feedbackController:getSupportTicketMessage:init",
  });
  try {
    const { user } = req;
    const { support_ticket_id } = req.params;
    const supportTicketRecordMessages =
      await TicketingService.retrieveSupportTicketMessage({
        userID: parseInt(user.user_id),
        ticketID: parseInt(support_ticket_id),
      });
    if (supportTicketRecordMessages?.messages) {
      Logger.log("success", {
        message: "feedbackController:getSupportTicketMessage:success",
      });
      return res.json({
        success: true,
        supportTicketRecordMessages: supportTicketRecordMessages.messages,
      });
    }
    Logger.log("success", {
      message: "feedbackController:getSupportTicketMessage:catch-2",
      params: "No messages",
    });
    return res.json({
      success: false,
    });
  } catch (error) {
    Logger.log("error", {
      message: "feedbackController:getSupportTicketMessage:catch-1",
      params: { error },
    });
    return res.json({ success: false, error: extractError(error) });
  }
};

/**
 *
 * @param {import("express").Request} req
 * @param {import("express").Response} res
 * @returns
 */
feedbackController.postSupportTicketMessage = async (req, res) => {
  Logger.log("info", {
    message: "feedbackController:portSupportTicketMessage:init",
  });
  try {
    const { user } = req;
    const { support_ticket_id } = req.params;
    const { text, ts } = req.body;
    const supportTicketRecordMessages =
      await TicketingService.postMessageToSupportTicket({
        userID: parseInt(user.user_id),
        ticketID: parseInt(support_ticket_id),
        message: text,
        ts,
      });
    Logger.log("success", {
      message: "feedbackController:portSupportTicketMessage:success",
    });
    return res.json({ success: true, supportTicketRecordMessages });
  } catch (error) {
    Logger.log("error", {
      message: "feedbackController:portSupportTicketMessage:catch-1",
      params: { error },
    });
    return res.json({ success: false, error: extractError(error) });
  }
};

module.exports = feedbackController;
