const constants = require("../../constants");
const Logger = require("../../utils/logger");
const moment = require("moment");

class BookingLogService {
  constructor() {}

  /**
   *
   * @param {object} param0
   * @param {Array<object>} param0.intervals
   * @param {Number|import("moment").Moment|Date} param0.startTime
   * @param {Number|import("moment").Moment|Date} param0.endTime
   * @returns
   */
  static calculateTimeFromTimelineWithStartAndEndTime = ({
    intervals,
    startTime,
    endTime,
  }) => {
    let totalDuration = moment.duration(0);

    // Convert start and end time strings to moment objects
    const startMoment = moment(startTime);
    const endMoment = moment(endTime);

    // Iterate through each interval
    intervals.forEach((interval) => {
      // Split the interval into start and end times
      const { startTime: intervalStart, endTime: intervalEnd } = interval;

      // Convert interval start and end time strings to moment objects
      const intervalStartMoment = moment(intervalStart);
      const intervalEndMoment = moment(intervalEnd);

      // Check if the interval fully contains the specified range
      if (
        startMoment.isBetween(intervalStartMoment, intervalEndMoment) &&
        endMoment.isBetween(intervalStartMoment, intervalEndMoment)
      ) {
        totalDuration.add(endMoment.diff(startMoment));
      } else if (
        intervalStartMoment.isSameOrAfter(startMoment) &&
        intervalEndMoment.isSameOrBefore(endMoment)
      ) {
        // Interval fully within the specified range
        totalDuration.add(intervalEndMoment.diff(intervalStartMoment));
      } else if (
        intervalStartMoment.isBefore(startMoment) &&
        intervalEndMoment.isAfter(startMoment)
      ) {
        // Interval overlaps with start time
        totalDuration.add(intervalEndMoment.diff(startMoment));
      } else if (
        intervalStartMoment.isBefore(endMoment) &&
        intervalEndMoment.isAfter(endMoment)
      ) {
        // Interval overlaps with end time
        totalDuration.add(endMoment.diff(intervalStartMoment));
      }
    });

    return totalDuration;
  };

  /**
   *
   * @param {object} param0
   * @param {Array<import("@prisma/client").tbl_booking_logs>} param0.bookingLogs
   */
  static convertBookingLogsToTimelines = ({ bookingLogs }) => {
    // calculate timeline of pause and ride from the logs
    const _rideTimeline = [];
    const _pauseTimeline = [];

    for (let i = 0; i < bookingLogs.length; i++) {
      switch (bookingLogs[i].booking_action) {
        case constants.BOOKING_ACTIONS.START: {
          
          _rideTimeline.push({
            startTime: moment(bookingLogs[i].created_at),
            endTime: null,
          });
          
          break;
        }
        case constants.BOOKING_ACTIONS.PAUSE: {
          const _latestRideTimelineIndex = _rideTimeline.length - 1;
          
          _rideTimeline[_latestRideTimelineIndex].endTime = moment(
            bookingLogs[i].created_at
          );
          _pauseTimeline.push({
            startTime: moment(bookingLogs[i].created_at),
            endTime: null,
          });
          
          break;
        }
        case constants.BOOKING_ACTIONS.RESUME: {
          const _latestPauseTimelineIndex = _pauseTimeline.length - 1;
          
          _pauseTimeline[_latestPauseTimelineIndex].endTime = moment(
            bookingLogs[i].created_at
          );
          _rideTimeline.push({
            startTime: moment(bookingLogs[i].created_at),
            endTime: null,
          });
          
          break;
        }
        case constants.BOOKING_ACTIONS.END: {
          if (
            bookingLogs[i - 1].booking_action ===
            constants.BOOKING_ACTIONS.PAUSE
          ) {
            
            const _latestPauseTimelineIndex = _pauseTimeline.length - 1;
            
            _pauseTimeline[_latestPauseTimelineIndex].endTime = moment(
              bookingLogs[i].created_at
            );
            
          } else {
            const _latestRideTimelineIndex = _rideTimeline.length - 1;
            
            _rideTimeline[_latestRideTimelineIndex].endTime = moment(
              bookingLogs[i].created_at
            );
            
          }
          break;
        }
        default: {
          break;
        }
      }
    }
    return { rideTimeline: _rideTimeline, pauseTimeline: _pauseTimeline };
  };

  /**
   *
   * @param {object} param0
   * @param {Array<import("@prisma/client").tbl_booking_logs>} param0.bookingLogs
   * @param {Number|import("moment").Moment|Date} param0.consideredStartTime
   * @param {Number|import("moment").Moment|Date} param0.consideredEndTime
   */
  static calculateBookingRideAndPauseTimeFromStartAndEndTime = async ({
    bookingLogs,
    consideredStartTime,
    consideredEndTime,
  }) => {
    try {
      Logger.log("info", {
        message:
          "BookingLogService:calculateBookingRideAndPauseTimeFromStartAndEndTime:params",
        params: {
          bookingLogsLength: bookingLogs.length,
          consideredStartTime,
          consideredEndTime,
        },
      });
      // declare moment type conversions of consideredStartTime and consideredEndTime
      let _consideredStartTime = moment(consideredStartTime);
      let _consideredEndTime = moment(consideredEndTime);

      const { pauseTimeline, rideTimeline } =
        this.convertBookingLogsToTimelines({ bookingLogs });

      Logger.log("info", {
        message:
          "BookingLogService:calculateBookingRideAndPauseTimeFromStartAndEndTime:timelines",
        params: {
          bookingLogsLength: bookingLogs.length,
          consideredStartTime,
          consideredEndTime,
          pauseTimeline: pauseTimeline.length,
          rideTimeline: rideTimeline.length,
        },
      });

      const _bookingRideTime =
        this.calculateTimeFromTimelineWithStartAndEndTime({
          intervals: rideTimeline,
          startTime: _consideredStartTime,
          endTime: _consideredEndTime,
        }).asMilliseconds();
      const _bookingPauseTime =
        this.calculateTimeFromTimelineWithStartAndEndTime({
          intervals: pauseTimeline,
          startTime: _consideredStartTime,
          endTime: _consideredEndTime,
        }).asMilliseconds();

      Logger.log("info", {
        message:
          "BookingLogService:calculateBookingRideAndPauseTimeFromStartAndEndTime:timelines",
        params: {
          bookingLogsLength: bookingLogs.length,
          consideredStartTime,
          consideredEndTime,
          pauseTimeline: pauseTimeline.length,
          rideTimeline: rideTimeline.length,
          _bookingPauseTime,
          _bookingRideTime,
        },
      });
      return {
        bookingRideTime: _bookingRideTime,
        bookingPauseTime: _bookingPauseTime,
      };
    } catch (error) {
      Logger.log("error", {
        message:
          "BookingLogService:calculateBookingRideAndPauseTimeFromStartAndEndTime:catch-1",
        params: {
          error,
        },
      });
      throw error;
    }
  };
}

module.exports = { BookingLogService };
