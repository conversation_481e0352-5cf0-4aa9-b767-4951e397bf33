const lodash = require("lodash");
const constants = require("../../constants");
const calculations = require("../../utils/calculations");
const moment = require("moment");
const Logger = require("../../utils/logger");

class BookingReceipt {
  constructor({
    booking_receipt_id,

    initial_ride_time,
    initial_pause_time,
    initial_booking_time,

    total_booking_time_after_plan,
    total_ride_time_after_plan,
    total_pause_time_after_plan,

    total_booking_time_after_subscription,
    total_ride_time_after_subscription,
    total_pause_time_after_subscription,

    tax,
    tax_rate,
    unlock_charge,
    pause_time_charge,
    ride_time_charge,
    outstation_charge,
    other_charge,
    minimum_charge_addition,
    created_at,
    updated_at,
    final_booking_fare,
    booking_start_at,
    booking_end_at,
    surge_charge,
    coupon_id,
    plan_id,
    subscription_id,
    plan,
    subscription,
    coupon,
    initial_ride_time_fare,
    initial_pause_time_fare,
    final_ride_time_fare,
    final_pause_time_fare,
    initial_unlock_fare,
    final_unlock_fare,
    inter_wallet_transaction_id,
    vehicle_maintenance_charge_percentage,
    payment_gateway_charge_percentage,
    vehicle_maintenance_charge,
    payment_gateway_charge,
  }) {
    this.booking_receipt_id = booking_receipt_id;
    this.vehicle_maintenance_charge_percentage =
      constants.DEFAULT_VEHICLE_MAINTENANCE_CHARGE_PERCENTAGE;
    this.payment_gateway_charge_percentage =
      constants.DEFAULT_PAYMENT_GATEWAY_CHARGE_PERCENTAGE;

    this.total_booking_time_after_plan = total_booking_time_after_plan; // in microseconds
    this.total_ride_time_after_plan = total_ride_time_after_plan; // in microseconds
    this.total_pause_time_after_plan = total_pause_time_after_plan; // in microseconds

    this.total_booking_time_after_subscription =
      total_booking_time_after_subscription; // in microseconds
    this.total_ride_time_after_subscription =
      total_ride_time_after_subscription; // in microseconds
    this.total_pause_time_after_subscription =
      total_pause_time_after_subscription; // in microseconds

    this.initial_booking_time = initial_booking_time; // in microseconds
    this.initial_ride_time = initial_ride_time; // in microseconds
    this.initial_pause_time = initial_pause_time; // in microseconds

    this.tax = tax;
    this.tax_rate = tax_rate;

    this.unlock_charge = unlock_charge;
    this.pause_time_charge = pause_time_charge;
    this.ride_time_charge = ride_time_charge;

    this.initial_unlock_fare = initial_unlock_fare;
    this.initial_pause_time_fare = initial_pause_time_fare;
    this.initial_ride_time_fare = initial_ride_time_fare;

    this.final_unlock_fare = final_unlock_fare;
    this.final_pause_time_fare = final_pause_time_fare;
    this.final_ride_time_fare = final_ride_time_fare;

    this.outstation_charge = outstation_charge ? outstation_charge : 0;
    this.other_charge = other_charge ? other_charge : 0;
    this.minimum_charge_addition = minimum_charge_addition
      ? minimum_charge_addition
      : 0;
    this.surge_charge = surge_charge ? surge_charge : 0;

    this.final_booking_fare = final_booking_fare;

    this.booking_start_at = booking_start_at;
    this.booking_end_at = booking_end_at;

    this.created_at = created_at;
    this.updated_at = updated_at;

    this.coupon_id = coupon_id;
    this.subscription_id = subscription_id;
    this.plan_id = plan_id;

    this.coupon = coupon;
    this.subscription = subscription;
    this.plan = plan;

    this.inter_wallet_transaction_id = inter_wallet_transaction_id;
    this.vehicle_maintenance_charge = 0;
    this.payment_gateway_charge = 0;
    this.minimum_charge =
        constants.MINIMUM_BASE_BOOKING_PRICE;
  }

  initialCalculation = () => {
    this.initial_unlock_fare = this.unlock_charge;
    this.initial_pause_time_fare = Math.floor(
      moment.duration(this.initial_pause_time, "milliseconds").asMinutes() *
        this.pause_time_charge
    );
    this.initial_ride_time_fare = Math.floor(
      moment.duration(this.initial_ride_time, "milliseconds").asMinutes() *
        this.ride_time_charge
    );
  };

  initialCalculationAfterPlanApplied = () => {
    this.initial_unlock_fare = this.unlock_charge;
    this.initial_pause_time_fare = Math.floor(
      moment
        .duration(this.total_pause_time_after_plan, "milliseconds")
        .asMinutes() * this.pause_time_charge
    );
    this.initial_ride_time_fare = Math.floor(
      moment
        .duration(this.total_ride_time_after_plan, "milliseconds")
        .asMinutes() * this.ride_time_charge
    );
  };

  initialCalculationAfterSubscriptionApplied = () => {
    this.initial_unlock_fare = this.unlock_charge;
    this.initial_pause_time_fare = Math.floor(
      moment
        .duration(this.total_pause_time_after_subscription, "milliseconds")
        .asMinutes() * this.pause_time_charge
    );
    this.initial_ride_time_fare = Math.floor(
      moment
        .duration(this.total_ride_time_after_subscription, "milliseconds")
        .asMinutes() * this.ride_time_charge
    );
  };

  /**
   *
   * @param {object} param0
   * @param {BookingReceipt} param0.bookingReceipt
   */
  static finalCalculate = ({ bookingReceipt }) => {
    const processedBookingReceipt = lodash.cloneDeep(bookingReceipt);
    processedBookingReceipt.final_unlock_fare =
      processedBookingReceipt.unlock_charge;
    processedBookingReceipt.final_pause_time_fare = Math.floor(
      moment
        .duration(
          processedBookingReceipt.total_pause_time_after_subscription,
          "milliseconds"
        )
        .asMinutes() * processedBookingReceipt.pause_time_charge
    );
    processedBookingReceipt.final_ride_time_fare = Math.floor(
      moment
        .duration(
          processedBookingReceipt.total_ride_time_after_subscription,
          "milliseconds"
        )
        .asMinutes() * processedBookingReceipt.ride_time_charge
    );

    const fareExcludingTaxes =
      parseInt(
        processedBookingReceipt.final_ride_time_fare
          ? processedBookingReceipt.final_ride_time_fare
          : 0
      ) +
      parseInt(
        processedBookingReceipt.final_pause_time_fare
          ? processedBookingReceipt.final_pause_time_fare
          : 0
      ) +
      parseInt(
        processedBookingReceipt.final_unlock_fare
          ? processedBookingReceipt.final_unlock_fare
          : 0
      ) +
      parseInt(
        processedBookingReceipt.outstation_charge
          ? processedBookingReceipt.outstation_charge
          : 0
      ) +
      parseInt(
        processedBookingReceipt.other_charge
          ? processedBookingReceipt.other_charge
          : 0
      ) +
      parseInt(
        processedBookingReceipt.surge_charge
          ? processedBookingReceipt.surge_charge
          : 0
      );
    Logger.log("info", {
      message: "BookingReceipt:fareExcludingTaxes",
      params: {
        fareExcludingTaxes,
      },
    });

    // calculate tax (on fareExcludingTaxes),
    // vehicle maintenance charge (on fareExcludingTaxes),
    // payment gateway charge (on finalBookingFareWithTaxes),
    // minimum charge addition

    // booking charges with taxes included
    processedBookingReceipt.tax = Math.floor(
      (fareExcludingTaxes * processedBookingReceipt.tax_rate) / 100
    );

    // booking charges with maintenance taxes included
    processedBookingReceipt.vehicle_maintenance_charge =
      (fareExcludingTaxes *
        processedBookingReceipt.vehicle_maintenance_charge_percentage) /
      100;

    // booking charges with tax and vehicle maintenance charge included
    const finalBookingFareWithTaxes =
      processedBookingReceipt.tax +
      processedBookingReceipt.vehicle_maintenance_charge +
      fareExcludingTaxes;

    // calculate payment gateway charges
    processedBookingReceipt.payment_gateway_charge =
      (finalBookingFareWithTaxes *
        processedBookingReceipt.payment_gateway_charge_percentage) /
      100;

    // minimum charge addition
    if (
      processedBookingReceipt.minimum_charge > finalBookingFareWithTaxes &&
      !processedBookingReceipt.plan &&
      !processedBookingReceipt.subscription
    ) {
      processedBookingReceipt.minimum_charge_addition =
        processedBookingReceipt.minimum_charge - finalBookingFareWithTaxes;
      processedBookingReceipt.final_booking_fare =
        processedBookingReceipt.minimum_charge;
    } else {
      // final booking charges with taxes & payment gateway charges included
      processedBookingReceipt.final_booking_fare = Math.floor(
        finalBookingFareWithTaxes +
          processedBookingReceipt.payment_gateway_charge
      );
    }

    Logger.log("info", {
      message: "BookingReceipt:finalCalculate",
      params: {
        final_pause_time_fare: processedBookingReceipt.final_pause_time_fare,
        final_ride_time_fare: processedBookingReceipt.final_ride_time_fare,
        final_unlock_fare: processedBookingReceipt.final_unlock_fare,
        final_booking_fare: processedBookingReceipt.final_booking_fare,
      },
    });

    return processedBookingReceipt;
  };
}

module.exports = { BookingReceipt };
