const constants = require("../../constants");
const { extractCurrencySpecificValue } = require("../../utils/currency");
const moment = require("moment");
const HTMLToPDF = require("convert-html-to-pdf").default;
/**
 *
 * @param {object} param0
 * @param {import("@prisma/client").tbl_payment_orders & {tbl_users:import("@prisma/client").tbl_users} & {payment_purpose_receipt:import("@prisma/client").tbl_wallet_recharge_transactions| import("@prisma/client").tbl_security_deposit_transactions | import("@prisma/client").tbl_plan_purchase_receipts | import("@prisma/client").tbl_subscription_purchase_receipts}} param0.paymentOrder
 * @returns
 */
const generateOptionsFromPaymentOrder = ({ paymentOrder }) => {
  return {
    logo: "https://firebasestorage.googleapis.com/v0/b/hover-mobility-auter.appspot.com/o/logo%2Finvoice_logo.png?alt=media&token=7a3ac387-5987-4cdc-b6f6-080fce4e4e8c",
    name: "Hover Mobility",
    address1: "55, Meherbaba nagar Shilpakar Buddha Vihar",
    address2: "Nagpur 440034",
    orderId: paymentOrder.payment_order_id,
    customerName: `${paymentOrder.tbl_users.first_name} ${paymentOrder.tbl_users.last_name}`,
    customerAddress1: `${paymentOrder.tbl_users.address1}`,
    customerAddress2: `${paymentOrder.tbl_users.address2}`,
    date: moment(paymentOrder.created_at).format("DD MMM YYYY, h:mm:ss a"),
    paymentTerms:
      constants.PAYMENT_PURPOSE_DETAILS[paymentOrder.payment_purpose].title,
    items: [
      {
        name: paymentOrder.payment_purpose,
        qty:
          paymentOrder.payment_purpose ==
          constants.PAYMENT_PURPOSE.WALLET_RECHARGE
            ? parseInt(paymentOrder.amount) / 100
            : 1,
        rate:
          paymentOrder.payment_purpose ==
          constants.PAYMENT_PURPOSE.WALLET_RECHARGE
            ? extractCurrencySpecificValue(100 / 1.18)
            : extractCurrencySpecificValue(paymentOrder.amount / 1.18),
        amount: extractCurrencySpecificValue(paymentOrder.amount / 1.18),
        tax: extractCurrencySpecificValue(
          paymentOrder.amount - paymentOrder.amount / 1.18
        ),
        total: extractCurrencySpecificValue(paymentOrder.amount),
      },
    ],
    total: extractCurrencySpecificValue(paymentOrder.amount),
    balanceDue: extractCurrencySpecificValue(0),
    notes: "Thanks for being an awesome customer!",
    terms:
      "This invoice is auto generated. If there is any issue, please contact on support",
  };
};
function getDeliveryItemsHTML(items) {
  let data = "";
  for (let item of items) {
    data += `
    <div class="table-row">
        <div class="table-cell text-left font-bold py-1 px-4">${item.name}</div>
        <div class="table-cell text-center">${item.qty}</div>
        <div class="table-cell text-center">${item.rate}</div>
        <div class="table-cell text-center">${item.tax}</div>
        <div class="table-cell text-center">${item.amount}</div>
      </div>
    `;
  }
  return data;
}

function getDeliveryHTML(options) {
  return `


<!DOCTYPE html>
  <html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Invoice</title>
    <style>::after,::before{box-sizing:border-box;border-width:0;border-style:solid;border-color:#e5e7eb}::after,::before{--tw-content:''}:host,html{line-height:1.5;-webkit-text-size-adjust:100%;-moz-tab-size:4;tab-size:4;font-family:ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";font-feature-settings:normal;font-variation-settings:normal;-webkit-tap-highlight-color:transparent}body{margin:0;line-height:inherit}hr{height:0;color:inherit;border-top-width:1px}abbr:where([title]){-webkit-text-decoration:underline dotted;text-decoration:underline dotted}h1,h2,h3,h4,h5,h6{font-size:inherit;font-weight:inherit}a{color:inherit;text-decoration:inherit}b,strong{font-weight:bolder}code,kbd,pre,samp{font-family:ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;font-feature-settings:normal;font-variation-settings:normal;font-size:1em}small{font-size:80%}sub,sup{font-size:75%;line-height:0;position:relative;vertical-align:baseline}sub{bottom:-.25em}sup{top:-.5em}table{text-indent:0;border-color:inherit;border-collapse:collapse}button,input,optgroup,select,textarea{font-family:inherit;font-feature-settings:inherit;font-variation-settings:inherit;font-size:100%;font-weight:inherit;line-height:inherit;letter-spacing:inherit;color:inherit;margin:0;padding:0}button,select{text-transform:none}button,input:where([type=button]),input:where([type=reset]),input:where([type=submit]){-webkit-appearance:button;background-color:transparent;background-image:none}:-moz-focusring{outline:auto}:-moz-ui-invalid{box-shadow:none}progress{vertical-align:baseline}::-webkit-inner-spin-button,::-webkit-outer-spin-button{height:auto}[type=search]{-webkit-appearance:textfield;outline-offset:-2px}::-webkit-search-decoration{-webkit-appearance:none}::-webkit-file-upload-button{-webkit-appearance:button;font:inherit}summary{display:list-item}blockquote,dd,dl,figure,h1,h2,h3,h4,h5,h6,hr,p,pre{margin:0}fieldset{margin:0;padding:0}legend{padding:0}menu,ol,ul{list-style:none;margin:0;padding:0}dialog{padding:0}textarea{resize:vertical}input::placeholder,textarea::placeholder{opacity:1;color:#9ca3af}[role=button],button{cursor:pointer}:disabled{cursor:default}audio,canvas,embed,iframe,img,object,svg,video{display:block;vertical-align:middle}img,video{max-width:100%;height:auto}[hidden]{display:none}*, ::before, ::after{--tw-border-spacing-x:0;--tw-border-spacing-y:0;--tw-translate-x:0;--tw-translate-y:0;--tw-rotate:0;--tw-skew-x:0;--tw-skew-y:0;--tw-scale-x:1;--tw-scale-y:1;--tw-pan-x: ;--tw-pan-y: ;--tw-pinch-zoom: ;--tw-scroll-snap-strictness:proximity;--tw-gradient-from-position: ;--tw-gradient-via-position: ;--tw-gradient-to-position: ;--tw-ordinal: ;--tw-slashed-zero: ;--tw-numeric-figure: ;--tw-numeric-spacing: ;--tw-numeric-fraction: ;--tw-ring-inset: ;--tw-ring-offset-width:0px;--tw-ring-offset-color:#fff;--tw-ring-color:rgb(59 130 246 / 0.5);--tw-ring-offset-shadow:0 0 #0000;--tw-ring-shadow:0 0 #0000;--tw-shadow:0 0 #0000;--tw-shadow-colored:0 0 #0000;--tw-blur: ;--tw-brightness: ;--tw-contrast: ;--tw-grayscale: ;--tw-hue-rotate: ;--tw-invert: ;--tw-saturate: ;--tw-sepia: ;--tw-drop-shadow: ;--tw-backdrop-blur: ;--tw-backdrop-brightness: ;--tw-backdrop-contrast: ;--tw-backdrop-grayscale: ;--tw-backdrop-hue-rotate: ;--tw-backdrop-invert: ;--tw-backdrop-opacity: ;--tw-backdrop-saturate: ;--tw-backdrop-sepia: ;--tw-contain-size: ;--tw-contain-layout: ;--tw-contain-paint: ;--tw-contain-style: }::backdrop{--tw-border-spacing-x:0;--tw-border-spacing-y:0;--tw-translate-x:0;--tw-translate-y:0;--tw-rotate:0;--tw-skew-x:0;--tw-skew-y:0;--tw-scale-x:1;--tw-scale-y:1;--tw-pan-x: ;--tw-pan-y: ;--tw-pinch-zoom: ;--tw-scroll-snap-strictness:proximity;--tw-gradient-from-position: ;--tw-gradient-via-position: ;--tw-gradient-to-position: ;--tw-ordinal: ;--tw-slashed-zero: ;--tw-numeric-figure: ;--tw-numeric-spacing: ;--tw-numeric-fraction: ;--tw-ring-inset: ;--tw-ring-offset-width:0px;--tw-ring-offset-color:#fff;--tw-ring-color:rgb(59 130 246 / 0.5);--tw-ring-offset-shadow:0 0 #0000;--tw-ring-shadow:0 0 #0000;--tw-shadow:0 0 #0000;--tw-shadow-colored:0 0 #0000;--tw-blur: ;--tw-brightness: ;--tw-contrast: ;--tw-grayscale: ;--tw-hue-rotate: ;--tw-invert: ;--tw-saturate: ;--tw-sepia: ;--tw-drop-shadow: ;--tw-backdrop-blur: ;--tw-backdrop-brightness: ;--tw-backdrop-contrast: ;--tw-backdrop-grayscale: ;--tw-backdrop-hue-rotate: ;--tw-backdrop-invert: ;--tw-backdrop-opacity: ;--tw-backdrop-saturate: ;--tw-backdrop-sepia: ;--tw-contain-size: ;--tw-contain-layout: ;--tw-contain-paint: ;--tw-contain-style: }.container{width:100%}@media (min-width: 640px){.container{max-width:640px}}@media (min-width: 768px){.container{max-width:768px}}@media (min-width: 1024px){.container{max-width:1024px}}@media (min-width: 1280px){.container{max-width:1280px}}@media (min-width: 1536px){.container{max-width:1536px}}.mx-auto{margin-left:auto;margin-right:auto}.flex{display:flex}.table{display:table}.table-cell{display:table-cell}.table-header-group{display:table-header-group}.table-row-group{display:table-row-group}.table-row{display:table-row}.w-40{width:10rem}.w-full{width:100%}.flex-col{flex-direction:column}.items-start{align-items:flex-start}.items-end{align-items:flex-end}.rounded-l-lg{border-top-left-radius:0.5rem;border-bottom-left-radius:0.5rem}.rounded-r-lg{border-top-right-radius:0.5rem;border-bottom-right-radius:0.5rem}.border-x-\[1px\]{border-left-width:1px;border-right-width:1px}.bg-gray-700{--tw-bg-opacity:1;background-color:rgb(55 65 81 / var(--tw-bg-opacity))}.\!p-4{padding:1rem !important}.p-3{padding:0.75rem}.p-4{padding:1rem}.px-4{padding-left:1rem;padding-right:1rem}.py-1{padding-top:0.25rem;padding-bottom:0.25rem}.py-2{padding-top:0.5rem;padding-bottom:0.5rem}.py-4{padding-top:1rem;padding-bottom:1rem}.pb-1{padding-bottom:0.25rem}.pb-2{padding-bottom:0.5rem}.pb-6{padding-bottom:1.5rem}.pb-8{padding-bottom:2rem}.pl-0{padding-left:0px}.pl-12{padding-left:3rem}.pl-4{padding-left:1rem}.pt-10{padding-top:2.5rem}.text-left{text-align:left}.text-center{text-align:center}.text-right{text-align:right}.text-3xl{font-size:1.875rem;line-height:2.25rem}.text-lg{font-size:1.125rem;line-height:1.75rem}.font-bold{font-weight:700}.font-normal{font-weight:400}.text-black{--tw-text-opacity:1;color:rgb(0 0 0 / var(--tw-text-opacity))}.text-gray-400{--tw-text-opacity:1;color:rgb(156 163 175 / var(--tw-text-opacity))}.text-gray-500{--tw-text-opacity:1;color:rgb(107 114 128 / var(--tw-text-opacity))}.text-white{--tw-text-opacity:1;color:rgb(255 255 255 / var(--tw-text-opacity))}@media (min-width: 768px){.md\:w-60{width:15rem}.md\:w-\[12rem\]{width:12rem}.md\:flex-1{flex:1 1 0%}.md\:flex-row{flex-direction:row}.md\:items-end{align-items:flex-end}.md\:justify-between{justify-content:space-between}.md\:p-10{padding:2.5rem}.md\:py-6{padding-top:1.5rem;padding-bottom:1.5rem}.md\:pb-16{padding-bottom:4rem}.md\:pl-10{padding-left:2.5rem}.md\:pl-24{padding-left:6rem}.md\:pl-4{padding-left:1rem}.md\:pt-20{padding-top:5rem}.md\:text-4xl{font-size:2.25rem;line-height:2.5rem}.md\:text-xl{font-size:1.25rem;line-height:1.75rem}}</style>
    
</head>
<body class="p-4 md:p-10 !p-4">
    <div class="container mx-auto">
        <!--Logo and Other info-->
        <div class="flex flex-col md:flex-row items-start md:justify-between">
            <div class="md:flex-1">
                <div class="w-full md:w-60 pb-6">
                    <img class="w-40" src="${options.logo}" alt="Logo">
                </div>
                
                <div class="w-full md:w-60 pl-0 md:pl-4 pb-6">
                    <h3 class="font-bold">${options.name}</h3>
                    <p>${options.address1}</p>
                    <p>${options.address2}</p>
                    <p class="font-bold">GSTIN :27AATCA3598D1Z8</p>
                </div>
                
                <div class="pl-0 md:pl-4 pb-6">
                    <p class="text-gray-500">Bill To:</p>
                    <h3 class="font-bold">${options.customerName}</h3>
                    <p>${options.customerAddress1}</p>
                </div>
            </div>
            <div class="flex items-start md:items-end flex-col">
                <div class="pb-8 md:pb-16">
                    <h1 class=" font-normal text-3xl md:text-4xl pb-1">Invoice</h1>
                    <p class="text-right text-gray-500 text-lg md:text-xl"># ${
                      options.orderId
                    }</p>
                </div>
                <div class="flex">
                    <div class="flex flex-col items-start md:items-end">
                        <p class="text-gray-500 py-1">Date:</p>
                        <p class="text-gray-500 py-1">Payment Terms:</p>
                        <p class="font-bold text-lg md:text-xl py-1 pb-2 ">Balance Due:</p>
                    </div>
                    <div class="flex flex-col items-end w-full md:w-[12rem] text-right">
                        <p class="py-1">${options.date}</p>
                        <p class="py-1 pl-4 md:pl-10">${
                          options.paymentTerms
                        }</p>
                        <div class="pb-2 py-1">
                            <p class="font-bold text-lg md:text-xl">${
                              options.balanceDue
                            }</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!--Items List-->
        <div class="table w-full">
            <div class="table-header-group bg-gray-700 text-white">
                <div class="table-row">
                    <div class="table-cell text-left py-2 px-4 rounded-l-lg border-x-[1px]">Item</div>
                    <div class="table-cell text-center border-x-[1px]">Quantity</div>
                    <div class="table-cell text-center border-x-[1px]">Rate</div>
                    <div class="table-cell text-center border-x-[1px]">Tax</div>
                    <div class="table-cell text-center border-x-[1px] rounded-r-lg">Amount</div>
                </div>
            </div>
            <div class="table-row-group">
                ${getDeliveryItemsHTML(options.items)}
            </div>
        </div>
        
        <!--Total Amount-->
        <div class="pt-10 md:pt-20 text-left">
            <p class="text-gray-400">Total: <span class="pl-12 md:pl-24 text-black">${
              options.total
            }</span></p>
        </div>

        <!--Notes and Other info-->
        <div class="py-4 md:py-6">
            <p class="text-gray-400 pb-2">Notes:</p>
            <p>${options.notes}</p>
        </div>
        <div class="">
            <p class="text-gray-400 pb-2">Terms:</p>
            <p>${options.terms}</p>
        </div>
    </div>
</body>
</html>
    `;
}

/**
 *
 * @param {object} param0
 * @param {import("@prisma/client").tbl_payment_orders & {tbl_users:import("@prisma/client").tbl_users} & {payment_purpose_receipt:import("@prisma/client").tbl_wallet_recharge_transactions| import("@prisma/client").tbl_security_deposit_transactions | import("@prisma/client").tbl_plan_purchase_receipts | import("@prisma/client").tbl_subscription_purchase_receipts}} param0.paymentOrder
 * @returns
 */
async function getInvoice({ paymentOrder }) {
  return new Promise(async (resolve, reject) => {
    try {
      const options = generateOptionsFromPaymentOrder({ paymentOrder });
      const html = getDeliveryHTML(options);
      // const htmlToPDF = new HTMLToPDF(html);

      // const pdf = await htmlToPDF.convert({
      //   waitForNetworkIdle: true,
      //   browserOptions: { defaultViewport: { width: 1920, height: 1080 } },
      //   pdfOptions: { height: 1200, width: 900, timeout: 0 },
      // });
      resolve(html);
    } catch (err) {
      reject(err);
    }
  });
}

module.exports = {
  getInvoice,
};
