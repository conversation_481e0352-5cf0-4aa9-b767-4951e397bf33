const { prisma } = require("../../config/prisma");
const constants = require("../../constants");
const Logger = require("../../utils/logger");
const walletSocketController = require("../socket/controllers/wallet.socket.controller");
const { RazorpayService } = require("./razorpay.service");
// const { RazorpayService } = require("./razorpay.service");

class WalletService {
  constructor() {}

  /**
   *
   * @param {import("@prisma/client").tbl_users} user
   * @returns
   */
  static activateWallet = async (user) => {
    try {
      Logger.log("info", {
        message: "WalletService:activateWallet:params",
        params: { userID: user.user_id },
      });

      if (user.wallet_id && user.tbl_wallets && user.tbl_wallets.is_disabled) {
        Logger.log("error", {
          message: "WalletService:activateWallet:catch-2",
          params: { error: constants.ERROR_CODES.WALLET_DISABLED_BY_ADMIN },
        });
        return null;
      }
      const razorpayCustomer =
        await RazorpayService.createRazorpayCustomerAccount({
          firebaseID: user.firebase_id,
          phoneNumber: user.phone_number,
          userID: user.user_id,
        });
      let updatedUser;
      if (user.wallet_id) {
        updatedUser = await prisma.tbl_users.update({
          where: { user_id: parseInt(user.user_id) },
          data: {
            razorpay_customer_id: razorpayCustomer.id,
          },
          include: { tbl_wallets: true, tbl_user_types: true },
        });
        Logger.log("info", {
          message: "WalletService:activateWallet:updatedUser:wallet not added",
          params: { updatedUser },
        });
      } else {
        updatedUser = await prisma.tbl_users.update({
          where: { user_id: parseInt(user.user_id) },
          data: {
            razorpay_customer_id: razorpayCustomer.id,
            tbl_wallets: { create: { balance: 0 } },
          },
          include: { tbl_wallets: true, tbl_user_types: true },
        });
        Logger.log("info", {
          message: "WalletService:activateWallet:updatedUser:wallet added",
          params: { updatedUser },
        });
      }
      
      walletSocketController.emitWalletOnUpdate({
        wallet: updatedUser.tbl_wallets,
        firebaseID: user.firebase_id,
      });
      return updatedUser;
    } catch (error) {
      Logger.log("error", {
        message: "WalletService:activateWallet:catch-1",
        params: { error },
      });
      return null;
    }
  };

  /**
   *
   * @param {object} param0
   * @param {import("@prisma/client").tbl_users & {tbl_wallets:import("@prisma/client").tbl_wallets} & {tbl_user_types:import("@prisma/client").tbl_user_types & {tbl_security_deposit_types:import("@prisma/client").tbl_security_deposit_types}}} param0.user
   */
  static createRequiredSecurityDepositEntry = async ({ user }) => {
    try {
      Logger.log("info", {
        message: "WalletService:createRequiredSecurityDepositEntry:params",
        params: {
          userID: user.user_id,
          securityDepositType: user.tbl_user_types.tbl_security_deposit_types,
        },
      });

      const remainingSecurityDeposit =
        user.tbl_user_types.tbl_security_deposit_types.amount -
        user.tbl_wallets.security_deposit;

      Logger.log("info", {
        message:
          "WalletService:createRequiredSecurityDepositEntry:remainingSecurityDeposit",
        params: { remainingSecurityDeposit },
      });
      const newSecurityDeposit =
        await prisma.tbl_security_deposit_transactions.create({
          data: {
            security_deposit_transaction_status:
              constants.SECURITY_DEPOSIT_STATUS.PENDING,
            wallet_id: user.wallet_id,
          },
        });
      Logger.log("success", {
        message:
          "WalletService:createRequiredSecurityDepositEntry:newSecurityDeposit",
        params: { newSecurityDeposit },
      });
      return {
        securityDepositID: newSecurityDeposit.security_deposit_transaction_id,
        amount: remainingSecurityDeposit,
      };
    } catch (error) {
      Logger.log("error", {
        message: "WalletService:createRequiredSecurityDepositEntry:catch-1",
        params: { error },
      });
      throw error;
    }
  };

  /**
   * @param {object} param0
   * @param {import("@prisma/client").tbl_users & {tbl_wallets:import("@prisma/client").tbl_wallets}} param0.user
   * @param {Number} param0.amount
   * @param {Number} param0.subscriptionPurchaseReceiptID
   * @param {Boolean} param0.partial
   * @returns
   */
  static paySubscriptionPurchasePaymentFromWallet = async ({
    user,
    amount,
    subscriptionPurchaseReceiptID,
    partial,
  }) => {
    try {
      Logger.log("info", {
        message: "WalletService:paySubscriptionPaymentFromWallet:params",
        params: {
          userID: user.user_id,
          amount,
          subscriptionPurchaseReceiptID,
          partial,
        },
      });
      if (parseInt(user.tbl_wallets.balance) < amount) {
        Logger.log("error", {
          message: "WalletService:paySubscriptionPaymentFromWallet:catch-2",
          params: { error: constants.ERROR_CODES.INSUFFICIENT_BALANCE },
        });
        throw constants.ERROR_CODES.INSUFFICIENT_BALANCE;
      } else {
        const interWalletTransaction = await prisma.$transaction([
          prisma.tbl_inter_wallet_transactions.create({
            data: {
              inter_wallet_transaction_message: String(
                `${constants.PAYMENT_PURPOSE.SUBSCRIPTION_PURCHASE}-${subscriptionPurchaseReceiptID}`
              ),
              inter_wallet_transaction_status: partial
                ? constants.INTER_WALLET_TRANSACTION_STATUS.PENDING
                : constants.INTER_WALLET_TRANSACTION_STATUS.SUCCESS,
              sender_wallet_id: parseInt(user.wallet_id),
              receiver_wallet_id: constants.HOVER_WALLET_ID,
              amount: parseInt(amount),
            },
          }),

          prisma.tbl_wallets.update({
            where: {
              wallet_id: parseInt(user.wallet_id),
            },
            data: {
              balance: {
                decrement: parseInt(amount),
              },
            },
          }),
          prisma.tbl_wallets.update({
            where: {
              wallet_id: constants.HOVER_WALLET_ID,
            },
            data: {
              balance: {
                increment: parseInt(amount),
              },
            },
          }),
          prisma.tbl_subscription_purchase_receipts.update({
            where: {
              subscription_purchase_receipt_id: subscriptionPurchaseReceiptID,
            },
            data: {
              
              subscription_purchase_receipt_status: partial
                ? constants.SUBSCRIPTION_PURCHASE_RECEIPT_STATUS.PENDING
                : constants.SUBSCRIPTION_PURCHASE_RECEIPT_STATUS.SUCCESS,
            },
          }),
        ]);
        Logger.log("success", {
          message:
            "WalletService:paySubscriptionPaymentFromWallet:interWalletTransaction",
          params: { userID: user.user_id, interWalletTransaction },
        });
        // walletSocketController.emitWalletOnUpdate({
        //   wallet: interWalletTransaction[1],
        //   firebaseID: user.firebase_id,
        // });
        return interWalletTransaction;
      }
    } catch (error) {
      Logger.log("error", {
        message: "WalletService:paySubscriptionPaymentFromWallet:catch-1",
        params: { error },
      });
      throw error;
    }
  };

  /**
   * @param {object} param0
   * @param {import("@prisma/client").tbl_users & {tbl_wallets:import("@prisma/client").tbl_wallets}} param0.user
   * @param {Number} param0.amount
   * @param {Number} param0.subscriptionPurchaseReceiptID
   * @param {Boolean} param0.partial
   * @returns
   */
  static payPlanPurchasePaymentFromWallet = async ({
    user,
    amount,
    planPurchaseReceiptID,
    partial,
  }) => {
    try {
      Logger.log("info", {
        message: "WalletService:payPlanPaymentFromWallet:params",
        params: {
          userID: user.user_id,
          amount,
          planPurchaseReceiptID,
          partial,
        },
      });
      if (parseInt(user.tbl_wallets.balance) < amount) {
        Logger.log("error", {
          message: "WalletService:payPlanPaymentFromWallet:catch-2",
          params: { error: constants.ERROR_CODES.INSUFFICIENT_BALANCE },
        });
        throw constants.ERROR_CODES.INSUFFICIENT_BALANCE;
      } else {
        const interWalletTransaction = await prisma.$transaction([
          prisma.tbl_inter_wallet_transactions.create({
            data: {
              inter_wallet_transaction_message: String(
                `${constants.PAYMENT_PURPOSE.PLAN_PURCHASE}-${planPurchaseReceiptID}`
              ),
              inter_wallet_transaction_status: partial
                ? constants.INTER_WALLET_TRANSACTION_STATUS.PENDING
                : constants.INTER_WALLET_TRANSACTION_STATUS.SUCCESS,
              sender_wallet_id: parseInt(user.wallet_id),
              receiver_wallet_id: constants.HOVER_WALLET_ID,
              amount: parseInt(amount),
            },
          }),

          prisma.tbl_wallets.update({
            where: {
              wallet_id: parseInt(user.wallet_id),
            },
            data: {
              balance: {
                decrement: parseInt(amount),
              },
            },
          }),
          prisma.tbl_wallets.update({
            where: {
              wallet_id: constants.HOVER_WALLET_ID,
            },
            data: {
              balance: {
                increment: parseInt(amount),
              },
            },
          }),
          prisma.tbl_plan_purchase_receipts.update({
            where: {
              plan_purchase_receipt_id: planPurchaseReceiptID,
            },
            data: {
              
              plan_purchase_receipt_status: partial
                ? constants.PLAN_PURCHASE_RECEIPT_STATUS.PENDING
                : constants.PLAN_PURCHASE_RECEIPT_STATUS.SUCCESS,
            },
          }),
        ]);
        Logger.log("success", {
          message:
            "WalletService:payPlanPaymentFromWallet:interWalletTransaction",
          params: { userID: user.user_id, interWalletTransaction },
        });
        // walletSocketController.emitWalletOnUpdate({
        //   wallet: interWalletTransaction[1],
        //   firebaseID: user.firebase_id,
        // });
        return interWalletTransaction;
      }
    } catch (error) {
      Logger.log("error", {
        message: "WalletService:payPlanPaymentFromWallet:catch-1",
        params: { error },
      });
      throw error;
    }
  };

  /**
   * @param {object} param0
   * @param {Number} param0.userWalletID
   * @param {Number} param0.interWalletTransactionID
   * @param {Number} param0.subscriptionPurchaseReceiptID
   * @param {Number} param0.planPurchaseReceiptID
   * @returns
   */
  static revertOfferPurchasePaymentFromWallet = async ({
    userWalletID,
    interWalletTransactionID,
    subscriptionPurchaseReceiptID,
    planPurchaseReceiptID,
  }) => {
    try {
      Logger.log("info", {
        message: "WalletService:revertOfferPurchasePaymentFromWallet:params",
        params: {
          userWalletID,
          interWalletTransactionID,
        },
      });
      const interWalletTransaction =
        await prisma.tbl_inter_wallet_transactions.findUnique({
          where: {
            inter_wallet_transaction_id: interWalletTransactionID,
          },
        });

      const interWalletTransactionRevert = await prisma.$transaction(
        async (tx) => {
          await tx.tbl_inter_wallet_transactions.update({
            where: {
              inter_wallet_transaction_id: interWalletTransactionID,
            },
            data: {
              inter_wallet_transaction_status:
                constants.INTER_WALLET_TRANSACTION_STATUS.REFUND,
            },
          });

          await tx.tbl_wallets.update({
            where: {
              wallet_id: parseInt(userWalletID),
            },
            data: {
              balance: {
                increment: parseInt(interWalletTransaction.amount),
              },
            },
          });
          await tx.tbl_wallets.update({
            where: {
              wallet_id: constants.HOVER_WALLET_ID,
            },
            data: {
              balance: {
                decrement: parseInt(interWalletTransaction.amount),
              },
            },
          });
          if (planPurchaseReceiptID) {
            await tx.tbl_plan_purchase_receipts.update({
              where: {
                plan_purchase_receipt_id: planPurchaseReceiptID,
              },
              data: {
                plan_purchase_receipt_status:
                  constants.PLAN_PURCHASE_RECEIPT_STATUS.REFUND,
              },
            });
          }
          if (subscriptionPurchaseReceiptID) {
            await tx.tbl_subscription_purchase_receipts.update({
              where: {
                subscription_purchase_receipt_id: subscriptionPurchaseReceiptID,
              },
              data: {
                subscription_purchase_receipt_status:
                  constants.SUBSCRIPTION_PURCHASE_RECEIPT_STATUS.REFUND,
              },
            });
          }
        }
      );
      Logger.log("success", {
        message:
          "WalletService:revertOfferPurchasePaymentFromWallet:interWalletTransaction",
        params: {
          userWalletID,
          interWalletTransaction,
        },
      });

      return interWalletTransactionRevert;
    } catch (error) {
      Logger.log("error", {
        message: "WalletService:revertOfferPurchasePaymentFromWallet:catch-1",
        params: { error },
      });
      return null;
    }
  };

  /**
   *
   * @param {object} param0
   * @param {import("@prisma/client").tbl_users & {tbl_wallets:import("@prisma/client").tbl_wallets} & {tbl_user_types:import("@prisma/client").tbl_user_types & {tbl_security_deposit_types:import("@prisma/client").tbl_security_deposit_types}}} param0.user
   * @param {String} param0.paymentSource
   */
  static createRequiredWalletRechargeTransactionEntry = async ({
    user,
    amount,
  }) => {
    try {
      Logger.log("info", {
        message:
          "WalletService:createRequiredWalletRechargeTransactionEntry:params",
        params: {
          userID: user.user_id,
          amount,
        },
      });
      const newWalletRechargeTransaction =
        await prisma.tbl_wallet_recharge_transactions.create({
          data: {
            wallet_recharge_transaction_status:
              constants.WALLET_RECHARGE_TRANSACTION_STATUS.PENDING,
            wallet_id: user.wallet_id,
          },
        });
      Logger.log("success", {
        message:
          "WalletService:createRequiredWalletRechargeTransactionEntry:newWalletRechargeTransaction",
        params: { newWalletRechargeTransaction },
      });
      return {
        walletRechargeTransactionID:
          newWalletRechargeTransaction.wallet_recharge_transaction_id,
        amount,
      };
    } catch (error) {
      Logger.log("error", {
        message:
          "WalletService:createRequiredWalletRechargeTransactionEntry:catch-1",
        params: { error },
      });
      throw error;
    }
  };

  /**
   * @param {object} param0
   * @param {Number} param0.userID
   * @param {Number} param0.bookingID
   * @returns
   */
  static payBookingPaymentFromWallet = async ({ userID, bookingID }) => {
    try {
      Logger.log("info", {
        message: "WalletService:payBookingPaymentFromWallet:params",
        params: {
          userID,
          bookingID,
        },
      });
      const booking = await prisma.tbl_bookings.findFirst({
        where: {
          booking_id: parseInt(bookingID),
          user_id: parseInt(userID),
        },
        include: {
          tbl_booking_receipts: true,
          tbl_users: true,
        },
      });
      Logger.log("info", {
        message: "WalletService:payBookingPaymentFromWallet:booking",
        params: {
          userID,
          booking,
        },
      });
      if (booking && booking.booking_receipt_id) {
        const interWalletTransaction = await prisma.$transaction([
          prisma.tbl_wallets.update({
            where: {
              wallet_id: booking.tbl_users.wallet_id,
            },
            data: {
              balance: {
                decrement: parseInt(
                  booking.tbl_booking_receipts.final_booking_fare
                ),
              },
            },
          }),
          prisma.tbl_wallets.update({
            where: {
              wallet_id: constants.HOVER_WALLET_ID,
            },
            data: {
              balance: {
                increment: parseInt(
                  booking.tbl_booking_receipts.final_booking_fare
                ),
              },
            },
          }),

          prisma.tbl_booking_receipts.update({
            where: {
              booking_receipt_id: booking.booking_receipt_id,
            },
            data: {
              tbl_inter_wallet_transactions: {
                create: {
                  inter_wallet_transaction_message:
                    constants.PAYMENT_PURPOSE.BOOKING_PAYMENT,
                  inter_wallet_transaction_status:
                    constants.INTER_WALLET_TRANSACTION_STATUS.SUCCESS,
                  sender_wallet_id: booking.tbl_users.wallet_id,
                  receiver_wallet_id: constants.HOVER_WALLET_ID,
                  amount: parseInt(
                    booking.tbl_booking_receipts.final_booking_fare
                  ),
                },
              },
            },
          }),
        ]);
        Logger.log("success", {
          message:
            "WalletService:payPlanPaymentFromWallet:interWalletTransaction",
          params: { userID: userID, interWalletTransaction },
        });
        // walletSocketController.emitWalletOnUpdate({
        //   wallet: interWalletTransaction[1],
        //   firebaseID: user.firebase_id,
        // });
        return interWalletTransaction;
      } else {
        Logger.log("error", {
          message: "WalletService:payBookingPaymentFromWallet:catch-2",
          params: { error: constants.ERROR_CODES.INVALID_BOOKING_ID },
        });
        throw constants.ERROR_CODES.INVALID_BOOKING_ID;
      }
    } catch (error) {
      Logger.log("error", {
        message: "WalletService:payBookingPaymentFromWallet:catch-1",
        params: { error },
      });
      throw error;
    }
  };

  /**
   * @param {object} param0
   * @param {Number} param0.userID
   * @param {Number} param0.reservationID
   * @returns
   */
  static payReservationPaymentFromWallet = async ({
    userID,
    reservationID,
  }) => {
    try {
      Logger.log("info", {
        message: "WalletService:payReservationPaymentFromWallet:params",
        params: {
          userID,
          reservationID,
        },
      });
      const reservation = await prisma.tbl_reservations.findFirst({
        where: {
          reservation_id: parseInt(reservationID),
          user_id: parseInt(userID),
        },
        include: {
          tbl_reservation_receipts: true,
          tbl_users: true,
        },
      });
      Logger.log("info", {
        message: "WalletService:payReservationPaymentFromWallet:reservation",
        params: {
          userID,
          reservation,
        },
      });
      if (reservation && reservation.reservation_receipt_id) {
        const interWalletTransaction = await prisma.$transaction([
          prisma.tbl_wallets.update({
            where: {
              wallet_id: reservation.tbl_users.wallet_id,
            },
            data: {
              balance: {
                decrement: parseInt(
                  reservation.tbl_reservation_receipts.final_reservation_fare
                ),
              },
            },
          }),
          prisma.tbl_wallets.update({
            where: {
              wallet_id: constants.HOVER_WALLET_ID,
            },
            data: {
              balance: {
                increment: parseInt(
                  reservation.tbl_reservation_receipts.final_reservation_fare
                ),
              },
            },
          }),

          prisma.tbl_reservation_receipts.update({
            where: {
              reservation_receipt_id: reservation.reservation_receipt_id,
            },
            data: {
              tbl_inter_wallet_transactions: {
                create: {
                  inter_wallet_transaction_message:
                    constants.PAYMENT_PURPOSE.RESERVATION_PAYMENT,
                  inter_wallet_transaction_status:
                    constants.INTER_WALLET_TRANSACTION_STATUS.SUCCESS,
                  sender_wallet_id: reservation.tbl_users.wallet_id,
                  receiver_wallet_id: constants.HOVER_WALLET_ID,
                  amount: parseInt(
                    reservation.tbl_reservation_receipts.final_reservation_fare
                  ),
                },
              },
            },
          }),
        ]);
        Logger.log("success", {
          message:
            "WalletService:payPlanPaymentFromWallet:interWalletTransaction",
          params: { userID: userID, interWalletTransaction },
        });
        // walletSocketController.emitWalletOnUpdate({
        //   wallet: interWalletTransaction[1],
        //   firebaseID: user.firebase_id,
        // });
        return interWalletTransaction;
      } else {
        Logger.log("error", {
          message: "WalletService:payReservationPaymentFromWallet:catch-2",
          params: { error: constants.ERROR_CODES.INVALID_RESERVATION_ID },
        });
        throw constants.ERROR_CODES.INVALID_RESERVATION_ID;
      }
    } catch (error) {
      Logger.log("error", {
        message: "WalletService:payReservationPaymentFromWallet:catch-1",
        params: { error },
      });
      throw error;
    }
  };
}

module.exports = { WalletService };
