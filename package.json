{"name": "backend", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"start": "export NODE_ENV=production&& nodemon index.js", "dev-w": "set NODE_ENV=development&& nodemon index.js&& npm run ngrok-w", "ngrok-w": "ngrok-startup.bat", "dev": "export NODE_ENV=development&& nodemon index.js", "customer-sync": "set NODE_ENV=development&& cd scripts&& nodemon customerDBSync.js", "db-setup": "set NODE_ENV=development&& cd scripts&& node dbSetup.js", "serve": "set NODE_ENV=development&& node index.js", "migrate": "knex migrate:latest", "rollback": "knex migrate:rollback", "pm2": "export NODE_ENV=production&& pm2 start index.js", "pm2-restart": "export NODE_ENV=production&& pm2 restart index", "pm2-w": "set NODE_ENV=production&& pm2 start index.js", "prisma-studio": "export DATABASE_URL=postgresql://prisma_client_user:Yesthisisnewpassword@127.0.0.1/customer_db&& export NODE_ENV=production&& npx prisma studio", "prisma-studio-dev-w": "set DATABASE_URL=postgresql://prisma_client_user:<EMAIL>/customer_db&& npx prisma studio --port 8091", "prisma-db-pull": "export DATABASE_URL=postgresql://prisma_client_user:Yesthisisnewpassword@127.0.0.1/customer_db&& npx prisma db pull", "prisma-db-pull-dev": "export DATABASE_URL=postgresql://prisma_client_user:<EMAIL>/customer_db&& npx prisma db pull", "prisma-db-pull-dev-w": "set DATABASE_URL=postgresql://prisma_client_user:<EMAIL>/customer_db&& npx prisma db pull", "prisma-migrate-dev-w": "set DATABASE_URL=postgresql://prisma_client_user:<EMAIL>/customer_db&& npx prisma migrate dev"}, "author": "", "license": "ISC", "dependencies": {"@prisma/client": "^5.1.1", "@prisma/internals": "^5.3.1", "@slack/web-api": "^6.9.0", "axios": "^1.4.0", "convert-html-to-pdf": "^1.0.1", "cors": "^2.8.5", "dotenv": "^16.0.0", "express": "^4.17.2", "firebase-admin": "^12.0.0", "http-errors": "^2.0.0", "joi": "^17.13.3", "lodash": "^4.17.21", "moment": "^2.29.4", "mongoose": "^7.8.2", "morgan": "^1.10.0", "mqtt": "^4.3.7", "node-cron": "^3.0.0", "nodmeon": "^0.0.1-security", "pdfkit": "^0.13.0", "pg": "^8.7.3", "pm2": "^5.3.0", "point-in-polygon": "^1.1.0", "razorpay": "^2.8.6", "socket.io": "^4.5.3", "uuid": "^9.0.0", "validator": "^13.7.0", "winston": "^3.8.2", "winston-daily-rotate-file": "^4.7.1", "winston-slack-webhook-transport": "^2.2.3", "winston-syslog": "^2.7.0"}, "devDependencies": {"nodemon": "^2.0.22", "prisma": "^5.0.0", "prisma-json-schema-generator": "^5.1.1"}}