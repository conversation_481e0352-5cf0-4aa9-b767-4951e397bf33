var admin = require("firebase-admin");
const { prisma } = require("../../../config/prisma");

const constants = require("../../../constants");
const Logger = require("../../../utils/logger");
const { VehicleService } = require("../../services/vehicle.service");
const moment = require("moment");
const calculations = require("../../../utils/calculations");
const interceptorMiddleware = {};

/**
 *
 * @param {import("express").Request} req
 * @param {import("express").Response} res
 * @param {import("express").NextFunction} next
 * @returns
 */
interceptorMiddleware.provideVehicle = async (req, res, next) => {
  try {
    const { user, params, query, body } = req;
    const vehicleID = body
      ? body.vehicle_id
      : params
      ? params.vehicle_id
      : query
      ? query.vehicle_id
      : null;
    Logger.log("info", {
      message: "interceptorMiddleware:provideVehicle:params",
      params: {
        userID: user.user_id,
        vehicle: vehicleID,
      },
    });
    const vehicle = await VehicleService.getVehicleByID({
      vehicleID: vehicleID,
    });
    if (!vehicle) {
      Logger.log("error", {
        message: "interceptorMiddleware:provideVehicle:catch-1",
        params: {
          error: constants.ERROR_CODES.INVALID_VEHICLE_ID,
        },
      });
    } else {
      req.vehicle = vehicle;
      Logger.log("success", {
        message: "interceptorMiddleware:provideVehicle:success",
        params: {
          userID: user.user_id,
          vehicle: vehicle.vehicle_id,
        },
      });
    }
    return next();
  } catch (error) {
    Logger.log("error", {
      message: "interceptorMiddleware:provideVehicle:catch-1",
      params: { error },
    });
    return res.json({
      success: false,
      error: constants.ERROR_CODES.SERVER_ERROR,
    });
  }
};

/**
 *
 * @param {import("express").Request} req
 * @param {import("express").Response} res
 * @param {import("express").NextFunction} next
 * @returns
 */
interceptorMiddleware.provideUserFromURLToken = async (req, res, next) => {
  try {
    const { query } = req;
    const token = query.token;
    Logger.log("info", {
      message: "interceptorMiddleware:provideVehicle:params",
    });

    const decodedIdToken = await admin.auth().verifyIdToken(token);
    Logger.log("info", {
      message: "interceptorMiddleware:provideVehicle:success",
      params: { uid: decodedIdToken.uid, phone: decodedIdToken.phone_number },
    });
    const dbUser = await prisma.tbl_users.findFirst({
      where: { firebase_id: decodedIdToken.uid },
      include: {
        tbl_wallets: true,
        tbl_user_types: {
          include: {
            tbl_security_deposit_types: true,
          },
        },
        tbl_location_offers_map: true,
      },
    });
    Logger.log("info", {
      message: "interceptorMiddleware:provideVehicle:dbUser",
      params: { uid: decodedIdToken.uid, dbUser },
    });
    if (!dbUser) {
      Logger.log("success", {
        message: "interceptorMiddleware:provideVehicle:success",
        params: { uid: decodedIdToken.uid },
      });
      req.user = dbUser;
      req.firebaseUser = decodedIdToken;
      return next();
    } else if (
      (dbUser && dbUser.user_type_id == constants.USER_TYPE_IDS.MAINTAINER) ||
      (dbUser && dbUser.user_type_id == constants.USER_TYPE_IDS.ADMIN) ||
      dbUser.is_disabled
    ) {
      Logger.log("error", {
        message: "interceptorMiddleware:provideVehicle:catch-3",
        params: { error: constants.ERROR_CODES.INVALID_USER },
      });
      return res.send({
        error: constants.ERROR_CODES.INVALID_USER,
      });
    } else {
      Logger.log("success", {
        message: "interceptorMiddleware:provideVehicle:success",
        params: { uid: decodedIdToken.uid },
      });
      req.user = dbUser;
      req.firebaseUser = decodedIdToken;
      return next();
    }
  } catch (error) {
    Logger.log("error", {
      message: "interceptorMiddleware:provideVehicle:catch-1",
      params: { error },
    });
    return res.json({
      success: false,
      error: constants.ERROR_CODES.SERVER_ERROR,
    });
  }
};

/**
 *
 * @param {import("express").Request} req
 * @param {import("express").Response} res
 * @param {import("express").NextFunction} next
 * @returns
 */
interceptorMiddleware.provideIfCancelOrEndBooking = async (req, res, next) => {
  try {
    const { user, ongoingBooking } = req;
    Logger.log("info", {
      message: "interceptorMiddleware:provideIfCancelOrEndBooking:params",
      params: {
        userID: user.user_id,
        ongoingBookingID: ongoingBooking.booking_id,
      },
    });
    if (
      ongoingBooking &&
      moment(ongoingBooking.booking_start_at).add(60, "seconds").toDate() <
        new Date()
    ) {
      req.isEndRide = true;
    } else {
      req.isEndRide = false;
    }

    Logger.log("success", {
      message: "interceptorMiddleware:provideIfCancelOrEndBooking:success",
      params: {
        userID: user.user_id,
        ongoingBookingID: ongoingBooking.booking_id,
        isEndRide: req.isEndRide,
      },
    });
    return next();
  } catch (error) {
    Logger.log("error", {
      message: "interceptorMiddleware:provideIfCancelOrEndBooking:catch-1",
      params: { error },
    });
    return res.json({
      success: false,
      error: constants.ERROR_CODES.SERVER_ERROR,
    });
  }
};

/**
 *
 * @param {import("express").Request} req
 * @param {import("express").Response} res
 * @param {import("express").NextFunction} next
 * @returns
 */
interceptorMiddleware.provideVehicleOutstationStatus = async (
  req,
  res,
  next
) => {
  try {
    const { user, ongoingBooking, body } = req;
    const { outstationConsent } = req.body;
    Logger.log("info", {
      message: "interceptorMiddleware:provideVehicleOutstationStatus:params",
      params: { user, ongoingBooking, outstationConsent, body },
    });
    const vehicle = await prisma.tbl_vehicles.findUnique({
      where: {
        vehicle_id: parseInt(ongoingBooking.vehicle_id),
      },
    });
    const stations = await prisma.tbl_stations.findMany();

    const { minimunDistance, nearestStationID } = calculations.nearestStation(
      stations,
      body.lat && body.lng ? { lat: body.lat, lng: body.lng } : vehicle
    );
    Logger.log("info", {
      message:
        "interceptorMiddleware:provideVehicleOutstationStatus:nearestStation",
      params: { userID: user.user_id, minimunDistance, nearestStationID },
    });
    req.isOutstation = true;
    req.rideEndStationID = constants.OUTSTATION_ID;
    req.minimunDistance = minimunDistance;

    if (minimunDistance < constants.OUTSTATION_THRESHOLD) {
      req.isOutstation = false;
      req.rideEndStationID = nearestStationID;
      Logger.log("info", {
        message: "interceptorMiddleware:provideVehicleOutstationStatus:success",
        params: { userID: user.user_id, minimunDistance, nearestStationID },
      });
      return next();
    } else {
      req.isOutstation = true;
      req.rideEndStationID = constants.OUTSTATION_ID;
      Logger.log("info", {
        message: "interceptorMiddleware:provideVehicleOutstationStatus:success",
        params: { userID: user.user_id },
      });
      return next();
    }
  } catch (error) {
    Logger.log("error", {
      message: "interceptorMiddleware:provideVehicleOutstationStatus:catch-1",
      params: { error },
    });
    return res.json({
      success: false,
      error: constants.ERROR_CODES.SERVER_ERROR,
    });
  }
};

module.exports = { interceptorMiddleware };
