const Logger = require("../../../utils/logger");
const { extractError } = require("../../../utils/error.utils");
const { prisma } = require("../../../config/prisma");
const constants = require("../../../constants");

const stationController = {};

/**
 *
 * @param {import("express").Request} req
 * @param {import("express").Response} res
 * @returns
 */
stationController.getAllStations = async (req, res) => {
  try {
    const stations = await prisma.tbl_stations.findMany({
      where: {
        station_id: { not: parseInt(constants.OUTSTATION_ID) },
      },
    });
    return res.json({ success: true, stations });
  } catch (error) {
    Logger.log("error", {
      message: "stationController:getAllStations:catch-1",
      params: { error },
    });
    return res.json({ success: false, error: extractError(error) });
  }
};

/**
 *
 * @param {import("express").Request} req
 * @param {import("express").Response} res
 * @returns
 */
stationController.getNearbyStations = async (req, res) => {
  try {
    const { user } = req;
    const { lat, lng } = req.query;
    const final_lat = isNaN(lat) ? user.last_lat : lat;
    const final_lng = isNaN(lng) ? user.last_lng : lng;
    Logger.log("info", {
      message: "stationController:getNearbyStations",
      params: {
        userID: user.user_id,
        user_lat: user.last_lat,
        user_lng: user.last_lng,
        lat,
        lng,
        final_lat,
        final_lng,
      },
    });
    const stations = await prisma.tbl_stations.findMany({
      where: {
        AND: [
          {
            lat: {
              gte: parseFloat(final_lat) - constants.STATION_RENDER_RADIUS,
            },
          },
          {
            lat: {
              lte: parseFloat(final_lat) + constants.STATION_RENDER_RADIUS,
            },
          },
          {
            lng: {
              gte: parseFloat(final_lng) - constants.STATION_RENDER_RADIUS,
            },
          },
          {
            lng: {
              lte: parseFloat(final_lng) + constants.STATION_RENDER_RADIUS,
            },
          },
        ],
      },
    });
Logger.log("success", {
  message: "stationController:getNearbyStations:success",
  params: { stations },
});
    return res.json({
      success: true,
      stations,
    });
  } catch (error) {
    Logger.log("error", {
      message: "stationController:getNearbyStations:catch-1",
      params: { error },
    });
    return res.json({ success: false, error: extractError(error) });
  }
};

/**
 *
 * @param {import("express").Request} req
 * @param {import("express").Response} res
 * @returns
 */
stationController.getStationById = async (req, res) => {
  try {
    const { id } = req.params;
    const station = await prisma.tbl_stations.findUnique({
      where: { station_id: parseInt(id) },
    });
    if (station === null || station === undefined || !station) {
      Logger.log("error", {
        message: "stationController:getStationById:catch-1",
        params: { error: constants.ERROR_CODES.INVALID_STATION_ID },
      });
      return res.json({
        success: false,
        error: constants.ERROR_CODES.INVALID_STATION_ID,
      });
    }
    Logger.log("error", {
      message: "stationController:getStationById:success",
      params: { station: station.station_id },
    });
    return res.json({ success: true, station });
  } catch (error) {
    Logger.log("error", {
      message: "stationController:getStationById:catch-1",
      params: { error },
    });
    return res.json({ success: false, error: extractError(error) });
  }
};

/**
 *
 * @param {import("express").Request} req
 * @param {import("express").Response} res
 * @returns
 */
stationController.getStationMachines = async (req, res) => {
  try {
    const { id } = req.params;
    const machines = await prisma.tbl_machines.findMany({
      where: { station_id: parseInt(id) },
      include: { tbl_machine_types: true },
    });
    Logger.log("error", {
      message: "stationController:getStationMachines:success",
      params: { machines },
    });
    return res.json({ success: true, machines });
  } catch (error) {
    Logger.log("error", {
      message: "stationController:getStationMachines:catch-1",
      params: { error },
    });
    return res.json({ success: false, error: extractError(error) });
  }
};

module.exports = stationController;
