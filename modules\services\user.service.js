const { prisma } = require("../../config/prisma");
const constants = require("../../constants");
const pointInPolygon = require("point-in-polygon");
const Logger = require("../../utils/logger");
const { isCoordinateInsidePolygon } = require("../../utils/geo.utils");
const { default: axios } = require("axios");
const { KYC_URL, KYC_API_TOKEN, KYC_OTP_URL } = require("../../environment");

class UserService {
  constructor() {}

  /**
   *
   * @param {import("@prisma/client").tbl_users} user
   * @returns
   */
  static updateUserLastSeen = async(user) =>{
    try{
      const updatedUser = await prisma.tbl_users.update({
        where: {
          user_id: user.user_id,
        },
        data: {
          last_seen: new Date(),
        },
      });
      Logger.log("success", {
        message: "UserService:updateUserLastSeen:success",
        params: { userID: user.user_id,last_seen: updatedUser.last_seen },
      });
      return true;
    }
    catch(error){
      Logger.log("error", {
        message: "UserService:updateUserLastSeen:catch-1",
        params: { error },
      });
      return false;
    }
  }

  /**
   *
   * @param {import("@prisma/client").tbl_users} user
   * @returns
   */
  static isWalletActivated = (user) => {
    if (
      user &&
      user.tbl_wallets &&
      user.wallet_id &&
      !user.tbl_wallets.is_disabled &&
      user.razorpay_customer_id
    ) {
      return true;
    } else {
      return false;
    }
  };

  /**
   *
   * @param {import("@prisma/client").tbl_users & {tbl_location_offers_map:import("@prisma/client").tbl_location_offers_map}} user
   */
  static getAllocatedReservationTypes = (user) => {
    try {
      return user.tbl_location_offers_map.reservation_type_ids;
    } catch (error) {
      return [];
    }
  };

  /**
   *
   * @param {import("@prisma/client").tbl_users & {tbl_location_offers_map:import("@prisma/client").tbl_location_offers_map}} user
   */
  static getAllocatedSubscriptionTypes = (user) => {
    try {
      return user.tbl_location_offers_map.subscription_type_ids;
    } catch (error) {
      return [];
    }
  };

  /**
   *
   * @param {import("@prisma/client").tbl_users & {tbl_location_offers_map:import("@prisma/client").tbl_location_offers_map}} user
   */
  static getAllocatedPlanTypes = (user) => {
    try {
      return user.tbl_location_offers_map.plan_type_ids;
    } catch (error) {
      return [];
    }
  };

  /**
   *
   * @param {object} param0
   * @param {import("@prisma/client").tbl_users} param0.user
   * @param {Number} param0.lat
   * @param {Number} param0.lat
   */
  static updateUserLocationData = async ({ user, lat, lng }) => {
    try {
      const userLocationMatrix = {
        lat: parseFloat(Number(lat).toFixed(8)),
        lng: parseFloat(Number(lng).toFixed(8)),
      };
      Logger.log("info", {
        message: "UserService:updateUserLocationData:params",
        params: { userID: user.user_id, userLocationMatrix },
      });
      let allocatedLocationOfferMapID = null;
      const locationOffersMaps =
        await prisma.tbl_location_offers_map.findMany();
      locationOffersMaps.forEach((locationOffersMap) => {
        let locationPolygon = [
          [
            parseFloat(Number(locationOffersMap.lat_1).toFixed(8)),
            parseFloat(Number(locationOffersMap.lng_1).toFixed(8)),
          ],
          [
            parseFloat(Number(locationOffersMap.lat_2).toFixed(8)),
            parseFloat(Number(locationOffersMap.lng_2).toFixed(8)),
          ],
          [
            parseFloat(Number(locationOffersMap.lat_3).toFixed(8)),
            parseFloat(Number(locationOffersMap.lng_3).toFixed(8)),
          ],
          [
            parseFloat(Number(locationOffersMap.lat_4).toFixed(8)),
            parseFloat(Number(locationOffersMap.lng_4).toFixed(8)),
          ],
        ];

        let t = isCoordinateInsidePolygon(userLocationMatrix, locationPolygon);
        Logger.log("info", {
          message: "UserService:updateUserLocationData:t",
          params: { userID: user.user_id, t, locationPolygon },
        });
        if (t) {
          allocatedLocationOfferMapID =
            locationOffersMap.location_offers_map_id;
        }
      });
      Logger.log("info", {
        message:
          "UserService:updateUserLocationData:allocatedLocationOfferMapID",
        params: { userID: user.user_id, allocatedLocationOfferMapID },
      });
      const updatedUser = await prisma.tbl_users.update({
        where: {
          user_id: user.user_id,
        },
        data: allocatedLocationOfferMapID
          ? {
              last_lat: lat,
              last_lng: lng,
              location_offers_map_id: allocatedLocationOfferMapID,
            }
          : {
              last_lat: lat,
              last_lng: lng,
            },
        include: {
          tbl_location_offers_map: true,
        },
      });
      Logger.log("success", {
        message: "UserService:updateUserLocationData:success",
        params: { userID: user.user_id, allocatedLocationOfferMapID },
      });
      return updatedUser;
    } catch (error) {
      Logger.log("error", {
        message: "UserService:updateUserLocationData:catch-1",
        params: { error: error },
      });
      throw error;
    }
  };

  static generateFirebasePushNotification = async ({
    title,
    description,
    icon,
    userIDs,
  }) => {
    try {
      const registrationTokenRecords =
        await prisma.tbl_push_notification_tokens.findMany({
          where: {
            user_id: {
              in: userIDs,
            },
          },
        });

      const registrationTokens = registrationTokenRecords.map(
        (registrationTokenRecord) => {
          return String(registrationTokenRecord.push_notification_token);
        }
      );

      Logger.log("info", {
        message:
          "UserService:generateFirebasePushNotification:registrationTokens",
        params: { registrationTokens },
      });
      if (registrationTokens.length === 0) return null;
      return {
        data: {
          title: String(title),
          text: String(description),
          icon: icon ? icon : constants.PUSH_NOTIFICATIONS.ICON_URLS.DEFAULT,
        },
        tokens: registrationTokens,
      };
    } catch (error) {
      Logger.log("error", {
        message: "UserService:generateFirebasePushNotification:catch-1",
        params: { error },
      });
    }
  };

  static remindUsers = async () => {
    try {
      const reminders = await prisma.tbl_user_machine_reminder_map.findMany({
        where: {
          reminder_at: { lt: new Date() },
        },
      });
      Logger.log("success", {
        message: "UserService:remindUsers:reminders",
        params: { reminders },
      });
      await prisma.tbl_user_machine_reminder_map.deleteMany({
        where: {
          user_machine_reminder_map_id: {
            in: reminders.map(
              (reminder) => reminder.user_machine_reminder_map_id
            ),
          },
        },
      });
    } catch (error) {
      Logger.log("error", {
        message: "UserService:remindUsers:catch-1",
        params: { error },
      });
    }
  };

  /**
   *
   * @param {object} param0
   * @param {String} param0.aadhaarNumber
   */
  static getAadhaarVerificationOTP = async ({ aadhaarNumber }) => {
    try {
      Logger.log("info", {
        message: "UserService:getAadhaarVerificationOTP:init",
        params: { aadhaarNumber },
      });
      const response = await axios.post(
        KYC_URL,
        { aadhaarNumber: `${aadhaarNumber}` },
        { headers: { Authorization: KYC_API_TOKEN } }
      );

      if (response.data?.statusCode == 200) {
        Logger.log("success", {
          message: "UserService:getAadhaarVerificationOTP:response",
          params: { response: response.data },
        });
        return response.data.data.requestId;
      } else {
        Logger.log("error", {
          message: "UserService:getAadhaarVerificationOTP:catch-2",
          params: { error: response.data?.message },
        });
        throw constants.ERROR_CODES.KYC_FAILED;
      }
    } catch (error) {
      Logger.log("error", {
        message: "UserService:getAadhaarVerificationOTP:catch-1",
        params: { error },
      });
      throw error;
    }
  };

  /**
   *
   * @param {object} param0
   * @param {import("@prisma/client").tbl_users} param0.user
   * @param {String} param0.requestID
   * @param {String} param0.OTP
   */
  static verifyAadhaarOTP = async ({ user, requestID, OTP }) => {
    try {
      Logger.log("info", {
        message: "UserService:verifyAadhaarOTP:init",
        params: { userID: user.user_id },
      });
      const response = await axios.post(
        KYC_OTP_URL,
        { requestId: requestID, otp: `${OTP}` },
        { headers: { Authorization: KYC_API_TOKEN } }
      );

      // Logger.log("warning", {
      //   message: "UserService:verifyAadhaarOTP:response",
      //   params: {
      //     response: response.data,
      //     status: response.status,
      //     statusCode: response.data?.statusCode,
      //   },
      // });

      if (
        response.data?.statusCode == 200 ||
        (response.data?.statusCode == 422 &&
          response.data?.message == "OTP Already Submitted.")
      ) {
        Logger.log("success", {
          message: "UserService:verifyAadhaarOTP:success",
        });
        const kycUpdatedUser = await prisma.tbl_users.update({
          where: {
            user_id: user.user_id,
          },
          data: { aadhaar_kyc: true },
        });
        return true;
      } else {
        Logger.log("error", {
          message: "UserService:verifyAadhaarOTP:catch-2",
          params: { error: response.data?.message },
        });
        throw constants.ERROR_CODES.KYC_FAILED;
      }
    } catch (error) {
      Logger.log("error", {
        message: "UserService:verifyAadhaarOTP:catch-1",
        params: { error },
      });
      throw error;
    }
  };
}

module.exports = { UserService };
