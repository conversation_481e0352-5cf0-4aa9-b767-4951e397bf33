const express = require("express");
const router = express.Router();
const walletController = require("../controllers/wallet.controller");
const authMiddleware = require("../middlewares/auth.middleware");
const checkMiddleware = require("../middlewares/check.middleware");
const validationMiddleware = require("../middlewares/validation.middleware");

//user routes

//get user info : auth check
router.post(
  "/activate",
  authMiddleware,
  // checkMiddleware.isProfileCompleted,
  checkMiddleware.isWalletNotActivated,
  walletController.activateWallet
);
router.get(
  "/",
  authMiddleware,
  checkMiddleware.isWalletActivated,
  walletController.getWallet
);

router.post(
  "/security_deposit",
  authMiddleware,
  checkMiddleware.isWalletActivated,
  checkMiddleware.isDepositPaymentValid,
  walletController.paySecurityDeposit
);

router.post(
  "/recharge",
  authMiddleware,
  checkMiddleware.isWalletActivated,
  checkMiddleware.isSecurityDepositDone,
  walletController.rechargeWallet
);

router.post(
  "/booking_fare",
  authMiddleware,
  checkMiddleware.isWalletActivated,
  walletController.payBookingFare
);
router.post(
  "/reservation_fare",
  authMiddleware,
  checkMiddleware.isWalletActivated,
  walletController.payReservationFare
);
// router.post(
//   "/transactions",
//   authMiddleware,
//   checkMiddleware.isWalletActivated,
//   checkMiddleware.isOrderValid,
//   walletController.createTransaction
// );

// router.get("/orders", authMiddleware, walletController.paymentOrderHistory);
// router.get(
//   "/orders/:payment_order_id",
//   authMiddleware,
//   walletController.paymentOrderHistoryByID
// );
module.exports = router;
