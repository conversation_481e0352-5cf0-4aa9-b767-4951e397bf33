const Logger = require("../../../utils/logger");
const { extractError } = require("../../../utils/error.utils");
const constants = require("../../../constants");
const { ReservationService } = require("../../services/reservation.service");
const {
  reservationSocketController,
} = require("../../socket/controllers/reservation.socket.controller");

const reservationController = {};

/**
 *
 * @param {import("express").Request} req
 * @param {import("express").Response} res
 * @returns
 */
reservationController.ongoingReservation = async (req, res) => {
  try {
    const { user, ongoingReservation } = req;
    Logger.log("info", {
      message: "reservationController:ongoingReservation:params",
      params: { userID: user.user_id },
    });

    return res.json({ success: true, ongoingReservation });
  } catch (error) {
    Logger.log("error", {
      message: "reservationController:ongoingReservation:catch-1",
      params: { error },
    });
    return res.json({ success: false, error: extractError(error) });
  }
};

/**
 *
 * @param {import("express").Request} req
 * @param {import("express").Response} res
 * @returns
 */
reservationController.getCompletedReservations = async (req, res) => {
  try {
    const { user } = req;
    const { page } = req.query;
    Logger.log("info", {
      message: "reservationController:getCompletedReservations:params",
      params: { userID: user.user_id, page },
    });
    let skip = 0;
    let take = constants.RESERVATION_HISTORY_PAGE_SIZE;
    if (page) {
      skip = (parseInt(page) - 1) * constants.RESERVATION_HISTORY_PAGE_SIZE;
      take = constants.RESERVATION_HISTORY_PAGE_SIZE;
    }

    const reservations = await ReservationService.getCompletedUserReservations({
      user,
      skip,
      take,
    });

    Logger.log("success", {
      message: "reservationController:getCompletedReservations:reservations",
      params: { userID: user.user_id, reservations: reservations.length },
    });
    return res.json({
      success: true,
      reservations,
      nextPage:
        reservations.length < constants.RESERVATION_HISTORY_PAGE_SIZE
          ? undefined
          : parseInt(parseInt(page) + 1),
    });
  } catch (error) {
    Logger.log("error", {
      message: "reservationController:getCompletedReservations:catch-1",
      params: { error },
    });
    return res.json({ success: false, error: extractError(error) });
  }
};

/**
 *
 * @param {import("express").Request} req
 * @param {import("express").Response} res
 * @returns
 */
reservationController.getReservationByID = async (req, res) => {
  try {
    const { user } = req;
    const { id } = req.params;

    Logger.log("info", {
      message: "reservationController:getReservationByID:params",
      params: { userID: user.user_id },
    });

    const reservation = await ReservationService.getUserReservationByID({
      user,
      reservationID: parseInt(id),
    });

    Logger.log("success", {
      message: "reservationController:getReservationByID:reservations",
      params: {
        userID: user.user_id,
        reservationID: reservation.reservation_id,
      },
    });
    return res.json({
      success: true,
      reservation,
    });
  } catch (error) {
    Logger.log("error", {
      message: "reservationController:getReservationByID:catch-1",
      params: { error },
    });
    return res.json({ success: false, error: extractError(error) });
  }
};

/**
 *
 * @param {import("express").Request} req
 * @param {import("express").Response} res
 * @returns
 */
reservationController.reserveVehicle = async (req, res) => {
  try {
    const { user, vehicle } = req;
    const { lat, lng, reservation_type_id } = req.body;
    Logger.log("info", {
      message: "reservationController:reserveVehicle:params",
      params: {
        userID: user.user_id,
        vehicle: vehicle.vehicle_id,
        lat,
        lng,
        reservation_type_id,
      },
    });
    const reserveVehicleTransaction = await ReservationService.reserveVehicle({
      user,
      vehicle,
      reservationTypeID: reservation_type_id,
    });
    Logger.log("success", {
      message: "reservationController:reserveVehicle:success",
      params: { userID: user.user_id, reserveVehicleTransaction },
    });
    // reservationSocketController.emitOngoingReservationOnUpdate({
    //   ongoingReservation: reserveVehicleTransaction.reservation,
    //   firebaseID: user.firebase_id,
    // });
    return res.json({
      success: true,
      reservation: reserveVehicleTransaction.reservation,
    });
  } catch (error) {
    Logger.log("error", {
      message: "reservationController:reserveVehicle:catch-1",
      params: { error },
    });
    res.json({ success: false, error: extractError(error) });
  }
};

/**
 *
 * @param {import("express").Request} req
 * @param {import("express").Response} res
 * @returns
 */
reservationController.endReservation = async (req, res) => {
  try {
    const { user, ongoingReservation } = req;
    const { reservation_id } = req.body;
    Logger.log("info", {
      message: "reservationController:endReservation:params",
      params: {
        userID: user.user_id,
        ongoingReservationID: ongoingReservation.reservation_id,
      },
    });
    const endReservationTransaction = await ReservationService.endReservation({
      user,
      reservation: ongoingReservation,
    });
    Logger.log("success", {
      message: "reservationController:endReservation:success",
      params: { userID: user.user_id, endReservationTransaction },
    });
    // reservationSocketController.emitOngoingReservationOnUpdate({
    //   ongoingReservation: endReservationTransaction.reservation,
    //   firebaseID: user.firebase_id,
    // });
    return res.json({
      success: true,
      reservation: endReservationTransaction.reservation,
    });
  } catch (error) {
    Logger.log("error", {
      message: "reservationController:endReservation:catch-1",
      params: { error },
    });
    res.json({ success: false, error: extractError(error) });
  }
};

module.exports = { reservationController };
