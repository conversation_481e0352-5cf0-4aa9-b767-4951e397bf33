const Logger = require("../../../utils/logger");
const { extractError } = require("../../../utils/error.utils");
const { prisma } = require("../../../config/prisma");
const userSocketController = require("../../socket/controllers/user.socket.controller");
const constants = require("../../../constants");

const vehicleController = {};

/**
 *
 * @param {import("express").Request} req
 * @param {import("express").Response} res
 * @returns
 */
vehicleController.getNearbyVehicles = async (req, res) => {
  try {
    const { user } = req;
    const { lat, lng } = req.query;
    const final_lat = isNaN(lat) ? user.last_lat : lat;
    const final_lng = isNaN(lng) ? user.last_lng : lng;
    Logger.log("info", {
      message: "vehicleController:getNearbyVehicles",
      params: {
        userID: user.user_id,
        user_lat: user.last_lat,
        user_lng: user.last_lng,
        lat,
        lng,
        final_lat,
        final_lng,
      },
    });
    const vehicles = await prisma.tbl_vehicles.findMany({
      where: {
        AND: [
          {
            lat: {
              gte: parseFloat(final_lat) - constants.VEHICLE_RENDER_RADIUS,
            },
          },
          {
            lat: {
              lte: parseFloat(final_lat) + constants.VEHICLE_RENDER_RADIUS,
            },
          },
          {
            lng: {
              gte: parseFloat(final_lng) - constants.VEHICLE_RENDER_RADIUS,
            },
          },
          {
            lng: {
              lte: parseFloat(final_lng) + constants.VEHICLE_RENDER_RADIUS,
            },
          },
        ],
        is_disabled: false,
        vehicle_status: constants.VEHICLE_STATUS.READY,
      },
      include: {
        tbl_vehicle_types: true,
      },
    });

    return res.json({
      success: true,
      vehicles,
    });
  } catch (error) {
    Logger.log("error", {
      message: "vehicleController:getNearbyVehicles:catch-1",
      params: { error },
    });
    return res.json({ success: false, error: extractError(error) });
  }
};

/**
 *
 * @param {import("express").Request} req
 * @param {import("express").Response} res
 * @returns
 */
vehicleController.getVehicleByID = async (req, res) => {
  try {
    const { id } = req.params;
    const vehicle = await prisma.tbl_vehicles.findUnique({
      where: { vehicle_id: parseInt(id), is_disabled: false },
      include: {
        tbl_vehicle_types: true,
      },
    });
    if (vehicle === null || vehicle === undefined || !vehicle) {
      Logger.log("error", {
        message: "vehicleController:getVehicleByID:catch-1",
        params: { error: constants.ERROR_CODES.INVALID_VEHICLE_ID },
      });
      return res.json({
        success: false,
        error: constants.ERROR_CODES.INVALID_VEHICLE_ID,
      });
    }
    Logger.log("success", {
      message: "vehicleController:getVehicleByID:success",
      params: { vehicle: vehicle.vehicle_id },
    });
    return res.json({ success: true, vehicle });
  } catch (error) {
    Logger.log("error", {
      message: "vehicleController:getVehicleByID:catch-1",
      params: { error },
    });
    return res.json({ success: false, error: extractError(error) });
  }
};

/**
 *
 * @param {import("express").Request} req
 * @param {import("express").Response} res
 * @returns
 */
vehicleController.getVehicleByVehicleCode = async (req, res) => {
  try {
    const { vehicle_code } = req.params;
    Logger.log("info", {
      message: "vehicleController:getVehicleByVehicleCode:params",
      params: { vehicle_code },
    });
    const vehicle = await prisma.tbl_vehicles.findFirst({
      where: {
        vehicle_code: String(vehicle_code).toUpperCase(),
        is_disabled: false,
      },
      include: {
        tbl_vehicle_types: true,
      },
    });

    if (vehicle === null || vehicle === undefined || !vehicle) {
      Logger.log("error", {
        message: "vehicleController:getVehicleByVehicleCode:catch-1",
        params: { error: constants.ERROR_CODES.INVALID_VEHICLE_ID },
      });
      return res.json({
        success: false,
        error: constants.ERROR_CODES.INVALID_VEHICLE_ID,
      });
    }
    if (vehicle.vehicle_status != constants.VEHICLE_STATUS.READY) {
      Logger.log("error", {
        message: "vehicleController:getVehicleByVehicleCode:catch-2",
        params: { error: constants.ERROR_CODES.VEHICLE_NOT_AVAILABLE },
      });
      return res.json({
        success: false,
        error: constants.ERROR_CODES.VEHICLE_NOT_AVAILABLE,
      });
    }
    Logger.log("success", {
      message: "vehicleController:getVehicleByVehicleCode:success",
      params: { vehicle: vehicle.vehicle_id, vehicle_code },
    });
    return res.json({ success: true, vehicle });
  } catch (error) {
    Logger.log("error", {
      message: "vehicleController:getVehicleByVehicleCode:catch-1",
      params: { error },
    });
    return res.json({ success: false, error: extractError(error) });
  }
};

/**
 *
 * @param {import("express").Request} req
 * @param {import("express").Response} res
 * @returns
 */
vehicleController.getVehicleTypeForDelivery = async (req, res) => {
  try {
    const vehicleTypes = await prisma.tbl_vehicle_types.findMany({
      where: { is_deliverable: true, is_disabled: false },
    });
    if (vehicleTypes === null || vehicleTypes === undefined || !vehicleTypes) {
      Logger.log("error", {
        message: "vehicleController:getVehicleTypeForDelivery:catch-1",
        params: { error: constants.ERROR_CODES.VEHICLE_NOT_AVAILABLE },
      });
      return res.json({
        success: false,
        error: constants.ERROR_CODES.VEHICLE_NOT_AVAILABLE,
      });
    }
    Logger.log("success", {
      message: "vehicleController:getVehicleTypeForDelivery:success",
      params: { vehicleTypesLength: vehicleTypes.length },
    });
    return res.json({ success: true, vehicleTypes });
  } catch (error) {
    Logger.log("error", {
      message: "vehicleController:getVehicleTypeForDelivery:catch-1",
      params: { error },
    });
    return res.json({ success: false, error: extractError(error) });
  }
};

module.exports = vehicleController;
