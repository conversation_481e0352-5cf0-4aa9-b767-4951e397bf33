const mongoose = require("mongoose");
const { BikeStatusEnum } = require("./enums");
var Schema = mongoose.Schema;

var bikeSchema = new Schema({
  _id: {
    type: String,
    minlength: 6,
    maxlength: 6,
  },
  np: {
    type: String,
    required: [true, "Display id / number is required"],
    unique: true,
  },
  blockedAt: {
    type: Number,
  },
  make: {
    type: String,
    trim: true,
    required: [true, "Bike maker is required"],
    trim: true,
    minlength: 1,
    maxlength: 100,
  },
  model: {
    type: String,
    trim: true,
    required: [true, "Bike model is required"],
    trim: true,
    minlength: 1,
    maxlength: 100,
  },
  isDL: {
    type: Boolean,
    required: true,
    required: [true, "Bike isDL is required"],
  },
  topSpeed: {
    type: Number,
    validate: {
      validator: function (v) {
        return v >= 0 && v <= 500;
      },
      message: (props) => "Invalid top speed",
    },
  },
  active: {
    type: Boolean,
  },
  socketId: {
    type: String,
  },
  lastSeen: {
    type: Number,
  },
  status: {
    type: String,
    enum: [
      BikeStatusEnum.Booked,
      BikeStatusEnum.Busy,
      BikeStatusEnum.Idle,
      BikeStatusEnum.Locked,
      BikeStatusEnum.Blocked,
      BikeStatusEnum.Maintenance,
    ],
    required: [true, "Bike status is required/invalid"],
  },

  currentBooking: {
    type: String,
    ref: "Booking",
  },

  plans: {
    type: [{ type: String, ref: "Plan" }],
    default: ["01"],
  },

  estBattery: {
    type: Number,
    validate: {
      validator: function (v) {
        return v >= 0 && v <= 100;
      },
      message: (props) => "Invalid battery status",
    },
    default: 50,
    required: [true, "Battery status is required"],
  },
  estRange: {
    type: Number,
    validate: {
      validator: function (v) {
        return v >= 0 && v <= 500;
      },
      message: (props) => "Invalid range",
    },
    default: 50,
    required: [true, "Bike range is required"],
  },

  bikeImage: {
    type: String,
    required: ["true", "Bike image is required"],
  },
  parkedImage: {
    type: String,
    required: ["true", "Bike parked image is required"],
  },

  location: {
    type: {
      type: String,
      enum: ["Point"],
      default: "Point",
    },
    coordinates: {
      type: [Number],
      default: [79.0, 21.0], // [long,lat]
    },
  },
});

bikeSchema.index({ location: "2dsphere" });

const Bike = mongoose.model("Bike", bikeSchema);

function validateBikeId(bikeId) {
  // implement regex
  return bikeId && bikeId.trim().length === 6;
}

exports.Bike = Bike;
exports.validateBikeId = validateBikeId;
