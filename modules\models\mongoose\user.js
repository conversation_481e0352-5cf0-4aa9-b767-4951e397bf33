const Joi = require("joi");
const mongoose = require("mongoose");
const { PassEnum } = require("./enums");

const userSchema = new mongoose.Schema({
  _id: {
    type: String,
  },
  phone: {
    type: String,
    required: true,
    unique: true,
    minlength: 10,
    maxlength: 15,
  },
  pendingPayments: {
    type: [{ type: String, ref: "Booking" }],
  },
  deviceTokens: {
    type: [{ type: String }],
  },
  name: {
    type: String,
    required: true,
    minlength: 1,
    maxlength: 50,
  },
  lastLocation: {
    type: {
      type: String,
      enum: ["Point"],
      default: "Point",
    },
    coordinates: {
      type: [Number],
      // default: [79.0, 21.0], // [long,lat]
    },
  },
  email: {
    type: String,
    required: true,
    minlength: 5,
    maxlength: 255,
    unique: true,
  },
  active: {
    type: Boolean,
  },
  socketId: {
    type: String,
  },
  lastSeen: {
    type: Number,
  },
  pass: {
    type: String,
    enum: [PassEnum.Nill, PassEnum.LSS, PassEnum.HSS],
    default: PassEnum.LSS,
    required: [true, "Pass is required/invalid"],
  },
  isSd: {
    type: Boolean,
  },
  wallet: {
    type: Number,
  },
  prevWalletAmt: {
    type: Number,
  },
  isPlanSelected: {
    type: Boolean,
  },
  isSub: {
    type: Boolean,
  },
  isPack: {
    type: Boolean,
  },
  freeTime: {
    type: Number,
  },
  packageTime: {
    type: Number,
  },
  subsExpiry: {
    type: Number,
  },
  joinedAt: {
    type: Number,
  },
  indiStartedAt: {
    type: Number,
  },
  indiExpiry: {
    type: Number,
  },
});

const User = mongoose.model("User", userSchema);

function validateUser(user) {
  const schema = Joi.object({
    name: Joi.string().min(1).max(50).required(),
    email: Joi.string().min(5).max(255).required().email(),
    phone: Joi.string().min(10).max(15).required(),
    joinedAt: Joi.required(),
  });
  return schema.validate(user);
}

exports.User = User;
exports.validate = validateUser;
